<template>
	<view class="my-pending-audit-tasks">
		<!-- 任务列表 -->
		<view class="task-list">
			<view 
				v-for="record in taskList" 
				:key="record.id"
				class="task-item"
				@click="goToDetail(record)"
			>
				<!-- 任务头部 -->
				<view class="task-header">
					<view class="task-title">{{ record.taskTitle || '任务标题' }}</view>
					<view class="task-status" :class="'status-' + record.status">
						{{ getStatusText(record.status) }}
					</view>
				</view>
				
				<!-- 任务信息 -->
				<view class="task-info">
					<view class="info-row">
						<text class="info-label">接受数量:</text>
						<text class="info-value">{{ record.acceptCount }}</text>
					</view>
					<view class="info-row">
						<text class="info-label">单价:</text>
						<text class="info-value price">{{ record.unitPrice }}助力值</text>
					</view>
					<view class="info-row">
						<text class="info-label">总奖励:</text>
						<text class="info-value total">{{ record.totalReward }}助力值</text>
					</view>
				</view>
				
				<!-- 时间信息 -->
				<view class="time-info">
					<view class="time-item">
						<text class="time-label">提交时间:</text>
						<text class="time-value">{{ formatTime(record.submitTime) }}</text>
					</view>
					<view v-if="record.actualDeadline" class="time-item">
						<text class="time-label">实际截止:</text>
						<text class="time-value" :class="{ expired: isExpired(record.actualDeadline) }">
							{{ formatTime(record.actualDeadline) }}
						</text>
					</view>
				</view>
				
				<!-- 完成说明预览 -->
				<view v-if="record.submitContent" class="submit-preview">
					<text class="preview-label">完成说明:</text>
					<text class="preview-content">{{ record.submitContent.length > 50 ? record.submitContent.substring(0, 50) + '...' : record.submitContent }}</text>
				</view>
				
				<!-- 操作按钮 -->
				<view class="task-actions">
					<view class="action-btn primary" @click.stop="auditTask(record)">
						<text class="btn-text">审核</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view v-if="hasMore" class="load-more" @click="loadMore">
			<text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
		</view>
		
		<!-- 空状态 -->
		<view v-if="!loading && taskList.length === 0" class="empty-state">
			<uv-empty
				mode="list"
				:text="getEmptyText()"
				:margin-top="80"
				text-color="#999999"
				icon-color="#cccccc"
				:icon-size="100"
			></uv-empty>
		</view>
		
		<!-- 审核弹窗 -->
		<AuditModal 
			:value="showAuditModal"
			:record="currentRecord"
			@input="showAuditModal = $event"
			@confirm="onAuditConfirm"
		/>
	</view>
</template>

<script>
import AuditModal from './AuditModal.vue'

export default {
	name: 'MyPendingAuditTasks',
	components: {
		AuditModal
	},
	data() {
		return {
			taskList: [],
			loading: false,
			hasMore: true,
			pageNo: 1,
			pageSize: 10,
			currentStatus: '', // 当前筛选状态
			statusList: [
				{ label: '全部', value: '', count: 0 },
				{ label: '待审核', value: '2', count: 0 }
			],
			showAuditModal: false,
			currentRecord: null
		}
	},
	methods: {
		// 加载数据
		async loadData(refresh = true) {
			if (this.loading) return
			
			if (refresh) {
				this.pageNo = 1
				this.hasMore = true
			}
			
			this.loading = true
			
			try {
				const params = {
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				
				const res = await uni.http.get(uni.api.getPendingAuditTasks, params)
				
				if (res.data.success) {
					const newList = res.data.result.records || []
					
					if (refresh) {
						this.taskList = newList
					} else {
						this.taskList.push(...newList)
					}
					
					this.hasMore = newList.length >= this.pageSize
					this.pageNo++
					
					// 更新状态统计
					this.updateStatusCount(res.data.result.total || 0)
				}
			} catch (error) {
				console.error('加载待审核任务失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return
			this.loadData(false)
		},
		
		// 切换状态筛选
		switchStatus(status) {
			if (this.currentStatus === status) return
			
			this.currentStatus = status
			this.loadData()
		},
		
		// 更新状态统计
		updateStatusCount(total) {
			this.statusList.forEach(item => {
				if (item.value === '') {
					// 全部数量
					item.count = total
				} else if (item.value === '2') {
					// 待审核数量
					item.count = total
				}
			})
		},
		
		// 审核任务
		auditTask(record) {
			this.currentRecord = record
			this.showAuditModal = true
		},
		
		// 审核确认
		async onAuditConfirm(auditData) {
			try {
				const params = {
					recordId: this.currentRecord.id,
					auditResult: auditData.auditResult,
					auditReason: auditData.auditReason || '',
					auditOpinion: auditData.auditOpinion || ''
				}
				
				const res = await uni.http.post(uni.api.auditTask, params)
				
				if (res.data.success) {
					uni.showToast({
						title: '审核完成',
						icon: 'success'
					})
					
					// 刷新列表
					this.loadData()
				} else {
					uni.showToast({
						title: res.data.message || '审核失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('审核任务失败:', error)
				uni.showToast({
					title: '审核失败',
					icon: 'none'
				})
			}
		},
		
		// 跳转到详情页
		goToDetail(record) {
			uni.navigateTo({
				url: `/pages/task/detail?id=${record.taskId}&recordId=${record.id}`
			})
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'2': '待审核'
			}
			return statusMap[status] || '未知'
		},
		
		// 获取空状态文本
		getEmptyText() {
			return '暂无待审核的任务'
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''

			const date = new Date(time)
			const now = new Date()
			const diff = now.getTime() - date.getTime()
			const days = Math.floor(diff / (1000 * 60 * 60 * 24))

			if (days === 0) {
				return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
			} else if (days === 1) {
				return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
			} else if (days < 7) {
				return `${days}天前`
			} else {
				return date.toLocaleDateString('zh-CN')
			}
		},

		// 判断是否已过期
		isExpired(deadline) {
			if (!deadline) return false
			return new Date(deadline).getTime() < Date.now()
		}
	}
}
</script>

<style lang="scss" scoped>
.my-pending-audit-tasks {
	background-color: #f5f5f5;
}

.status-filter {
	background-color: white;
	padding: 8px 16px 10px 16px;
	display: flex;
	gap: 6px;
	border-bottom: 1px solid #f0f0f0;
	overflow-x: auto;

	.status-item {
		display: flex;
		align-items: center;
		gap: 3px;
		padding: 5px 10px;
		background-color: #f1f3f4;
		border-radius: 14px;
		white-space: nowrap;
		position: relative;
		min-height: 28px;
		transition: all 0.2s ease;

		.status-text {
			font-size: 13px;
			font-weight: 400;
			color: #666;
			transition: color 0.2s ease;
		}

		.status-count {
			background-color: #ff6b6b;
			color: white;
			font-size: 10px;
			font-weight: 500;
			padding: 1px 5px;
			border-radius: 8px;
			min-width: 14px;
			height: 16px;
			text-align: center;
			line-height: 14px;
		}

		&.active {
			background-color: #667eea;
			transform: translateY(-1px);
			box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);

			.status-text {
				color: white;
				font-weight: 500;
			}

			.status-count {
				background-color: rgba(255, 255, 255, 0.9);
				color: #667eea;
			}
		}
	}
}

.task-list {
	padding: 12px 16px;

	.task-item {
		background-color: white;
		border-radius: 12px;
		padding: 16px;
		margin-bottom: 12px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

		.task-header {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 12px;

			.task-title {
				flex: 1;
				font-size: 16px;
				font-weight: 600;
				color: #333;
				line-height: 1.4;
			}

			.task-status {
				padding: 4px 12px;
				border-radius: 12px;
				font-size: 12px;
				font-weight: 500;

				&.status-2 {
					background-color: #fff3e0;
					color: #f57c00;
				}
			}
		}

		.task-info {
			margin-bottom: 12px;

			.info-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 6px;

				&:last-child {
					margin-bottom: 0;
				}

				.info-label {
					font-size: 14px;
					color: #666;
				}

				.info-value {
					font-size: 14px;
					color: #333;
					font-weight: 500;

					&.price {
						color: #ff6b35;
					}

					&.total {
						color: #667eea;
						font-weight: 600;
					}
				}
			}
		}

		.time-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;

			.time-label {
				font-size: 12px;
				color: #999;
			}

			.time-value {
				font-size: 12px;
				color: #666;
			}
		}

		.submit-preview {
			margin-bottom: 12px;
			padding: 8px;
			background-color: #f8f9fa;
			border-radius: 6px;

			.preview-label {
				font-size: 12px;
				color: #666;
				margin-bottom: 4px;
			}

			.preview-content {
				font-size: 13px;
				color: #333;
				line-height: 1.4;
			}
		}

		.task-actions {
			display: flex;
			justify-content: flex-end;

			.action-btn {
				padding: 8px 20px;
				border-radius: 20px;

				.btn-text {
					font-size: 14px;
					font-weight: 500;
				}

				&.primary {
					background-color: #667eea;
					color: white;
				}
			}
		}
	}
}

.load-more {
	padding: 20px;
	text-align: center;

	.load-text {
		font-size: 14px;
		color: #999;
	}
}

.empty-state {
	padding: 20px;
}

/* 时间信息样式 */
.time-info {
	.time-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 4px;

		.time-value.expired {
			color: #ff4757;
			font-weight: 600;
		}
	}
}
</style>
