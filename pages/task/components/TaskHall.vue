<template>
	<view class="task-hall">
		<!-- 筛选栏 - 已隐藏 -->
		<view v-if="false" class="filter-bar">
			<view class="filter-item" @click="showSortModal = true">
				<text class="filter-text">{{ sortText }}</text>
				<text class="filter-icon">▼</text>
			</view>
			<view class="filter-item" @click="showStatusModal = true">
				<text class="filter-text">{{ statusText }}</text>
				<text class="filter-icon">▼</text>
			</view>
			<view class="filter-item" @click="showPriorityModal = true">
				<text class="filter-text">{{ priorityText }}</text>
				<text class="filter-icon">▼</text>
			</view>
		</view>
		
		<!-- 任务列表 -->
		<view class="task-list">
			<view 
				v-for="task in taskList" 
				:key="task.id"
				class="task-item"
				@click="goToDetail(task)"
			>
				<!-- 任务头部 -->
				<view class="task-header">
					<view class="task-title">{{ task.title }}</view>
					<view class="task-badges">
						<text v-if="task.isOfficial" class="official-badge">官方</text>
						<text v-if="task.priority > 0" class="priority-badge" :class="'priority-' + task.priority">
							{{ getPriorityText(task.priority) }}
						</text>
					</view>
				</view>
				
				<!-- 任务描述 -->
				<view class="task-desc">{{ task.description }}</view>
				
				<!-- 任务信息 -->
				<view class="task-info">
					<view class="info-item">
						<text class="info-label">任务单价</text>
						<text class="info-value price">{{ task.unitPrice }}</text>
						<text class="info-unit">助力值</text>
					</view>
					<view class="info-divider"></view>
					<view class="info-item">
						<text class="info-label">剩余名额</text>
						<text class="info-value">{{ task.remainingCount }}/{{ task.totalCount }}</text>
					</view>
				</view>
				
				<!-- 任务状态栏 -->
				<view class="task-status">
					<view class="status-item">
						<view class="status-icon publisher">{{ task.publisherType === '2' ? '🏢' : '👤' }}</view>
						<text class="status-text">{{ getPublisherTypeText(task.publisherType) }}</text>
					</view>
					<view class="status-item">
						<view class="status-icon deadline" :class="getDeadlineStatus(task)">⏰</view>
						<text class="status-text" :class="getDeadlineStatus(task)">{{ formatDeadline(task) }}</text>
						<text v-if="task.taskType === '2'" class="task-type-badge">长期</text>
					</view>
				</view>
				
				<!-- 任务底部 -->
				<view class="task-footer">
					<view class="task-progress">
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: getProgressPercent(task) + '%' }"></view>
						</view>
						<text class="progress-text">完成进度 {{ getProgressPercent(task) }}%</text>
					</view>
					<view class="task-actions">
						<view 
							class="accept-btn" 
							:class="{ disabled: task.remainingCount <= 0 }"
							@click.stop="acceptTask(task)"
						>
							<text class="btn-text">{{ task.remainingCount > 0 ? '接受任务' : '已满额' }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view v-if="hasMore" class="load-more" @click="loadMore">
			<text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
		</view>
		
		<!-- 空状态 -->
		<view v-if="!loading && taskList.length === 0" class="empty-state">
			<uv-empty
				mode="list"
				text="暂无任务"
				:margin-top="80"
				text-color="#999999"
				icon-color="#cccccc"
				:icon-size="100"
			></uv-empty>
		</view>
		
		<!-- 排序弹窗 -->
		<SortModal 
			v-model="showSortModal" 
			:current="sortType"
			@change="onSortChange"
		/>
		
		<!-- 状态筛选弹窗 -->
		<StatusModal 
			v-model="showStatusModal" 
			:current="statusFilter"
			@change="onStatusChange"
		/>
		
		<!-- 优先级筛选弹窗 -->
		<PriorityModal 
			v-model="showPriorityModal" 
			:current="priorityFilter"
			@change="onPriorityChange"
		/>
		
		<!-- 数量选择弹窗 -->
		<QuantityModal 
			v-model="showQuantityModal"
			:task="selectedTask"
			@confirm="onQuantityConfirm"
		/>
	</view>
</template>

<script>
import SortModal from './SortModal.vue'
import StatusModal from './StatusModal.vue'
import PriorityModal from './PriorityModal.vue'
import QuantityModal from './QuantityModal.vue'

export default {
	name: 'TaskHall',
	components: {
		SortModal,
		StatusModal,
		PriorityModal,
		QuantityModal
	},
	data() {
		return {
			taskList: [],
			loading: false,
			hasMore: true,
			pageNo: 1,
			pageSize: 10,
			
			// 筛选条件
			sortType: 'createTime', // createTime, unitPrice, deadline
			statusFilter: '', // 1-进行中
			priorityFilter: '', // 0-普通, 1-高, 2-紧急
			
			// 弹窗状态
			showSortModal: false,
			showStatusModal: false,
			showPriorityModal: false,
			showQuantityModal: false,
			selectedTask: null
		}
	},
	computed: {
		sortText() {
			const sortMap = {
				'createTime': '最新发布',
				'unitPrice': '价格排序',
				'deadline': '截止时间'
			}
			return sortMap[this.sortType] || '排序'
		},
		statusText() {
			return this.statusFilter ? '进行中' : '全部状态'
		},
		priorityText() {
			const priorityMap = {
				'0': '普通',
				'1': '高优先级',
				'2': '紧急'
			}
			return this.priorityFilter ? priorityMap[this.priorityFilter] : '全部优先级'
		}
	},
	methods: {
		// 加载数据
		async loadData(refresh = true) {
			if (this.loading) return
			
			if (refresh) {
				this.pageNo = 1
				this.hasMore = true
			}
			
			this.loading = true
			
			try {
				const params = {
					pageNo: this.pageNo,
					pageSize: this.pageSize,
					sortField: this.sortType,
					sortOrder: this.sortType === 'unitPrice' ? 'desc' : 'desc'
				}
				
				if (this.statusFilter) {
					params.status = this.statusFilter
				}
				if (this.priorityFilter) {
					params.priority = this.priorityFilter
				}
				
				const res = await uni.http.get(uni.api.getTaskList, params)
				
				if (res.data.success) {
					const newList = res.data.result.records || []
					
					if (refresh) {
						this.taskList = newList
					} else {
						this.taskList.push(...newList)
					}
					
					this.hasMore = newList.length >= this.pageSize
					this.pageNo++
				}
			} catch (error) {
				console.error('加载任务列表失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return
			this.loadData(false)
		},
		
		// 排序改变
		onSortChange(sortType) {
			this.sortType = sortType
			this.loadData()
		},
		
		// 状态筛选改变
		onStatusChange(status) {
			this.statusFilter = status
			this.loadData()
		},
		
		// 优先级筛选改变
		onPriorityChange(priority) {
			this.priorityFilter = priority
			this.loadData()

		},
		
		// 接受任务
		acceptTask(task) {
			if (task.remainingCount <= 0) {
				uni.showToast({
					title: '任务已满额',
					icon: 'none'
				})
				return
			}
			
			this.selectedTask = task
			this.showQuantityModal = true
		},
		
		// 确认接受数量
		async onQuantityConfirm(quantity) {
			if (!this.selectedTask) return

			try {
				const res = await uni.http.post(uni.api.acceptTask, {
					taskId: this.selectedTask.id,
					acceptCount: quantity
				})

				if (res.data.success) {
					uni.showToast({
						title: '接受成功',
						icon: 'success'
					})

					// 刷新列表
					this.loadData()
				} else {
					// 业务错误：显示后端返回的具体错误信息
					uni.showToast({
						title: res.data.message || '接受失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('接受任务失败:', error)
				// 网络错误或其他系统异常
				if (error && error.message) {
					// 如果error有具体的message，显示具体信息
					uni.showToast({
						title: error.message,
						icon: 'none'
					})
				} else {
					// 否则显示通用错误提示
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					})
				}
			}
		},
		
		// 跳转到详情页
		goToDetail(task) {
			uni.navigateTo({
				url: `/pages/task/detail?id=${task.id}`
			})
		},
		
		// 获取发布者类型文本
		getPublisherTypeText(type) {
			return type === '2' ? '官方发布' : '用户发布'
		},
		
		// 获取优先级文本
		getPriorityText(priority) {
			const map = { 0: '普通', 1: '高', 2: '紧急' }
			return map[priority] || '普通'
		},
		
		// 格式化截止时间（根据任务类型动态计算）
		formatDeadline(task) {
			const actualDeadline = this.calculateDisplayDeadline(task)
			const now = new Date()
			const diff = actualDeadline.getTime() - now.getTime()

			if (diff <= 0) {
				return '已过期'
			}

			const days = Math.floor(diff / (1000 * 60 * 60 * 24))
			const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

			if (days > 0) {
				return `${days}天后截止`
			} else if (hours > 0) {
				return `${hours}小时后截止`
			} else {
				return '即将截止'
			}
		},

		// 获取截止时间状态（根据任务类型动态计算）
		getDeadlineStatus(task) {
			const actualDeadline = this.calculateDisplayDeadline(task)
			const now = new Date()
			const diff = actualDeadline.getTime() - now.getTime()
			const hours = Math.floor(diff / (1000 * 60 * 60))

			if (diff <= 0) {
				return 'expired'
			} else if (hours <= 24) {
				return 'urgent'
			} else if (hours <= 72) {
				return 'warning'
			} else {
				return 'normal'
			}
		},

		// 计算显示用的截止时间
		calculateDisplayDeadline(task) {
			if (!task || !task.deadline) {
				return new Date()
			}

			// 短期任务或没有天数限制：直接使用原始deadline
			// 向后兼容：taskType为null/undefined时当作短期任务处理
			if (!task.taskType || task.taskType === '1' || !task.daysToComplete || task.daysToComplete <= 0) {
				return new Date(task.deadline)
			}

			// 长期任务：计算从当前时间开始的截止时间
			const now = new Date()
			const deadlineFromNow = new Date(now.getTime() + task.daysToComplete * 24 * 60 * 60 * 1000)
			const originalDeadline = new Date(task.deadline)

			// 返回较早的时间
			return originalDeadline < deadlineFromNow ? originalDeadline : deadlineFromNow
		},
		
		// 获取进度百分比
		getProgressPercent(task) {
			if (!task.totalCount || task.totalCount === 0) return 0
			// 使用后端返回的已完成数量而非接受数量
			const completed = task.completedCount || 0
			return Math.round((completed / task.totalCount) * 100)
		}
	}
}
</script>

<style lang="scss" scoped>
.task-hall {
	background-color: #f5f5f5;
}

.filter-bar {
	background-color: white;
	padding: 12px 16px;
	display: flex;
	gap: 16px;
	border-bottom: 1px solid #eee;
	
	.filter-item {
		display: flex;
		align-items: center;
		gap: 4px;
		padding: 6px 12px;
		background-color: #f8f9fa;
		border-radius: 16px;
		
		.filter-text {
			font-size: 14px;
			color: #333;
		}
		
		.filter-icon {
			font-size: 12px;
			color: #999;
		}
	}
}

.task-list {
	padding: 16px;
	
	.task-item {
		background-color: white;
		border-radius: 16px;
		padding: 16px;
		margin-bottom: 12px;
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
		border: 1px solid rgba(102, 126, 234, 0.08);
		position: relative;
		overflow: hidden;
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		box-sizing: border-box;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 3px;
			background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
		}
		
		&:active {
			transform: translateY(2px);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
		}
		
		.task-header {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 10px;
			
			.task-title {
				flex: 1;
				font-size: 17px;
				font-weight: 700;
				color: #1a1a1a;
				line-height: 1.4;
				margin-right: 12px;
			}
			
			.task-badges {
				display: flex;
				gap: 8px;
				flex-shrink: 0;
				
				.official-badge {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					font-size: 12px;
					font-weight: 600;
					padding: 4px 10px;
					border-radius: 12px;
					box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
				}
				
				.priority-badge {
					color: white;
					font-size: 12px;
					font-weight: 600;
					padding: 4px 10px;
					border-radius: 12px;
					
					&.priority-1 {
						background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
						box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
					}
					
					&.priority-2 {
						background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
						box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
					}
				}
			}
		}
		
		.task-desc {
			font-size: 14px;
			color: #666;
			line-height: 1.6;
			margin-bottom: 12px;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			overflow: hidden;
			display: -webkit-box;
		}
		
		.task-info {
			background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
			border-radius: 12px;
			padding: 12px;
			margin-bottom: 12px;
			border: 1px solid rgba(102, 126, 234, 0.1);
			
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.info-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				flex: 1;
				
				.info-label {
					font-size: 13px;
					color: #999;
					margin-bottom: 6px;
					font-weight: 500;
				}
				
				.info-value {
					font-size: 15px;
					color: #333;
					font-weight: 600;
					
					&.price {
						color: #ff5722;
						font-size: 20px;
						font-weight: 800;
						background: linear-gradient(135deg, #ff5722 0%, #e64a19 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						background-clip: text;
						text-shadow: 0 1px 2px rgba(255, 87, 34, 0.2);
					}
				}
				
				.info-unit {
					font-size: 12px;
					color: #ff5722;
					margin-top: 2px;
					font-weight: 600;
				}
			}
			
			.info-divider {
				width: 1px;
				height: 32px;
				background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.2), transparent);
				margin: 0 12px;
			}
		}
		
		.task-status {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
			padding: 6px 0;
			
			.status-item {
				display: flex;
				align-items: center;
				gap: 6px;
				
				.status-icon {
					font-size: 14px;
					width: 20px;
					height: 20px;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 10px;
					
					&.publisher {
						background: rgba(102, 126, 234, 0.1);
					}
					
					&.deadline {
						&.normal {
							background: rgba(76, 175, 80, 0.1);
						}
						&.warning {
							background: rgba(255, 152, 0, 0.1);
						}
						&.urgent {
							background: rgba(244, 67, 54, 0.1);
						}
						&.expired {
							background: rgba(156, 39, 176, 0.1);
						}
					}
				}
				
				.status-text {
					font-size: 13px;
					font-weight: 500;
					color: #666;
					
					&.normal {
						color: #4caf50;
					}
					&.warning {
						color: #ff9800;
					}
					&.urgent {
						color: #f44336;
					}
					&.expired {
						color: #9c27b0;
					}
				}
			}
		}
		
		.task-footer {
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.task-progress {
				flex: 1;
				margin-right: 16px;
				
				.progress-bar {
					width: 100%;
					height: 6px;
					background-color: #f0f0f0;
					border-radius: 3px;
					overflow: hidden;
					margin-bottom: 4px;
					
					.progress-fill {
						height: 100%;
						background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
						border-radius: 3px;
						transition: width 0.3s ease;
					}
				}
				
				.progress-text {
					font-size: 12px;
					color: #999;
					font-weight: 500;
				}
			}
			
			.task-actions {
				.accept-btn {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					padding: 8px 16px;
					border-radius: 18px;
					box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
					position: relative;
					overflow: hidden;
					transition: all 0.3s ease;
					
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -100%;
						width: 100%;
						height: 100%;
						background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
						transition: left 0.5s ease;
					}
					
					&:active:not(.disabled) {
						transform: scale(0.95);
						box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
						
						&::before {
							left: 100%;
						}
					}
					
					&.disabled {
						background: linear-gradient(135deg, #ccc 0%, #999 100%);
						box-shadow: none;
						cursor: not-allowed;
					}
					
					.btn-text {
						font-size: 14px;
						font-weight: 600;
						position: relative;
						z-index: 1;
					}
				}
			}
		}
	}
}

.load-more {
	padding: 24px 20px;
	text-align: center;
	
	.load-text {
		font-size: 15px;
		color: #999;
		font-weight: 500;
	}
}

.empty-state {
	padding: 20px;
}

// 添加骨架屏效果（为后续加载状态优化准备）
@keyframes shimmer {
	0% {
		background-position: -468px 0;
	}
	100% {
		background-position: 468px 0;
	}
}

.skeleton {
	animation: shimmer 1.2s ease-in-out infinite;
	background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
	background-size: 800px 104px;
}

// 响应式适配
@media screen and (max-width: 375px) {
	.task-list {
		padding: 12px;
		
		.task-item {
			padding: 14px;
			
			.task-header .task-title {
				font-size: 16px;
			}
			
			.task-info {
				padding: 10px;
				
				.info-item .info-value.price {
					font-size: 18px;
				}
				
				.info-divider {
					height: 28px;
					margin: 0 10px;
				}
			}
			
			.task-status {
				.status-item .status-text {
					font-size: 12px;
				}
			}
			
			.task-footer {
				.accept-btn {
					padding: 7px 14px;
					
					.btn-text {
						font-size: 13px;
					}
				}
			}
		}
	}
}

/* 长期任务标识样式 */
.task-type-badge {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 8px;
	margin-left: 6px;
	font-weight: 500;
}
</style>
