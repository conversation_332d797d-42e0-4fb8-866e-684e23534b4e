<template>
	<view class="my-accepted-tasks">
		<!-- 状态筛选 -->
		<view class="status-filter">
			<view 
				v-for="(status, index) in statusList" 
				:key="index"
				class="status-item"
				:class="{ active: currentStatus === status.value }"
				@click="switchStatus(status.value)"
			>
				<text class="status-text">{{ status.label }}</text>
				<view v-if="status.count > 0" class="status-count">{{ status.count }}</view>
			</view>
		</view>
		
		<!-- 任务列表 -->
		<view class="task-list">
			<view 
				v-for="record in taskList" 
				:key="record.id"
				class="task-item"
				@click="goToDetail(record)"
			>
				<!-- 任务头部 -->
				<view class="task-header">
					<view class="task-title">{{ record.taskTitle }}</view>
					<view class="task-badges">
						<text v-if="record.taskIsOfficial === 1" class="official-badge">官方</text>
						<text class="task-status-badge" :class="'status-' + record.status">
							{{ getStatusText(record.status) }}
						</text>
					</view>
				</view>
				
				<!-- 任务描述 -->
				<view v-if="record.taskDescription" class="task-desc">
					<text class="description-text">{{ record.taskDescription }}</text>
				</view>

				<!-- 任务信息卡片 -->
				<view class="task-info-card">
					<view class="info-item">
						<text class="info-label">接受数量</text>
						<text class="info-value">{{ record.acceptCount }}</text>
						<text class="info-unit">个</text>
					</view>
					<view class="info-divider"></view>
					<view class="info-item">
						<text class="info-label">单价</text>
						<text class="info-value price">{{ record.unitPrice }}</text>
						<text class="info-unit">助力值</text>
					</view>
					<view class="info-divider"></view>
					<view class="info-item">
						<text class="info-label">总奖励</text>
						<text class="info-value total">{{ record.totalReward }}</text>
						<text class="info-unit">助力值</text>
					</view>
				</view>

				<!-- 发布者信息 -->
				<view v-if="record.publisherNickname" class="publisher-section">
					<view class="publisher-row">
						<view class="publisher-avatar">
							<image
								v-if="record.publisherAvatar"
								:src="record.publisherAvatar"
								class="avatar-img"
								mode="aspectFill"
							></image>
							<view v-else class="avatar-placeholder">
								<text class="avatar-text">{{ record.publisherNickname.charAt(0) }}</text>
							</view>
						</view>
						<view class="publisher-details">
							<text class="publisher-name">{{ record.publisherNickname }}</text>
						</view>
					</view>
				</view>

				<!-- 时间信息 -->
				<view class="time-section">
					<view class="time-row">
						<view class="time-item">
							<view class="time-icon">📅</view>
							<text class="time-label">接受时间</text>
							<text class="time-value">{{ formatTime(record.acceptTime) }}</text>
						</view>
					</view>
					<view v-if="record.taskDeadline" class="time-row">
						<view class="time-item">
							<view class="time-icon deadline">⏰</view>
							<text class="time-label">截止时间</text>
							<text class="time-value">{{ formatDeadlineTime(record.taskDeadline) }}</text>
						</view>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="task-actions">
					<view v-if="record.status === '1'" class="action-btn primary" @click.stop="submitAudit(record)">
						<text class="btn-text">提交审核</text>
					</view>
					<!-- 取消按钮：仅在进行中状态且未过期时显示 -->
					<view
						v-if="record.status === '1' && !isTaskExpired(record)"
						class="action-btn cancel"
						:class="{ 'cancelling': cancellingTaskIds.has(record.id) }"
						@click.stop="cancelTask(record)"
					>
						<text class="btn-text">{{ cancellingTaskIds.has(record.id) ? '取消中...' : '取消' }}</text>
					</view>
					<view v-if="record.status === '4'" class="action-btn warning" @click.stop="resubmit(record)">
						<text class="btn-text">重新提交</text>
					</view>
					<view v-if="record.status === '2'" class="action-btn disabled">
						<text class="btn-text">审核中</text>
					</view>
					<view v-if="record.status === '3'" class="action-btn success">
						<text class="btn-text">已完成</text>
					</view>
					<view v-if="record.status === '5'" class="action-btn expired">
						<text class="btn-text">已过期</text>
					</view>
					<view v-if="record.status === '6'" class="action-btn cancelled">
						<text class="btn-text">已取消</text>
					</view>
				</view>
				
				<!-- 审核信息 -->
				<view v-if="record.auditReason && record.status === '4'" class="audit-section">
					<view class="audit-header">
						<view class="audit-icon">💬</view>
						<text class="audit-label">审核意见</text>
					</view>
					<text class="audit-reason">{{ record.auditReason }}</text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view v-if="hasMore" class="load-more" @click="loadMore">
			<text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
		</view>
		
		<!-- 空状态 -->
		<view v-if="!loading && taskList.length === 0" class="empty-state">
			<uv-empty
				mode="list"
				:text="getEmptyText()"
				:margin-top="80"
				text-color="#999999"
				icon-color="#cccccc"
				:icon-size="100"
			></uv-empty>
		</view>
	</view>
</template>

<script>
import TimeUtils from '@/utils/TimeUtils.js'

export default {
	name: 'MyAcceptedTasks',
	data() {
		return {
			taskList: [],
			loading: false,
			hasMore: true,
			pageNo: 1,
			pageSize: 10,
			currentStatus: '', // 当前筛选状态
			cancellingTaskIds: new Set(), // 正在取消的任务ID集合，防止重复操作
			statusList: [
				{ label: '全部', value: '', count: 0 },
				{ label: '进行中', value: '1', count: 0 },
				{ label: '待审核', value: '2', count: 0 },
				{ label: '已完成', value: '3', count: 0 },
				{ label: '未通过', value: '4', count: 0 },
				{ label: '已过期', value: '5', count: 0 },
				{ label: '已取消', value: '6', count: 0 }
			]
		}
	},
	methods: {
		// 加载数据
		async loadData(refresh = true) {
			if (this.loading) return
			
			if (refresh) {
				this.pageNo = 1
				this.hasMore = true
			}
			
			this.loading = true
			
			try {
				const params = {
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				
				if (this.currentStatus) {
					params.status = this.currentStatus
				}
				
				const res = await uni.http.get(uni.api.getMyAcceptedTasks, params)
				
				if (res.data.success) {
					const newList = res.data.result.records || []
					
					if (refresh) {
						this.taskList = newList
					} else {
						this.taskList.push(...newList)
					}
					
					this.hasMore = newList.length >= this.pageSize
					this.pageNo++
					
					// 更新状态统计
					this.updateStatusCount(res.data.result.statusCount || {})
				}
			} catch (error) {
				console.error('加载我接受的任务失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return
			this.loadData(false)
		},
		
		// 切换状态筛选
		switchStatus(status) {
			if (this.currentStatus === status) return
			
			this.currentStatus = status
			this.loadData()
		},
		
		// 更新状态统计
		updateStatusCount(statusCount) {
			this.statusList.forEach(item => {
				if (item.value === '') {
					// 全部数量
					item.count = Object.values(statusCount).reduce((sum, count) => sum + count, 0)
				} else {
					item.count = statusCount[item.value] || 0
				}
			})
		},
		
		// 提交审核
		submitAudit(record) {
			uni.navigateTo({
				url: `/pages/task/submit-audit?recordId=${record.id}`
			})
		},
		
		// 重新提交
		resubmit(record) {
			uni.navigateTo({
				url: `/pages/task/resubmit?recordId=${record.id}`
			})
		},
		
		// 跳转到详情页
		goToDetail(record) {
			uni.navigateTo({
				url: `/pages/task/detail?id=${record.taskId}&recordId=${record.id}`
			})
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'1': '进行中',
				'2': '待审核',
				'3': '已完成',
				'4': '未通过',
				'5': '已过期',
				'6': '已取消'
			}
			return statusMap[status] || '未知'
		},
		
		// 获取空状态文本
		getEmptyText() {
			if (this.currentStatus) {
				return `暂无${this.getStatusText(this.currentStatus)}的任务`
			}
			return '您还没有接受任何任务'
		},

		// 检测任务是否过期
		isTaskExpired(record) {
			if (!record || !record.actualDeadline) {
				return false
			}

			// 使用TimeUtils工具类检测实际截止时间是否过期
			return TimeUtils.isActualDeadlineExpired(record.actualDeadline)
		},

		// 取消任务
		async cancelTask(record) {
			// 0. 防止重复操作
			if (this.cancellingTaskIds.has(record.id)) {
				uni.showToast({
					title: '操作进行中，请稍候',
					icon: 'none',
					duration: 1500
				})
				return
			}

			// 1. 再次检查任务状态和过期状态
			if (record.status !== '1') {
				uni.showToast({
					title: '只有进行中的任务才能取消',
					icon: 'none',
					duration: 2000
				})
				return
			}

			if (this.isTaskExpired(record)) {
				uni.showToast({
					title: '任务已过期，无法取消',
					icon: 'none',
					duration: 2000
				})
				return
			}

			// 2. 显示确认弹窗
			const confirmResult = await new Promise((resolve) => {
				uni.showModal({
					title: '确认取消',
					content: `确定要取消任务"${record.taskTitle}"吗？\n\n取消后任务额度将退回，此操作不可撤销。`,
					confirmText: '确认取消',
					cancelText: '我再想想',
					confirmColor: '#ff4d4f',
					success: (res) => {
						resolve(res.confirm)
					},
					fail: () => {
						resolve(false)
					}
				})
			})

			if (!confirmResult) {
				return
			}

			// 3. 添加到正在操作的集合中
			this.cancellingTaskIds.add(record.id)

			// 4. 显示加载状态
			uni.showLoading({
				title: '取消中...',
				mask: true
			})

			try {
				// 5. 调用取消接口（遵循C端网络请求规范）
				const { data } = await uni.http.post(uni.api.cancelAcceptanceRecord + '/' + record.id)

				uni.hideLoading()

				if (data.success) {
					// 5. 取消成功，显示成功提示
					uni.showToast({
						title: '任务取消成功',
						icon: 'success',
						duration: 2000
					})

					// 6. 刷新任务列表
					await this.loadData(true)

					// 7. 触发父组件刷新（如果需要）
					this.$emit('refresh')

				} else {
					// 取消失败，显示错误信息
					uni.showToast({
						title: data.message || '取消失败，请重试',
						icon: 'none',
						duration: 3000
					})
				}

			} catch (error) {
				uni.hideLoading()

				// 处理网络错误或其他异常
				console.error('取消任务失败：', error)

				let errorMessage = '网络异常，请检查网络连接后重试'

				// 根据错误类型提供更具体的提示
				if (error.message) {
					if (error.message.includes('过期')) {
						errorMessage = '任务已过期，无法取消'
					} else if (error.message.includes('状态')) {
						errorMessage = '任务状态已变更，无法取消'
					} else {
						errorMessage = error.message
					}
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				})
			} finally {
				// 6. 清理操作状态
				this.cancellingTaskIds.delete(record.id)
			}
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''

			const date = new Date(time)
			const now = new Date()
			const diff = now.getTime() - date.getTime()
			const days = Math.floor(diff / (1000 * 60 * 60 * 24))

			if (days === 0) {
				return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
			} else if (days === 1) {
				return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
			} else if (days < 7) {
				return `${days}天前`
			} else {
				return date.toLocaleDateString('zh-CN')
			}
		},

		// 格式化截止时间（简化版本，直接显示原值）
		formatDeadlineTime(time) {
			if (!time) return ''
			const date = new Date(time)
			return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
		}
	}
}
</script>

<style lang="scss" scoped>
.my-accepted-tasks {
	background-color: #f8f9fa;
	min-height: 100vh;
}

.status-filter {
	background-color: white;
	padding: 8px 16px 10px 16px;
	display: flex;
	gap: 6px;
	border-bottom: 1px solid #f0f0f0;
	overflow-x: auto;

	.status-item {
		display: flex;
		align-items: center;
		gap: 3px;
		padding: 5px 10px;
		background-color: #f1f3f4;
		border-radius: 14px;
		white-space: nowrap;
		position: relative;
		min-height: 28px;
		transition: all 0.2s ease;

		.status-text {
			font-size: 13px;
			font-weight: 400;
			color: #666;
			transition: color 0.2s ease;
		}

		.status-count {
			background-color: #ff6b6b;
			color: white;
			font-size: 10px;
			font-weight: 500;
			padding: 1px 5px;
			border-radius: 8px;
			min-width: 14px;
			height: 16px;
			text-align: center;
			line-height: 14px;
		}

		&.active {
			background-color: #667eea;
			transform: translateY(-1px);
			box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);

			.status-text {
				color: white;
				font-weight: 500;
			}

			.status-count {
				background-color: rgba(255, 255, 255, 0.9);
				color: #667eea;
			}
		}
	}
}

.task-list {
	padding: 16px;
	
	.task-item {
		background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
		border-radius: 16px;
		padding: 14px;
		margin-bottom: 16px;
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
		border: 1px solid rgba(102, 126, 234, 0.08);
		position: relative;
		overflow: hidden;
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 3px;
			background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
		}
		
		&:active {
			transform: translateY(2px);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
		}
		
		.task-header {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 10px;
			
			.task-title {
				flex: 1;
				font-size: 17px;
				font-weight: 700;
				color: #1a1a1a;
				line-height: 1.4;
				margin-right: 12px;
			}
			
			.task-badges {
				display: flex;
				gap: 8px;
				flex-shrink: 0;
				
				.official-badge {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					font-size: 12px;
					font-weight: 600;
					padding: 4px 10px;
					border-radius: 12px;
					box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
				}
				
				.task-status-badge {
					color: white;
					font-size: 12px;
					font-weight: 600;
					padding: 4px 10px;
					border-radius: 12px;
					
					&.status-1 {
						background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
						box-shadow: 0 2px 6px rgba(25, 118, 210, 0.3);
					}
					
					&.status-2 {
						background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
						box-shadow: 0 2px 6px rgba(245, 124, 0, 0.3);
					}
					
					&.status-3 {
						background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
						box-shadow: 0 2px 6px rgba(56, 142, 60, 0.3);
					}
					
					&.status-4 {
						background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
						box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
					}
					
					&.status-5 {
						background: linear-gradient(135deg, #757575 0%, #616161 100%);
						box-shadow: 0 2px 6px rgba(117, 117, 117, 0.3);
					}

					&.status-6 {
						background: linear-gradient(135deg, #90a4ae 0%, #78909c 100%);
						box-shadow: 0 2px 6px rgba(144, 164, 174, 0.3);
					}
				}
			}
		}
		
		.task-desc {
			margin-bottom: 8px;

			.description-text {
				font-size: 14px;
				color: #666;
				line-height: 1.6;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
			}
		}

		.task-info-card {
			background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
			border-radius: 12px;
			padding: 12px;
			margin-bottom: 8px;
			border: 1px solid rgba(102, 126, 234, 0.1);
			
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.info-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				flex: 1;
				
				.info-label {
					font-size: 13px;
					color: #999;
					margin-bottom: 6px;
					font-weight: 500;
				}
				
				.info-value {
					font-size: 16px;
					color: #333;
					font-weight: 600;
					
					&.price {
						color: #ff5722;
						font-size: 18px;
						font-weight: 800;
						background: linear-gradient(135deg, #ff5722 0%, #e64a19 100%);
						-webkit-background-clip: text;
						-webkit-text-fill-color: transparent;
						background-clip: text;
					}
					
					&.total {
						color: #667eea;
						font-weight: 700;
						font-size: 16px;
					}
				}
				
				.info-unit {
					font-size: 12px;
					color: #ff5722;
					margin-top: 2px;
					font-weight: 600;
				}
			}
			
			.info-divider {
				width: 1px;
				height: 36px;
				background: linear-gradient(to bottom, transparent, rgba(102, 126, 234, 0.2), transparent);
				margin: 0 12px;
			}
		}

		.publisher-section {
			margin-bottom: 8px;

			.publisher-row {
				display: flex;
				align-items: center;
				gap: 10px;

				.publisher-avatar {
					width: 36px;
					height: 36px;
					border-radius: 18px;
					overflow: hidden;
					border: 2px solid rgba(102, 126, 234, 0.1);

					.avatar-img {
						width: 100%;
						height: 100%;
					}

					.avatar-placeholder {
						width: 100%;
						height: 100%;
						background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
						display: flex;
						align-items: center;
						justify-content: center;

						.avatar-text {
							color: white;
							font-size: 16px;
							font-weight: 700;
						}
					}
				}

				.publisher-details {
					flex: 1;

					.publisher-name {
						font-size: 15px;
						color: #333;
						font-weight: 600;
					}
				}
			}
		}

		.time-section {
			margin-bottom: 8px;

			.time-row {
				margin-bottom: 6px;

				&:last-child {
					margin-bottom: 0;
				}

				.time-item {
					display: flex;
					align-items: center;
					gap: 8px;

					.time-icon {
						font-size: 14px;
						width: 20px;
						height: 20px;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 10px;
						background: rgba(102, 126, 234, 0.1);

						&.deadline {
							background: rgba(255, 152, 0, 0.1);
						}
					}

					.time-label {
						font-size: 13px;
						color: #999;
						font-weight: 500;
						min-width: 60px;
					}

					.time-value {
						font-size: 13px;
						color: #666;
						font-weight: 500;
					}
				}
			}
		}
		
		.task-actions {
			display: flex;
			justify-content: flex-end;
			margin-top: 2px;
			
			.action-btn {
				padding: 10px 20px;
				border-radius: 20px;
				position: relative;
				overflow: hidden;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
				transition: all 0.3s ease;
				
				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
					transition: left 0.5s ease;
				}

				&:active:not(.disabled) {
					transform: scale(0.95);
					
					&::before {
						left: 100%;
					}
				}
				
				.btn-text {
					font-size: 14px;
					font-weight: 600;
					position: relative;
					z-index: 1;
				}
				
				&.primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
				}
				
				&.warning {
					background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
					color: white;
					box-shadow: 0 4px 12px rgba(255, 167, 38, 0.3);
				}
				
				&.success {
					background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
					color: white;
					box-shadow: 0 4px 12px rgba(102, 187, 106, 0.3);
				}
				
				&.disabled {
					background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
					color: #999;
					box-shadow: none;
					cursor: not-allowed;
				}
				
				&.expired {
					background: linear-gradient(135deg, #bdbdbd 0%, #9e9e9e 100%);
					color: white;
					box-shadow: 0 4px 12px rgba(189, 189, 189, 0.3);
				}

				&.cancel {
					background: linear-gradient(135deg, #ff7043 0%, #ff5722 100%);
					color: white;
					box-shadow: 0 4px 12px rgba(255, 112, 67, 0.3);

					&:active {
						background: linear-gradient(135deg, #e64a19 0%, #d84315 100%);
					}

					&.cancelling {
						background: linear-gradient(135deg, #bdbdbd 0%, #9e9e9e 100%);
						box-shadow: 0 4px 12px rgba(189, 189, 189, 0.3);
						pointer-events: none;

						.btn-text {
							opacity: 0.8;
						}
					}
				}

				&.cancelled {
					background: linear-gradient(135deg, #90a4ae 0%, #78909c 100%);
					color: white;
					box-shadow: 0 4px 12px rgba(144, 164, 174, 0.3);
				}
			}
		}
		
		.audit-section {
			margin-top: 8px;
			padding: 12px;
			background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
			border-radius: 12px;
			border-left: 4px solid #f44336;
			
			.audit-header {
				display: flex;
				align-items: center;
				gap: 8px;
				margin-bottom: 8px;

				.audit-icon {
					font-size: 16px;
				}

				.audit-label {
					font-size: 13px;
					color: #d32f2f;
					font-weight: 600;
				}
			}
			
			.audit-reason {
				font-size: 14px;
				color: #666;
				line-height: 1.5;
				padding-left: 24px;
			}
		}
	}
}

.load-more {
	padding: 24px 20px;
	text-align: center;
	
	.load-text {
		font-size: 15px;
		color: #999;
		font-weight: 500;
	}
}

.empty-state {
	padding: 20px;
}

// 响应式适配
@media screen and (max-width: 375px) {
	.task-list {
		padding: 12px;
		
		.task-item {
			padding: 12px;
			
			.task-header {
				margin-bottom: 8px;
				
				.task-title {
					font-size: 16px;
				}
			}
			
			.task-desc {
				margin-bottom: 6px;
			}
			
			.task-info-card {
				padding: 10px;
				margin-bottom: 6px;
				
				.info-item .info-value {
					font-size: 15px;
					
					&.price {
						font-size: 16px;
					}
				}
				
				.info-divider {
					height: 30px;
					margin: 0 8px;
				}
			}
			
			.publisher-section, .time-section {
				margin-bottom: 6px;
			}
			
			.task-actions .action-btn {
				padding: 8px 16px;
				
				.btn-text {
					font-size: 13px;
				}
			}
		}
	}
	
	.status-filter {
		padding: 6px 12px 8px 12px;
		gap: 5px;
		
		.status-item {
			padding: 4px 8px;
			min-height: 26px;
			
			.status-text {
				font-size: 12px;
			}
		}
	}
}
</style>
