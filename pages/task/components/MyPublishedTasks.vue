<template>
	<view class="my-published-tasks">
		<!-- 状态筛选 -->
		<view class="status-filter">
			<view 
				v-for="(status, index) in statusList" 
				:key="index"
				class="status-item"
				:class="{ active: currentStatus === status.value }"
				@click="switchStatus(status.value)"
			>
				<text class="status-text">{{ status.label }}</text>
				<view v-if="status.count > 0" class="status-count">{{ status.count }}</view>
			</view>
		</view>
		
		<!-- 任务列表 -->
		<view class="task-list">
			<view 
				v-for="task in taskList" 
				:key="task.id"
				class="task-item"
				@click="goToDetail(task)"
			>
				<!-- 任务头部 -->
				<view class="task-header">
					<view class="task-title">{{ task.title }}</view>
					<view class="task-status" :class="'status-' + task.status">
						{{ getStatusText(task.status) }}
					</view>
				</view>
				
				<!-- 任务描述 -->
				<view class="task-desc">{{ task.description }}</view>
				
				<!-- 任务进度 -->
				<view class="task-progress">
					<view class="progress-info">
						<text class="progress-text">完成进度: {{ task.completedCount || 0 }}/{{ task.totalCount }}</text>
						<text class="progress-percent">{{ getProgressPercent(task) }}%</text>
					</view>
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: getProgressPercent(task) + '%' }"></view>
					</view>
				</view>
				
				<!-- 财务信息 -->
				<view class="finance-info">
					<view class="finance-item">
						<text class="finance-label">任务总数量:</text>
						<text class="finance-value">{{ task.totalCount }}个</text>
					</view>
					<view class="finance-item">
						<text class="finance-label">任务单价:</text>
						<text class="finance-value price">{{ task.unitPrice }}助力值</text>
					</view>
					<view class="finance-item">
						<text class="finance-label">预扣总额:</text>
						<text class="finance-value">{{ task.deductedBalance }}助力值</text>
					</view>
					<view class="finance-item">
						<text class="finance-label">已发放:</text>
						<text class="finance-value paid">{{ task.paidBalance }}助力值</text>
					</view>
					<view class="finance-item">
						<text class="finance-label">可退回:</text>
						<text class="finance-value refund">{{ getRefundableAmount(task) }}助力值</text>
					</view>
				</view>
				
				<!-- 时间信息 -->
				<view class="time-info">
					<view class="time-item">
						<text class="time-label">发布时间:</text>
						<text class="time-value">{{ formatTime(task.createTime) }}</text>
					</view>
					<view class="time-item">
						<text class="time-label">截止时间:</text>
						<text class="time-value" :class="{ expired: isExpired(task) }">
							{{ getDisplayDeadline(task) }}
						</text>
						<text v-if="task.taskType === '2'" class="task-type-badge">长期</text>
					</view>
					<view v-if="task.taskType === '2' && task.daysToComplete" class="time-item">
						<text class="time-label">完成期限:</text>
						<text class="time-value">{{ task.daysToComplete }}天内</text>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="task-actions">
					<view v-if="task.status === '1'" class="action-btn secondary" @click.stop="editTask(task)">
						<text class="btn-text">编辑</text>
					</view>
					<!-- <view v-if="task.status === '1'" class="action-btn warning" @click.stop="cancelTask(task)">
						<text class="btn-text">取消</text>
					</view> -->
					<view v-if="task.status === '1' && task.remainingCount < task.totalCount" class="action-btn primary" @click.stop="viewAcceptors(task)">
						<text class="btn-text">查看接受者</text>
					</view>
					<view v-if="canRefund(task)" class="action-btn success" @click.stop="refundTask(task)">
						<text class="btn-text">申请退款</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view v-if="hasMore" class="load-more" @click="loadMore">
			<text class="load-text">{{ loading ? '加载中...' : '加载更多' }}</text>
		</view>
		
		<!-- 空状态 -->
		<view v-if="!loading && taskList.length === 0" class="empty-state">
			<uv-empty
				mode="list"
				:text="getEmptyText()"
				:margin-top="80"
				text-color="#999999"
				icon-color="#cccccc"
				:icon-size="100"
			></uv-empty>
		</view>
	</view>
</template>

<script>
export default {
	name: 'MyPublishedTasks',
	data() {
		return {
			taskList: [],
			loading: false,
			hasMore: true,
			pageNo: 1,
			pageSize: 10,
			currentStatus: '', // 当前筛选状态
			statusList: [
				{ label: '全部', value: '', count: 0 },
				{ label: '进行中', value: '1', count: 0 },
				{ label: '已满额', value: '2', count: 0 },
				{ label: '已完成', value: '3', count: 0 },
				{ label: '已过期', value: '4', count: 0 },
				{ label: '已下架', value: '5', count: 0 }
			]
		}
	},
	methods: {
		// 加载数据
		async loadData(refresh = true) {
			if (this.loading) return
			
			if (refresh) {
				this.pageNo = 1
				this.hasMore = true
			}
			
			this.loading = true
			
			try {
				const params = {
					pageNo: this.pageNo,
					pageSize: this.pageSize
				}
				
				if (this.currentStatus) {
					params.status = this.currentStatus
				}
				
				const res = await uni.http.get(uni.api.getMyPublishedTasks, params)
				
				if (res.data.success) {
					const newList = res.data.result.records || []
					
					if (refresh) {
						this.taskList = newList
					} else {
						this.taskList.push(...newList)
					}
					
					this.hasMore = newList.length >= this.pageSize
					this.pageNo++
					
					// 更新状态统计
					this.updateStatusCount(res.data.result.statusCount || {})
				}
			} catch (error) {
				console.error('加载我发布的任务失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 加载更多
		loadMore() {
			if (!this.hasMore || this.loading) return
			this.loadData(false)
		},
		
		// 切换状态筛选
		switchStatus(status) {
			if (this.currentStatus === status) return
			
			this.currentStatus = status
			this.loadData()
		},
		
		// 更新状态统计
		updateStatusCount(statusCount) {
			this.statusList.forEach(item => {
				if (item.value === '') {
					// 全部数量
					item.count = Object.values(statusCount).reduce((sum, count) => sum + count, 0)
				} else {
					item.count = statusCount[item.value] || 0
				}
			})
		},
		
		// 编辑任务
		editTask(task) {
			uni.navigateTo({
				url: `/pages/task/edit?id=${task.id}`
			})
		},
		
		// 取消任务
		async cancelTask(task) {
			const res = await uni.showModal({
				title: '确认取消',
				content: '取消后任务将下架，已接受的任务将失效，确认取消吗？'
			})
			
			if (!res.confirm) return
			
			try {
				const result = await uni.http.post(uni.api.cancelTask, {
					taskId: task.id
				})
				
				if (result.data.success) {
					uni.showToast({
						title: '取消成功',
						icon: 'success'
					})
					
					// 刷新列表
					this.loadData()
				} else {
					uni.showToast({
						title: result.data.message || '取消失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('取消任务失败:', error)
				uni.showToast({
					title: '取消失败',
					icon: 'none'
				})
			}
		},
		
		// 查看接受者
		viewAcceptors(task) {
			uni.navigateTo({
				url: `/pages/task/acceptors?taskId=${task.id}`
			})
		},
		
		// 申请退款
		async refundTask(task) {
			const refundAmount = this.getRefundableAmount(task)
			
			const res = await uni.showModal({
				title: '申请退款',
				content: `可退回${refundAmount}助力值，确认申请退款吗？`
			})
			
			if (!res.confirm) return
			
			try {
				const result = await uni.http.post(uni.api.refundTask, {
					taskId: task.id
				})
				
				if (result.data.success) {
					uni.showToast({
						title: '退款成功',
						icon: 'success'
					})
					
					// 刷新列表
					this.loadData()
				} else {
					uni.showToast({
						title: result.data.message || '退款失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('申请退款失败:', error)
				uni.showToast({
					title: '退款失败',
					icon: 'none'
				})
			}
		},
		
		// 跳转到详情页
		goToDetail(task) {
			uni.navigateTo({
				url: `/pages/task/detail?id=${task.id}&type=published`
			})
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'1': '进行中',
				'2': '已满额',
				'3': '已完成',
				'4': '已过期',
				'5': '已下架'
			}
			return statusMap[status] || '未知'
		},
		
		// 获取进度百分比
		getProgressPercent(task) {
			if (!task.totalCount || task.totalCount === 0) return 0
			// 使用后端返回的已完成数量而非接受数量
			const completed = task.completedCount || 0
			return Math.round((completed / task.totalCount) * 100)
		},
		
		// 获取可退回金额
		getRefundableAmount(task) {
			return Math.max(0, task.deductedBalance - task.paidBalance - (task.refundBalance || 0))
		},
		
		// 是否可以退款
		canRefund(task) {
			return ['3', '4', '5'].includes(task.status) && this.getRefundableAmount(task) > 0
		},
		
		// 是否已过期（根据任务类型判断）
		isExpired(task) {
			if (!task || !task.deadline) return false

			const actualDeadline = this.calculateDisplayDeadline(task)
			return actualDeadline.getTime() < Date.now()
		},
		
		// 获取空状态文本
		getEmptyText() {
			if (this.currentStatus) {
				return `暂无${this.getStatusText(this.currentStatus)}的任务`
			}
			return '您还没有发布任何任务'
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			
			const date = new Date(time)
			return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
		},
		
		// 获取显示用的截止时间（与TaskHall.vue保持一致）
		getDisplayDeadline(task) {
			if (!task || !task.deadline) return '无截止时间'

			const actualDeadline = this.calculateDisplayDeadline(task)
			return actualDeadline.toLocaleDateString('zh-CN') + ' ' + actualDeadline.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
		},

		// 计算显示用的截止时间
		calculateDisplayDeadline(task) {
			if (!task || !task.deadline) {
				return new Date()
			}

			// 短期任务或没有天数限制：直接使用原始deadline
			// 向后兼容：taskType为null/undefined时当作短期任务处理
			if (!task.taskType || task.taskType === '1' || !task.daysToComplete || task.daysToComplete <= 0) {
				return new Date(task.deadline)
			}

			// 长期任务：计算从当前时间开始的截止时间
			const now = new Date()
			const deadlineFromNow = new Date(now.getTime() + task.daysToComplete * 24 * 60 * 60 * 1000)
			const originalDeadline = new Date(task.deadline)

			// 返回较早的时间
			return originalDeadline < deadlineFromNow ? originalDeadline : deadlineFromNow
		}
	}
}
</script>

<style lang="scss" scoped>
.my-published-tasks {
	background-color: #f5f5f5;
}

.status-filter {
	background-color: white;
	padding: 10px 16px 12px 16px;
	display: flex;
	gap: 8px;
	border-bottom: 1px solid #f0f0f0;
	overflow-x: auto;

	.status-item {
		display: flex;
		align-items: center;
		gap: 4px;
		padding: 6px 12px;
		background-color: #f8f9fa;
		border-radius: 16px;
		white-space: nowrap;
		position: relative;
		min-height: 32px;
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		cursor: pointer;
		border: 1px solid transparent;

		.status-text {
			font-size: 13px;
			font-weight: 500;
			color: #666;
			transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		}

		.status-count {
			background-color: #ff6b6b;
			color: white;
			font-size: 10px;
			font-weight: 600;
			padding: 2px 6px;
			border-radius: 10px;
			min-width: 16px;
			height: 18px;
			text-align: center;
			line-height: 14px;
			transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		}

		&:hover {
			background-color: #f0f2f5;
			transform: translateY(-1px);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		}

		&.active {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
			border: 1px solid rgba(102, 126, 234, 0.2);

			.status-text {
				color: white;
				font-weight: 600;
			}

			.status-count {
				background-color: rgba(255, 255, 255, 0.95);
				color: #667eea;
				font-weight: 700;
			}
		}
	}
}

.task-list {
	padding: 12px 16px;
	
	.task-item {
		background-color: white;
		border-radius: 16px;
		padding: 16px;
		margin-bottom: 12px;
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(102, 126, 234, 0.08);
		border: 1px solid rgba(102, 126, 234, 0.08);
		position: relative;
		overflow: hidden;
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 3px;
			background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
		}

		&:active {
			transform: translateY(2px);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
		}
		
		.task-header {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 10px;

			.task-title {
				flex: 1;
				font-size: 17px;
				font-weight: 700;
				color: #1a1a1a;
				line-height: 1.4;
				margin-right: 12px;
			}

			.task-status {
				padding: 5px 14px;
				border-radius: 14px;
				font-size: 12px;
				font-weight: 600;
				flex-shrink: 0;
				box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

				&.status-1 {
					background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
					color: white;
				}

				&.status-2 {
					background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
					color: white;
				}

				&.status-3 {
					background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
					color: white;
				}

				&.status-4 {
					background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
					color: white;
				}

				&.status-5 {
					background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
					color: white;
				}
			}
		}
		
		.task-desc {
			font-size: 14px;
			color: #666;
			line-height: 1.5;
			margin-bottom: 12px;
		}
		
		.task-progress {
			margin-bottom: 12px;
			
			.progress-info {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;
				
				.progress-text {
					font-size: 14px;
					color: #333;
				}
				
				.progress-percent {
					font-size: 14px;
					color: #667eea;
					font-weight: 600;
				}
			}
			
			.progress-bar {
				height: 6px;
				background-color: #f0f0f0;
				border-radius: 3px;
				overflow: hidden;
				
				.progress-fill {
					height: 100%;
					background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
					transition: width 0.3s ease;
				}
			}
		}
		
		.finance-info {
			margin-bottom: 14px;
			background: rgba(102, 126, 234, 0.02);
			padding: 12px;
			border-radius: 12px;
			border: 1px solid rgba(102, 126, 234, 0.08);

			.finance-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;

				&:last-child {
					margin-bottom: 0;
				}

				.finance-label {
					font-size: 13px;
					color: #666;
					font-weight: 500;
				}

				.finance-value {
					font-size: 14px;
					color: #333;
					font-weight: 600;

					&.price {
						color: #667eea;
						font-weight: 700;
						font-size: 15px;
					}

					&.paid {
						color: #4caf50;
						font-weight: 600;
					}

					&.refund {
						color: #ff9800;
						font-weight: 600;
					}
				}
			}
		}
		
		.time-info {
			margin-bottom: 14px;

			.time-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;
				padding: 6px 0;

				&:last-child {
					margin-bottom: 0;
				}

				.time-label {
					font-size: 13px;
					color: #888;
					font-weight: 500;
				}

				.time-value {
					font-size: 13px;
					color: #555;
					font-weight: 600;

					&.expired {
						color: #f44336;
						font-weight: 700;
						background: rgba(244, 67, 54, 0.1);
						padding: 2px 8px;
						border-radius: 8px;
					}
				}
			}
		}
		
		.task-actions {
			display: flex;
			justify-content: flex-end;
			gap: 10px;
			margin-top: 4px;

			.action-btn {
				padding: 8px 18px;
				border-radius: 12px;
				transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
				cursor: pointer;
				border: none;

				.btn-text {
					font-size: 13px;
					font-weight: 600;
				}

				&:active {
					transform: translateY(1px);
				}

				&.primary {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
					color: white;
					box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);

					&:hover {
						box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
						transform: translateY(-1px);
					}
				}

				&.secondary {
					background-color: #f8f9fa;
					color: #555;
					border: 1px solid rgba(102, 126, 234, 0.2);

					&:hover {
						background-color: #e9ecef;
						border-color: rgba(102, 126, 234, 0.3);
					}
				}

				&.warning {
					background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
					color: white;
					box-shadow: 0 3px 8px rgba(255, 152, 0, 0.3);
				}

				&.success {
					background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
					color: white;
					box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
				}
			}
		}
	}
}

.load-more {
	padding: 20px;
	text-align: center;
	
	.load-text {
		font-size: 14px;
		color: #999;
	}
}

.empty-state {
	padding: 20px;
}

/* 长期任务相关样式 */
.task-type-badge {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	font-size: 10px;
	padding: 3px 8px;
	border-radius: 10px;
	margin-left: 8px;
	font-weight: 600;
	box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
	animation: pulse 2s infinite;
}

.time-item.highlight {
	background: rgba(102, 126, 234, 0.1);
	padding: 6px 10px;
	border-radius: 8px;
	margin-top: 4px;
	border: 1px solid rgba(102, 126, 234, 0.2);

	.time-label {
		color: #667eea;
		font-weight: 600;
	}

	.time-value {
		color: #667eea;
		font-weight: 600;
	}
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
	.task-list {
		padding: 10px 12px;

		.task-item {
			padding: 14px;

			.task-header .task-title {
				font-size: 16px;
			}

			.finance-info {
				padding: 10px;
			}

			.task-actions {
				gap: 8px;

				.action-btn {
					padding: 6px 14px;

					.btn-text {
						font-size: 12px;
					}
				}
			}
		}
	}
}

/* 动画效果 */
@keyframes pulse {
	0%, 100% {
		opacity: 1;
	}
	50% {
		opacity: 0.8;
	}
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 卡片进入动画 */
.task-item {
	animation: fadeInUp 0.5s ease-out;
}

/* 加载更多按钮优化 */
.load-more {
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}
}
</style>
