<template>
	<view v-if="visible" class="audit-modal-overlay" @click="handleCancel">
		<view class="audit-modal" @click.stop>
			<!-- 弹窗头部 -->
			<view class="modal-header">
				<text class="modal-title">任务审核</text>
				<view class="close-btn" @click="handleCancel">
					<text class="close-icon">×</text>
				</view>
			</view>
			
			<!-- 任务信息 -->
			<view v-if="record" class="task-info">
				<view class="info-item">
					<text class="info-label">任务标题:</text>
					<text class="info-value">{{ record.taskTitle || '任务标题' }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">接受数量:</text>
					<text class="info-value">{{ record.acceptCount }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">总奖励:</text>
					<text class="info-value">{{ record.totalReward }}助力值</text>
				</view>
				<view class="info-item">
					<text class="info-label">提交时间:</text>
					<text class="info-value">{{ formatTime(record.submitTime) }}</text>
				</view>
			</view>
			
			<!-- 完成说明 -->
			<view v-if="record && record.submitContent" class="submit-section">
				<text class="section-title">完成说明</text>
				<view class="submit-content">
					<text class="content-text">{{ record.submitContent }}</text>
				</view>
			</view>
			
			<!-- 证明材料 -->
			<view v-if="record && record.submitImages" class="images-section">
				<text class="section-title">证明材料</text>
				<view class="images-grid">
					<view 
						v-for="(image, index) in imageList" 
						:key="index"
						class="image-item"
						@click="previewImage(image)"
					>
						<image :src="image" class="submit-image" mode="aspectFill" />
					</view>
				</view>
			</view>
			
			<!-- 审核选择 -->
			<view class="audit-section">
				<text class="section-title">审核结果</text>
				<view class="audit-options">
					<view 
						class="audit-option"
						:class="{ active: auditResult === true }"
						@click="selectAuditResult(true)"
					>
						<view class="option-icon">
							<text class="icon-text">✓</text>
						</view>
						<text class="option-text">通过</text>
					</view>
					<view 
						class="audit-option"
						:class="{ active: auditResult === false }"
						@click="selectAuditResult(false)"
					>
						<view class="option-icon">
							<text class="icon-text">×</text>
						</view>
						<text class="option-text">不通过</text>
					</view>
				</view>
			</view>
			
			<!-- 审核意见 -->
			<view class="opinion-section">
				<text class="section-title">{{ auditResult === false ? '不通过原因' : '审核意见' }}</text>
				<textarea 
					v-model="auditOpinion"
					class="opinion-input"
					:placeholder="auditResult === false ? '请输入不通过的原因...' : '请输入审核意见（可选）...'"
					:maxlength="200"
				/>
				<view class="char-count">{{ auditOpinion.length }}/200</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="modal-actions">
				<view class="action-btn cancel" @click="handleCancel">
					<text class="btn-text">取消</text>
				</view>
				<view 
					class="action-btn confirm"
					:class="{ disabled: auditResult === null || (auditResult === false && !auditOpinion.trim()) }"
					@click="handleConfirm"
				>
					<text class="btn-text">确认</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'AuditModal',
	props: {
		value: {
			type: Boolean,
			default: false
		},
		record: {
			type: Object,
			default: null
		}
	},
	data() {
		return {
			auditResult: null, // true-通过, false-不通过
			auditOpinion: ''
		}
	},
	computed: {
		visible() {
			return this.value
		},
		imageList() {
			if (!this.record || !this.record.submitImages) return []
			return this.record.submitImages.split(',').filter(url => url.trim())
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				// 弹窗打开时重置数据
				this.auditResult = null
				this.auditOpinion = ''
			}
		}
	},
	methods: {
		// 选择审核结果
		selectAuditResult(result) {
			this.auditResult = result
			// 如果选择通过，清空之前的不通过原因
			if (result === true) {
				this.auditOpinion = ''
			}
		},
		
		// 预览图片
		previewImage(imageUrl) {
			uni.previewImage({
				urls: this.imageList,
				current: imageUrl
			})
		},
		
		// 取消
		handleCancel() {
			this.$emit('input', false)
		},
		
		// 确认
		handleConfirm() {
			// 验证必填项
			if (this.auditResult === null) {
				uni.showToast({
					title: '请选择审核结果',
					icon: 'none'
				})
				return
			}
			
			if (this.auditResult === false && !this.auditOpinion.trim()) {
				uni.showToast({
					title: '请输入不通过原因',
					icon: 'none'
				})
				return
			}
			
			// 提交审核数据
			const auditData = {
				auditResult: this.auditResult,
				auditReason: this.auditResult === false ? this.auditOpinion.trim() : '',
				auditOpinion: this.auditResult === true ? this.auditOpinion.trim() : ''
			}
			
			this.$emit('confirm', auditData)
			this.$emit('input', false)
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			
			const date = new Date(time)
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.audit-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	padding: 20px;
}

.audit-modal {
	background-color: white;
	border-radius: 16px;
	width: 100%;
	max-width: 500px;
	max-height: 80vh;
	overflow-y: auto;
	
	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px 20px 16px 20px;
		border-bottom: 1px solid #f0f0f0;
		
		.modal-title {
			font-size: 18px;
			font-weight: 600;
			color: #333;
		}
		
		.close-btn {
			width: 32px;
			height: 32px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 50%;
			background-color: #f5f5f5;
			
			.close-icon {
				font-size: 20px;
				color: #666;
			}
		}
	}
	
	.task-info {
		padding: 16px 20px;
		border-bottom: 1px solid #f0f0f0;
		
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 8px;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.info-label {
				font-size: 14px;
				color: #666;
			}
			
			.info-value {
				font-size: 14px;
				color: #333;
				font-weight: 500;
			}
		}
	}
	
	.submit-section, .images-section, .audit-section, .opinion-section {
		padding: 16px 20px;
		border-bottom: 1px solid #f0f0f0;
		
		.section-title {
			font-size: 16px;
			font-weight: 600;
			color: #333;
			margin-bottom: 12px;
		}
	}
	
	.submit-content {
		background-color: #f8f9fa;
		border-radius: 8px;
		padding: 12px;
		
		.content-text {
			font-size: 14px;
			color: #333;
			line-height: 1.5;
		}
	}
	
	.images-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		
		.image-item {
			width: 80px;
			height: 80px;
			border-radius: 8px;
			overflow: hidden;
			border: 1px solid #e0e0e0;
			
			.submit-image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.audit-options {
		display: flex;
		gap: 16px;

		.audit-option {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 16px;
			border: 2px solid #e0e0e0;
			border-radius: 12px;
			transition: all 0.2s ease;

			.option-icon {
				width: 40px;
				height: 40px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 8px;
				background-color: #f5f5f5;

				.icon-text {
					font-size: 20px;
					font-weight: bold;
					color: #999;
				}
			}

			.option-text {
				font-size: 14px;
				color: #666;
				font-weight: 500;
			}

			&.active {
				border-color: #667eea;
				background-color: rgba(102, 126, 234, 0.05);

				.option-icon {
					background-color: #667eea;

					.icon-text {
						color: white;
					}
				}

				.option-text {
					color: #667eea;
				}
			}
		}
	}

	.opinion-input {
		width: 100%;
		min-height: 80px;
		padding: 12px;
		border: 1px solid #e0e0e0;
		border-radius: 8px;
		font-size: 14px;
		color: #333;
		background-color: #fafafa;
		resize: none;
	}

	.char-count {
		text-align: right;
		font-size: 12px;
		color: #999;
		margin-top: 4px;
	}

	.modal-actions {
		display: flex;
		gap: 12px;
		padding: 20px;

		.action-btn {
			flex: 1;
			height: 44px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 22px;

			.btn-text {
				font-size: 16px;
				font-weight: 500;
			}

			&.cancel {
				background-color: #f5f5f5;
				color: #666;
			}

			&.confirm {
				background-color: #667eea;
				color: white;

				&.disabled {
					background-color: #ccc;
					color: #999;
				}
			}
		}
	}
}
</style>
