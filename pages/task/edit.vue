<template>
	<view class="edit-task-container">
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading" />
		</view>
		
		<view v-else class="form-container">
			<!-- 任务标题 -->
			<view class="form-item">
				<view class="label">任务标题 <text class="required">*</text></view>
				<input 
					v-model="formData.title" 
					class="input" 
					placeholder="请输入任务标题"
					maxlength="50"
				/>
				<view class="char-count">{{ formData.title.length }}/50</view>
			</view>
			
			<!-- 任务描述 -->
			<view class="form-item">
				<view class="label">任务描述 <text class="required">*</text></view>
				<textarea 
					v-model="formData.description" 
					class="textarea" 
					placeholder="请详细描述任务内容和要求"
					maxlength="500"
				/>
				<view class="char-count">{{ formData.description.length }}/500</view>
			</view>
			
			<!-- 任务完成要求 -->
			<view class="form-item">
				<view class="label">任务完成要求 <text class="required">*</text></view>
				<textarea 
					v-model="formData.taskRequirements" 
					class="textarea" 
					placeholder="请详细说明任务完成的具体要求和标准"
					maxlength="500"
				/>
				<view class="char-count">{{ formData.taskRequirements.length }}/500</view>
			</view>
			
			<!-- 任务奖励 -->
			<view class="form-item">
				<view class="label">任务奖励 <text class="required">*</text></view>
				<view class="reward-input">
					<input 
						v-model="formData.reward" 
						class="input" 
						type="digit"
						placeholder="请输入奖励金额"
					/>
					<text class="unit">助力值</text>
				</view>
			</view>
			
			<!-- 任务数量 -->
			<view class="form-item">
				<view class="label">任务数量 <text class="required">*</text></view>
				<input 
					v-model="formData.totalCount" 
					class="input" 
					type="number"
					placeholder="请输入任务数量"
				/>
			</view>
		</view>
		
		<!-- 固定底部提交按钮 -->
		<view class="fixed-submit-container">
			<button 
				class="submit-btn" 
				:disabled="!canSubmit || submitting"
				@click="submitEdit"
			>
				{{ submitting ? '保存中...' : '保存修改' }}
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			taskId: '',
			loading: true,
			submitting: false,
			formData: {
				title: '',
				description: '',
				taskRequirements: '',
				reward: '',
				totalCount: ''
			}
		}
	},
	
	computed: {
		canSubmit() {
			return this.formData.title.trim() && 
				   this.formData.description.trim() && 
				   this.formData.taskRequirements.trim() &&
				   this.formData.reward && 
				   this.formData.totalCount
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.taskId = options.id
			this.loadTaskDetail()
		} else {
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	},
	
	methods: {
		// 加载任务详情
		async loadTaskDetail() {
			try {
				const res = await uni.http.get(uni.api.getTaskDetail.replace('{id}', this.taskId))
				
				if (res.data.success) {
					const task = res.data.result
					this.formData = {
						title: task.title,
						description: task.description,
						taskRequirements: task.taskRequirements || '',
						reward: task.unitPrice ? task.unitPrice.toString() : task.reward ? task.reward.toString() : '',
						totalCount: task.totalCount.toString()
					}
				} else {
					uni.showToast({
						title: res.data.message || '加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载任务详情失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 提交编辑
		async submitEdit() {
			if (!this.canSubmit) return
			
			// 验证数据
			const reward = parseFloat(this.formData.reward)
			const totalCount = parseInt(this.formData.totalCount)
			
			if (isNaN(reward) || reward <= 0) {
				uni.showToast({
					title: '请输入有效的奖励金额',
					icon: 'none'
				})
				return
			}
			
			if (isNaN(totalCount) || totalCount <= 0) {
				uni.showToast({
					title: '请输入有效的任务数量',
					icon: 'none'
				})
				return
			}
			
			this.submitting = true
			
			try {
				const submitData = {
					id: this.taskId,
					title: this.formData.title.trim(),
					description: this.formData.description.trim(),
					taskRequirements: this.formData.taskRequirements.trim(),
					unitPrice: reward,
					totalCount: totalCount
				}
				
				const res = await uni.http.post(uni.api.editTask, submitData)
				
				if (res.data.success) {
					uni.showToast({
						title: '修改成功',
						icon: 'success'
					})
					
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({
						title: res.data.message || '修改失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('修改任务失败:', error)
				uni.showToast({
					title: '修改失败',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.edit-task-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 200px;
}

.form-container {
	padding: 20rpx;
	padding-bottom: 180rpx; /* 为固定按钮留出空间 */
}

.form-item {
	background: white;
	margin-bottom: 20rpx;
	padding: 30rpx;
	border-radius: 12rpx;
	
	.label {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 20rpx;
		font-weight: 500;
		
		.required {
			color: #ff4757;
		}
	}
	
	.input, .textarea {
		width: 100%;
		font-size: 30rpx;
		color: #333;
		border: 1px solid #e8e8e8;
		border-radius: 8rpx;
		padding: 24rpx 20rpx;
		box-sizing: border-box; /* 确保padding不会撑大容器 */
		
		&::placeholder {
			color: #999;
		}
		
		&:focus {
			border-color: #667eea;
		}
	}
	
	.input {
		min-height: 88rpx; /* 设置输入框最小高度 */
	}
	
	.textarea {
		min-height: 120rpx;
	}
	
	/* 字符计数器样式 */
	.char-count {
		text-align: right;
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
	}
	
	.reward-input {
		display: flex;
		align-items: center;
		border: 1px solid #e8e8e8;
		border-radius: 8rpx;
		min-height: 88rpx; /* 与其他输入框保持一致的高度 */
		
		&:focus-within {
			border-color: #667eea;
		}
		
		.input {
			flex: 1;
			border: none;
			padding: 24rpx 20rpx;
			min-height: auto; /* 重置min-height，由容器控制 */
			
			&:focus {
				border: none;
			}
		}
		
		.unit {
			margin-left: 20rpx;
			margin-right: 20rpx;
			font-size: 30rpx;
			color: #667eea;
			font-weight: 500;
		}
	}
}

/* 固定底部提交按钮容器 */
.fixed-submit-container {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 20rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* 适配安全区域 */
	border-top: 1px solid #f0f0f0;
	z-index: 999; /* 确保在最上层 */
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	
	&:disabled {
		background: #ccc;
	}
	
	/* 添加按钮按下效果 */
	&:active:not(:disabled) {
		transform: translateY(1px);
		opacity: 0.9;
	}
}
</style>
