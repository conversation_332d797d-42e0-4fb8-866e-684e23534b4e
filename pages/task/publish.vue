<template>
	<view class="task-publish">
		<!-- 表单内容 -->
		<scroll-view class="form-scroll" scroll-y>
			<uv-form
				ref="uvForm"
				:model="formData"
				:rules="formRules"
				label-position="top"
				label-width="auto"
			>
				<!-- 基础信息卡片 -->
				<view class="form-card">
					<view class="card-title">基础信息</view>

					<uv-form-item label="任务标题" prop="title" required>
						<uv-input
							v-model="formData.title"
							placeholder="请输入任务标题（10-50字）"
							maxlength="50"
							count
							:custom-style="{
								backgroundColor: 'var(--input-bg)',
								borderRadius: 'var(--radius-sm)',
								border: '1px solid var(--border-color)',
								padding: 'var(--spacing-sm) var(--spacing-md)'
							}"
						/>
					</uv-form-item>

					<uv-form-item label="任务描述" prop="description" required>
						<uv-textarea
							v-model="formData.description"
							placeholder="请详细描述任务内容和背景（20-200字）"
							maxlength="200"
							count
							auto-height
							:custom-style="{
								backgroundColor: 'var(--input-bg)',
								borderRadius: 'var(--radius-sm)',
								border: '1px solid var(--border-color)',
								padding: 'var(--spacing-sm) var(--spacing-md)'
							}"
						/>
					</uv-form-item>

					<uv-form-item label="完成要求" prop="taskRequirements">
						<uv-textarea
							v-model="formData.taskRequirements"
							placeholder="请详细说明任务的完成标准和要求（20-300字）"
							maxlength="300"
							count
							auto-height
							:custom-style="{
								backgroundColor: 'var(--input-bg)',
								borderRadius: 'var(--radius-sm)',
								border: '1px solid var(--border-color)',
								padding: 'var(--spacing-sm) var(--spacing-md)'
							}"
						/>
					</uv-form-item>

					<!-- 任务类型选择 -->
					<uv-form-item label="任务类型" prop="taskType" required>
						<uv-radio-group v-model="formData.taskType" @change="onTaskTypeChange">
							<uv-radio
								name="1"
								:custom-style="radioStyle"
								active-color="var(--brand-color)"
							>
								短期任务
							</uv-radio>
							<uv-radio
								name="2"
								:custom-style="radioStyle"
								active-color="var(--brand-color)"
							>
								长期任务
							</uv-radio>
						</uv-radio-group>
						<view class="input-hint">
							<text v-if="formData.taskType === '1'" class="hint-text">
								短期任务：直接设置最终截止时间
							</text>
							<text v-else class="hint-text">
								长期任务：可设置完成期限，系统将取较早时间作为实际截止时间
							</text>
						</view>
					</uv-form-item>
				</view>

			<!-- 奖励设置卡片 -->
			<view class="form-card">
				<view class="card-title">奖励设置</view>

				<uv-form-item label="任务总数" prop="totalCount" required>
					<view class="count-input-wrapper">
						<uv-input
							v-model="formData.totalCount"
							type="number"
							placeholder="输入数量"
							@input="onCountInput"
							:custom-style="{
								textAlign: 'center',
								fontSize: '16px',
								fontWeight: '600',
								backgroundColor: 'var(--input-bg)',
								borderRadius: 'var(--radius-sm)',
								border: '1px solid var(--border-color)',
								padding: 'var(--spacing-sm) var(--spacing-md)'
							}"
						/>
						<text class="unit-text">个</text>
					</view>
					<view class="input-hint">建议1-10个，最多100个</view>
				</uv-form-item>

				<uv-form-item label="单个奖励" prop="unitPrice" required>
					<view class="price-input-wrapper">
						<uv-input
							v-model="formData.unitPrice"
							type="digit"
							placeholder="0.00"
							@input="onPriceInput"
							:custom-style="{
								textAlign: 'right',
								fontSize: '16px',
								fontWeight: '600',
								backgroundColor: 'var(--input-bg)',
								borderRadius: 'var(--radius-sm)',
								border: '1px solid var(--border-color)',
								padding: 'var(--spacing-sm) var(--spacing-md)'
							}"
						/>
						<text class="price-unit">助力值</text>
					</view>
					<view class="input-hint">单个奖励不少于0.01助力值</view>
				</uv-form-item>

				<view class="info-row highlight-row">
					<text class="info-label">预扣总额</text>
					<view class="total-amount">
						<text class="total-value">{{ totalAmount }}</text>
						<text class="total-unit">助力值</text>
					</view>
				</view>
				
				<view class="calculation-hint" v-if="formData.totalCount && formData.unitPrice">
					<text class="calculation-text">{{ formData.totalCount }} × {{ formData.unitPrice }} = {{ totalAmount }} 助力值</text>
				</view>
			</view>

			<!-- 时间设置卡片 -->
			<view class="form-card">
				<view class="card-title">时间设置</view>

				<uv-form-item label="截止时间" prop="deadline" required>
					<view class="datetime-selector" @click="showDateTimePicker">
						<text :class="{ 'datetime-text': true, 'placeholder': !formData.deadline }">
							{{ formData.deadline ? formatDateTime(formData.deadline) : '请选择截止时间' }}
						</text>
						<uv-icon name="arrow-right" size="14" color="var(--text-color-secondary)"></uv-icon>
					</view>
				</uv-form-item>

				<!-- 几天内完成（仅长期任务显示） -->
				<transition name="slide-fade">
					<uv-form-item v-if="formData.taskType === '2'" label="几天内完成" prop="daysToComplete" required>
						<view class="days-input-wrapper">
							<uv-input
								v-model="formData.daysToComplete"
								type="number"
								placeholder="请输入天数（1-365）"
								@input="onDaysChange"
								:custom-style="{
									backgroundColor: 'var(--input-bg)',
									borderRadius: 'var(--radius-sm)',
									border: '1px solid var(--border-color)',
									padding: 'var(--spacing-sm) var(--spacing-md)',
									flex: 1
								}"
							/>
							<text class="unit-text">天</text>
						</view>
						<view class="input-hint">
							<text class="hint-text">设置任务的完成期限，系统将自动计算实际截止时间</text>
						</view>
					</uv-form-item>
				</transition>

				<!-- 长期任务说明 -->
				<view v-if="formData.taskType === '2' && formData.daysToComplete" class="deadline-preview">
					<view class="preview-header">
						<uv-icon name="info-circle" size="16" color="var(--brand-color)"></uv-icon>
						<text class="preview-label">长期任务说明</text>
					</view>
					<text class="preview-note">用户接受任务后，系统将根据接受时间和完成期限自动计算实际截止时间</text>
				</view>
			</view>

			<!-- 余额信息卡片 -->
			<view class="form-card">
				<view class="card-title">余额信息</view>

				<view class="info-row">
					<text class="info-label">当前助力值余额</text>
					<view class="balance-amount">
						<text class="balance-value">{{ userBalance }}</text>
						<text class="balance-unit">助力值</text>
					</view>
				</view>

				<uv-alert
					v-if="totalAmount > userBalance"
					type="warning"
					title="余额不足，请先充值"
					:show-icon="true"
					:closable="false"
					:custom-style="{
						marginTop: 'var(--spacing-md)',
						borderRadius: 'var(--radius-sm)'
					}"
				></uv-alert>
			</view>
			</uv-form>
		</scroll-view>

	<!-- 底部操作栏 -->
	<view class="bottom-actions">
		<uv-button
			type="primary"
			:disabled="!canPublish"
			@click="publishTask"
			:custom-style="{
				background: canPublish ? 'var(--brand-color)' : 'var(--disabled-bg)',
				borderRadius: 'var(--radius-lg)',
				height: '52px',
				border: 'none',
				fontSize: '16px',
				fontWeight: '600'
			}"
		>
			发布任务
		</uv-button>
	</view>

	<!-- 时间选择器 -->
	<uv-datetime-picker
		ref="datetimePicker"
		v-model="formData.deadline"
		mode="datetime"
		:minDate="minDateTime"
		:maxDate="maxDateTime"
		title="选择截止时间"
		@confirm="onDateTimeConfirm"
		@cancel="onDateTimeCancel"
	></uv-datetime-picker>

	</view>
</template>

<script>
import { userStore } from '@/store/index.js';
import { getUserInfos } from '@/hooks';

export default {
	name: 'TaskPublish',
	data() {
		return {
			statusBarHeight: 0,
			users: userStore(),
			formData: {
				title: '',
				description: '',
				taskRequirements: '',
				totalCount: 1,
				unitPrice: '',
				deadline: null, // 改为null，将使用时间戳格式
				priority: 0,
				taskType: '1', // 默认为短期任务
				daysToComplete: null // 几天内完成
			},
			priorityOptions: [
				{ value: 0, label: '普通' },
				{ value: 1, label: '高优先级' },
				{ value: 2, label: '紧急' }
			],
			// 单选框样式
			radioStyle: {
				marginRight: '32rpx',
				marginBottom: '16rpx'
			}
		}
	},
	computed: {
		userBalance() {
			return this.users.userInfo?.balance || 0
		},
		// 时间选择器的最小时间（当前时间）
		minDateTime() {
			return Date.now()
		},
		// 时间选择器的最大时间（一年后）
		maxDateTime() {
			return Date.now() + 365 * 24 * 60 * 60 * 1000
		},
		totalAmount() {
			const count = parseInt(this.formData.totalCount) || 0
			const price = parseFloat(this.formData.unitPrice) || 0
			return (count * price).toFixed(2)
		},
		canPublish() {
			// 主要检查余额是否充足，表单验证由uv-form处理
			return this.totalAmount <= this.userBalance
		},
		// 表单验证规则（优化后）
		formRules() {
			const rules = {
				title: [
					{ required: true, message: '请输入任务标题', trigger: 'blur' }
				],
				description: [
					{ required: true, message: '请输入任务描述', trigger: 'blur' }
				],
				taskType: [
					{ required: true, message: '请选择任务类型', trigger: 'change' }
				],
				totalCount: [
					{
						type: 'number',
						required: true,
						min: 1,
						message: '请设置任务数量',
						trigger: ['change', 'blur']
					}
				],
				unitPrice: [
					{
						required: true,
						validator: this.validateUnitPrice,
						trigger: ['blur', 'change']
					}
				],
				deadline: [
					{
						required: true,
						validator: this.validateDeadline,
						trigger: 'change'
					}
				]
			}

			// 长期任务时添加天数验证
			if (this.formData.taskType === '2') {
				rules.daysToComplete = [
					{ required: true, message: '请输入完成天数', trigger: 'blur' },
					{
						validator: this.validateDaysToComplete,
						trigger: ['blur', 'change']
					}
				]
			}

			return rules
		},


	},
	onLoad() {
		// 获取系统状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight || 0
	},
	onReady() {
		// 设置表单验证规则（uv-ui要求）
		this.$refs.uvForm.setRules(this.formRules)
	},
	methods: {
		// 自定义验证函数：单个奖励
		validateUnitPrice(rule, value, callback) {
			if (!value || value === '') {
				callback(new Error('请设置单个奖励'))
				return
			}
			const numValue = parseFloat(value)
			if (isNaN(numValue) || numValue <= 0) {
				callback(new Error('请输入有效的奖励金额'))
				return
			}
			if (numValue < 0.01) {
				callback(new Error('单个奖励不能少于0.01助力值'))
				return
			}
			callback()
		},

		// 自定义验证函数：截止时间
		validateDeadline(rule, value, callback) {
			if (!value) {
				callback(new Error('请选择截止时间'))
				return
			}
			const now = Date.now()
			const deadline = typeof value === 'number' ? value : new Date(value).getTime()
			if (deadline <= now) {
				callback(new Error('截止时间必须晚于当前时间'))
				return
			}
			callback()
		},

		// 自定义验证函数：完成天数
		validateDaysToComplete(rule, value, callback) {
			if (!value) {
				callback(new Error('请输入完成天数'))
				return
			}
			const days = parseInt(value)
			if (isNaN(days) || days <= 0) {
				callback(new Error('完成天数必须大于0'))
				return
			}
			if (days > 365) {
				callback(new Error('完成天数不能超过365天'))
				return
			}
			callback()
		},

		// 任务类型切换事件
		onTaskTypeChange(value) {
			console.log('任务类型切换:', value)
			// 切换到短期任务时，清空天数
			if (value === '1') {
				this.formData.daysToComplete = null
			}
			// 重新验证表单
			this.$nextTick(() => {
				this.$refs.uvForm.clearValidate()
			})
		},

		// 天数输入变化事件
		onDaysChange(value) {
			console.log('天数变化:', value)
			// 可以在这里添加实时计算逻辑
		},



		// 显示时间选择器
		showDateTimePicker() {
			this.$refs.datetimePicker.open()
		},

		// 时间选择器确认事件
		onDateTimeConfirm(e) {
			console.log('选择的时间:', e)
			// e.value 是时间戳格式
			this.formData.deadline = e.value
		},

		// 时间选择器取消事件
		onDateTimeCancel() {
			console.log('取消选择时间')
		},
		
		// 价格输入
		onPriceInput(e) {
			let value = e.detail.value
			// 只允许数字和小数点
			value = value.replace(/[^\d.]/g, '')
			// 只允许一个小数点
			const parts = value.split('.')
			if (parts.length > 2) {
				value = parts[0] + '.' + parts.slice(1).join('')
			}
			// 限制小数位数
			if (parts[1] && parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2)
			}
			this.formData.unitPrice = value
		},
		
		// 任务总数输入验证
		onCountInput(e) {
			let value = parseInt(e.detail.value) || 0
			// 限制范围在1-100之间
			if (value < 1) value = 1
			if (value > 100) value = 100
			this.formData.totalCount = value
		},
		
		// 选择优先级
		selectPriority(priority) {
			this.formData.priority = priority
		},
		
		// 保存草稿
		async saveDraft() {
			try {
				// 保存到本地存储
				uni.setStorageSync('taskDraft', this.formData)
				uni.showToast({
					title: '草稿已保存',
					icon: 'success'
				})
			} catch (error) {
				console.error('保存草稿失败:', error)
			}
		},
		
		// 发布任务
		async publishTask() {
			// 调试：打印formData的值
			console.log('发布任务时的formData:', JSON.stringify(this.formData, null, 2))
			console.log('totalCount类型:', typeof this.formData.totalCount, '值:', this.formData.totalCount)

			// 使用uv-form验证
			try {
				await this.$refs.uvForm.validate()
			} catch (error) {
				console.log('表单验证失败:', error)
				// 显示具体的验证错误信息
				if (error && error.length > 0) {
					const firstError = error[0]
					uni.showToast({
						title: firstError.message || '请完善表单信息',
						icon: 'none'
					})
				} else {
					uni.showToast({
						title: '请完善表单信息',
						icon: 'none'
					})
				}
				return
			}

			// 检查余额是否充足
			if (this.totalAmount > this.userBalance) {
				uni.showToast({
					title: '余额不足，请先充值',
					icon: 'none'
				})
				return
			}
			
			// 确认发布
			const res = await uni.showModal({
				title: '确认发布',
				content: `将预扣${this.totalAmount}助力值，确认发布任务吗？`
			})
			
			if (!res.confirm) return
			
			try {
				uni.showLoading({ title: '发布中...' })

				// 准备发布数据，确保格式正确
				const publishData = {
					...this.formData,
					remainingCount: this.formData.totalCount,
					unitPrice: parseFloat(this.formData.unitPrice), // 确保是数字
					deadline: this.formatDateTimeForBackend(this.formData.deadline) // 转换为后端期望的格式
				}

				const result = await uni.http.post(uni.api.publishTask, publishData)
				
				if (result.data.success) {
					uni.hideLoading()
					uni.showToast({
						title: '发布成功',
						icon: 'success'
					})
					
					// 清除草稿
					uni.removeStorageSync('taskDraft')
					
					// 返回任务中心
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.hideLoading()
					uni.showToast({
						title: result.data.message || '发布失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.hideLoading()
				console.error('发布任务失败:', error)
				uni.showToast({
					title: '发布失败',
					icon: 'none'
				})
			}
		},

		// 返回上一页
		goBack() {
			// 检查是否有未保存的内容
			const hasContent = this.formData.title || this.formData.description || this.formData.taskRequirements
			
			if (hasContent) {
				uni.showModal({
					title: '提示',
					content: '有未保存的内容，是否保存为草稿？',
					confirmText: '保存',
					cancelText: '不保存',
					success: (res) => {
						if (res.confirm) {
							this.saveDraft()
						}
						uni.navigateBack()
					}
				})
			} else {
				uni.navigateBack()
			}
		},
		
		// 格式化日期时间（支持时间戳和ISO字符串）
		formatDateTime(datetime) {
			if (!datetime) return ''

			// 支持时间戳和ISO字符串格式
			const date = typeof datetime === 'number' ? new Date(datetime) : new Date(datetime)

			// 检查日期是否有效
			if (isNaN(date.getTime())) return ''

			const year = date.getFullYear()
			const month = (date.getMonth() + 1).toString().padStart(2, '0')
			const day = date.getDate().toString().padStart(2, '0')
			const hour = date.getHours().toString().padStart(2, '0')
			const minute = date.getMinutes().toString().padStart(2, '0')

			return `${year}-${month}-${day} ${hour}:${minute}`
		},

		// 格式化日期时间为后端期望的格式（yyyy-MM-dd HH:mm:ss）
		formatDateTimeForBackend(datetime) {
			if (!datetime) return ''

			// 支持时间戳和ISO字符串格式
			const date = typeof datetime === 'number' ? new Date(datetime) : new Date(datetime)

			// 检查日期是否有效
			if (isNaN(date.getTime())) return ''

			const year = date.getFullYear()
			const month = (date.getMonth() + 1).toString().padStart(2, '0')
			const day = date.getDate().toString().padStart(2, '0')
			const hour = date.getHours().toString().padStart(2, '0')
			const minute = date.getMinutes().toString().padStart(2, '0')
			const second = date.getSeconds().toString().padStart(2, '0')

			return `${year}-${month}-${day} ${hour}:${minute}:${second}`
		}
	},
	
	onShow() {
		// 刷新用户信息
		getUserInfos()

		// 尝试加载草稿
		try {
			const draft = uni.getStorageSync('taskDraft')
			if (draft && Object.keys(draft).length > 0) {
				uni.showModal({
					title: '发现草稿',
					content: '是否恢复上次编辑的内容？',
					success: (res) => {
						if (res.confirm) {
							this.formData = { ...this.formData, ...draft }
						}
					}
				})
			}
		} catch (error) {
			console.error('加载草稿失败:', error)
		}
	}
}
</script>

<style lang="scss" scoped>
/* 设计Token系统 */
.task-publish {
	/* 间距系统 */
	--spacing-xs: 4px;
	--spacing-sm: 8px;
	--spacing-md: 12px;
	--spacing-lg: 16px;
	--spacing-xl: 20px;
	--spacing-xxl: 24px;
	--spacing-xxxl: 32px;
	
	/* 圆角系统 */
	--radius-xs: 4px;
	--radius-sm: 8px;
	--radius-md: 12px;
	--radius-lg: 16px;
	--radius-xl: 20px;
	
	/* 色彩系统 */
	--brand-color: #667eea;
	--brand-color-light: #7c8df0;
	--brand-color-lighter: rgba(102, 126, 234, 0.1);
	--success-color: #4caf50;
	--warning-color: #ff9800;
	--text-color-primary: #333333;
	--text-color-secondary: #666666;
	--text-color-placeholder: #999999;
	--border-color: #e4e7ed;
	--border-color-light: #f0f0f0;
	--input-bg: #f8f9fa;
	--disabled-bg: #e9ecef;
	--disabled-text: #adb5bd;
	
	/* 阴影系统 */
	--shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
	--shadow-md: 0 2px 8px rgba(0, 0, 0, 0.06);
	--shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.task-publish {
	min-height: 100vh;
	background-color: #f5f7fa;
	display: flex;
	flex-direction: column;
}



/* 表单容器样式 */
.form-scroll {
	flex: 1;
	padding: 0;
	background-color: #f5f7fa;
	padding-bottom: 140px; /* 为底部操作栏预留空间 */
}

/* 表单卡片样式 - 统一规范 */
.form-card {
	background: white;
	margin: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
	border-radius: var(--radius-lg);
	padding: var(--spacing-xl);
	box-shadow: var(--shadow-md);
	border: 1px solid var(--border-color-light);
}

.card-title {
	font-size: 16px;
	font-weight: 600;
	color: var(--text-color-primary);
	margin-bottom: var(--spacing-xl);
	padding-left: var(--spacing-xs);
	position: relative;
}

.card-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 3px;
	height: 16px;
	background: var(--brand-color);
	border-radius: var(--radius-xs);
}

/* 信息行样式 */
.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: var(--spacing-lg) 0;
	border-top: 1px solid var(--border-color-light);
}

.info-row:first-child {
	border-top: none;
	padding-top: 0;
}

.info-row:last-child {
	padding-bottom: 0;
}

.info-label {
	font-size: 15px;
	color: var(--text-color-primary);
	font-weight: 500;
}

/* 任务总数输入样式 */
.count-input-wrapper {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	position: relative;

	.unit-text {
		font-size: 14px;
		color: var(--text-color-secondary);
		font-weight: 500;
		white-space: nowrap;
		margin-left: var(--spacing-sm);
	}
}

/* 输入提示样式 */
.input-hint {
	font-size: 12px;
	color: var(--text-color-secondary);
	margin-top: var(--spacing-xs);
	padding-left: var(--spacing-xs);
	line-height: 1.4;
}

/* 突出显示的信息行 */
.highlight-row {
	background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, rgba(102, 126, 234, 0.02) 100%);
	border-radius: var(--radius-sm);
	padding: var(--spacing-lg) var(--spacing-md) !important;
	margin: var(--spacing-sm) 0;
	border: 1px solid rgba(102, 126, 234, 0.1);
}

/* 计算提示 */
.calculation-hint {
	background: rgba(102, 126, 234, 0.05);
	border-radius: var(--radius-sm);
	padding: var(--spacing-sm) var(--spacing-md);
	margin-top: var(--spacing-sm);
	border-left: 3px solid var(--brand-color);
}

.calculation-text {
	font-size: 13px;
	color: var(--brand-color);
	font-weight: 500;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 时间选择器样式 */
.datetime-selector {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: var(--spacing-lg);
	background-color: var(--input-bg);
	border-radius: var(--radius-sm);
	border: 1px solid var(--border-color);
	min-height: 48px; /* 保证触摸区域 */
	transition: all 0.2s ease;
}

.datetime-selector:active {
	background-color: #f0f2f5;
	transform: scale(0.98);
}

/* 价格输入样式 */
.price-input-wrapper {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
	position: relative;

	.price-unit {
		font-size: 14px;
		color: var(--text-color-secondary);
		white-space: nowrap;
		font-weight: 500;
		margin-left: var(--spacing-sm);
	}
}

/* 总额显示样式 */
.total-amount {
	display: flex;
	align-items: center;
	gap: var(--spacing-xs);

	.total-value {
		font-size: 18px;
		font-weight: 700;
		color: var(--brand-color);
	}

	.total-unit {
		font-size: 13px;
		color: var(--text-color-secondary);
	}
}

/* 余额显示样式 */
.balance-amount {
	display: flex;
	align-items: center;
	gap: var(--spacing-xs);

	.balance-value {
		font-size: 18px;
		font-weight: 700;
		color: var(--success-color);
	}

	.balance-unit {
		font-size: 13px;
		color: var(--text-color-secondary);
	}
}

/* 时间显示样式 */
.datetime-text {
	font-size: 15px;
	color: var(--text-color-primary);
	font-weight: 500;

	&.placeholder {
		color: var(--text-color-placeholder);
		font-weight: 400;
	}
}

/* 底部操作栏样式 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: var(--spacing-lg) var(--spacing-xl);
	padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom));
	border-top: 1px solid var(--border-color-light);
	box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
	z-index: 100;
}

/* uv-ui组件样式优化 */
.form-card :deep(.uv-form-item) {
	margin-bottom: var(--spacing-xl);
}

.form-card :deep(.uv-form-item:last-child) {
	margin-bottom: 0;
}

.form-card :deep(.uv-form-item__label) {
	font-size: 15px;
	font-weight: 600;
	color: var(--text-color-primary);
	margin-bottom: var(--spacing-sm);
}

/* 输入框获得焦点时的效果 */
.count-input-wrapper :deep(.uv-input__content) {
	transition: all 0.2s ease;
}

.count-input-wrapper :deep(.uv-input__content:focus-within) {
	border-color: var(--brand-color) !important;
	box-shadow: 0 0 0 3px var(--brand-color-lighter) !important;
}

.price-input-wrapper :deep(.uv-input__content:focus-within) {
	border-color: var(--brand-color) !important;
	box-shadow: 0 0 0 3px var(--brand-color-lighter) !important;
}

/* 响应式适配 */
@media (max-width: 375px) {
	.form-card {
		margin: 0 var(--spacing-md) var(--spacing-md) var(--spacing-md);
		padding: var(--spacing-lg);
	}

	.bottom-actions {
		padding: var(--spacing-md) var(--spacing-lg);
		padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom));
	}
}

/* 优化小屏幕下的字体大小 */
@media (max-width: 320px) {
	.card-title {
		font-size: 15px !important;
	}

	.info-label {
		font-size: 14px !important;
	}
}

/* 新增样式：任务类型和动态表单 */
.input-hint {
	margin-top: var(--spacing-xs);
}

.hint-text {
	font-size: 12px;
	color: var(--text-color-secondary);
	line-height: 1.4;
}

.days-input-wrapper {
	display: flex;
	align-items: center;
	gap: var(--spacing-sm);
}

.unit-text {
	font-size: 14px;
	color: var(--text-color-primary);
	font-weight: 500;
	min-width: 24px;
}

.deadline-preview {
	background: linear-gradient(135deg, var(--brand-color-lighter) 0%, var(--brand-color-light) 100%);
	border-radius: var(--radius-md);
	padding: var(--spacing-md);
	margin-top: var(--spacing-md);
	border: 1px solid var(--brand-color-light);
}

.preview-header {
	display: flex;
	align-items: center;
	gap: var(--spacing-xs);
	margin-bottom: var(--spacing-xs);
}

.preview-label {
	font-size: 13px;
	font-weight: 600;
	color: var(--brand-color);
}

.preview-value {
	font-size: 15px;
	font-weight: 600;
	color: var(--text-color-primary);
	margin-bottom: var(--spacing-xs);
}

.preview-note {
	font-size: 11px;
	color: var(--text-color-secondary);
	opacity: 0.8;
}

/* 动画效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
	transition: all 0.3s ease;
}

.slide-fade-enter-from {
	opacity: 0;
	transform: translateY(-10px);
}

.slide-fade-leave-to {
	opacity: 0;
	transform: translateY(-10px);
}
</style>
