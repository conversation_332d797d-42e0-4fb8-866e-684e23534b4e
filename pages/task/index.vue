<template>
	<view class="task-center">
		<!-- Tab切换 -->
		<view class="tab-container">
			<view class="tab-list">
				<view 
					v-for="(tab, index) in tabs" 
					:key="index"
					class="tab-item"
					:class="{ active: currentTab === index }"
					@click="switchTab(index)"
				>
					<text class="tab-text">{{ tab.name }}</text>
					<view v-if="tab.badge > 0" class="tab-badge">{{ tab.badge }}</view>
				</view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content-container">
			<!-- 任务广场 -->
			<view v-if="currentTab === 0" class="tab-content">
				<TaskHall ref="taskHall" />
			</view>
			
			<!-- 参与中 -->
			<view v-if="currentTab === 1" class="tab-content">
				<MyAcceptedTasks ref="myAccepted" />
			</view>

			<!-- 审核台 -->
			<view v-if="currentTab === 2" class="tab-content">
				<MyPendingAuditTasks ref="myPendingAudit" />
			</view>

			<!-- 发布管理 -->
			<view v-if="currentTab === 3" class="tab-content">
				<MyPublishedTasks ref="myPublished" />
			</view>
		</view>
		
		<!-- 悬浮发布按钮 -->
		<view class="floating-btn" @click="goToPublish">
			<view class="btn-icon">+</view>
		</view>
	</view>
</template>

<script>
import TaskHall from './components/TaskHall.vue'
import MyAcceptedTasks from './components/MyAcceptedTasks.vue'
import MyPendingAuditTasks from './components/MyPendingAuditTasks.vue'
import MyPublishedTasks from './components/MyPublishedTasks.vue'

export default {
	name: 'TaskCenter',
	components: {
		TaskHall,
		MyAcceptedTasks,
		MyPendingAuditTasks,
		MyPublishedTasks
	},
	data() {
		return {
			statusBarHeight: 0,
			currentTab: 0,
			tabs: [
				{ name: '任务广场', badge: 0 },
				{ name: '参与中', badge: 0 },
				{ name: '审核台', badge: 0 },
				{ name: '发布管理', badge: 0 }
			]
		}
	},
	onLoad(options) {
		// 获取系统状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight || 0
		
		// 处理从其他页面跳转过来的tab参数
		if (options.tab) {
			this.currentTab = parseInt(options.tab) || 0
		}
		
		// 加载数据
		this.loadData()
	},
	onShow() {
		// 页面显示时刷新当前tab的数据
		this.refreshCurrentTab()
		// 更新badge数量
		this.updateBadges()
	},
	onPullDownRefresh() {
		// 下拉刷新当前tab
		this.refreshCurrentTab()
		setTimeout(() => {
			uni.stopPullDownRefresh()
		}, 1000)
	},
	methods: {
		// 切换Tab
		switchTab(index) {
			if (this.currentTab === index) return
			
			this.currentTab = index
			this.loadTabData(index)
		},
		
		// 加载初始数据
		loadData() {
			this.loadTabData(this.currentTab)
			this.updateBadges()
		},
		
		// 加载指定tab的数据
		loadTabData(tabIndex) {
			this.$nextTick(() => {
				switch(tabIndex) {
					case 0:
						this.$refs.taskHall && this.$refs.taskHall.loadData()
						break
					case 1:
						this.$refs.myAccepted && this.$refs.myAccepted.loadData()
						break
					case 2:
						this.$refs.myPendingAudit && this.$refs.myPendingAudit.loadData()
						break
					case 3:
						this.$refs.myPublished && this.$refs.myPublished.loadData()
						break
				}
			})
		},
		
		// 刷新当前tab
		refreshCurrentTab() {
			this.loadTabData(this.currentTab)
		},
		
		// 更新badge数量
		async updateBadges() {
			try {
				// 获取待审核任务数量
				const pendingRes = await uni.http.get(uni.api.getPendingAuditTasks, {
					pageNo: 1,
					pageSize: 1
				})
				if (pendingRes.data.success) {
					this.tabs[2].badge = pendingRes.data.result.total || 0
				}
			} catch (error) {
				console.error('更新badge失败:', error)
			}
		},
		
		// 跳转到发布任务页面
		goToPublish() {
			uni.navigateTo({
				url: '/pages/task/publish'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.task-center {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.custom-navbar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	
	.navbar-content {
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		
		.navbar-title {
			font-size: 18px;
			font-weight: 600;
		}
	}
}

.tab-container {
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(255, 255, 255, 1) 100%);
	border-bottom: 1px solid rgba(102, 126, 234, 0.1);
	box-shadow: 0 1px 4px rgba(102, 126, 234, 0.08);
	backdrop-filter: blur(10px);

	.tab-list {
		display: flex;
		height: 48px;
		position: relative;

		.tab-item {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			cursor: pointer;
			transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

			.tab-text {
				font-size: 15px;
				font-weight: 500;
				color: #8a8a8a;
				transition: all 0.3s ease;
				position: relative;
			}
			
			.tab-badge {
				position: absolute;
				top: 6px;
				right: calc(50% - 18px);
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: white;
				font-size: 10px;
				font-weight: 600;
				padding: 2px 6px;
				border-radius: 8px;
				min-width: 16px;
				height: 16px;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
				transform: scale(0.9);
				transition: all 0.3s ease;
			}
			
			&:hover {
				.tab-text {
					color: #667eea;
				}
			}
			
			&.active {
				.tab-text {
					color: #667eea;
					font-weight: 600;
				}

				.tab-badge {
					transform: scale(1);
				}

				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 28px;
					height: 3px;
					background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
					border-radius: 2px 2px 0 0;
					box-shadow: 0 -2px 6px rgba(102, 126, 234, 0.3);
				}
			}
		}
	}
}

.content-container {
	flex: 1;
	
	.tab-content {
		min-height: calc(100vh - 144px);
	}
}

.floating-btn {
	position: fixed;
	right: 32rpx;
	z-index: 998;
	bottom: 280rpx;
	bottom: calc(280rpx + constant(safe-area-inset-bottom));
	bottom: calc(280rpx + env(safe-area-inset-bottom));

	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

	&:active {
		transform: scale(0.92);
	}

	animation: float 3s ease-in-out infinite;

	width: 88rpx;
	height: 88rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow:
		0 8rpx 24rpx rgba(102, 126, 234, 0.3),
		0 4rpx 8rpx rgba(102, 126, 234, 0.2),
		0 2rpx 4rpx rgba(102, 126, 234, 0.1);
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.2s ease;

	&:active {
		box-shadow:
			0 4rpx 12rpx rgba(102, 126, 234, 0.4),
			0 2rpx 6rpx rgba(102, 126, 234, 0.3);
	}

	.btn-icon {
		color: white;
		font-size: 48rpx;
		font-weight: 300;
		line-height: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
	}
}

@keyframes float {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-8rpx);
	}
}

// 响应式适配
@media screen and (max-width: 375px) {
	.tab-container {
		.tab-list {
			height: 44px;
			
			.tab-item {
				.tab-text {
					font-size: 14px;
				}
				
				.tab-badge {
					right: calc(50% - 16px);
					font-size: 9px;
					padding: 1px 4px;
					min-width: 14px;
					height: 14px;
				}
			}
		}
	}
	
	.floating-btn {
		right: 24rpx;
		bottom: 260rpx;
		bottom: calc(260rpx + constant(safe-area-inset-bottom));
		bottom: calc(260rpx + env(safe-area-inset-bottom));

		width: 80rpx;
		height: 80rpx;

		.btn-icon {
			font-size: 40rpx;
		}
	}
}
</style>
