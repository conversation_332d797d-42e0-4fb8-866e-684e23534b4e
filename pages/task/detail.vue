<template>
	<view class="task-detail">
		<!-- 内容区域 -->
		<scroll-view class="content-scroll" scroll-y>
			<view v-if="taskDetail" class="detail-content">
				<!-- 任务头部 -->
				<view class="task-header">
					<view class="header-gradient-bg">
						<view class="header-content">
							<view class="header-top">
								<text class="task-title">{{ taskDetail.title }}</text>
								<view class="task-badges">
									<text v-if="taskDetail.isOfficial" class="official-badge">
										<text class="badge-icon">👑</text>官方
									</text>
									<text v-if="taskDetail.priority > 0" class="priority-badge" :class="'priority-' + taskDetail.priority">
										<text class="badge-icon">⚡</text>{{ getPriorityText(taskDetail.priority) }}
									</text>
								</view>
							</view>
							<view class="task-status-wrapper">
								<view class="task-status" :class="'status-' + taskDetail.status">
									{{ getStatusText(taskDetail.status) }}
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 任务信息卡片 -->
				<view class="info-card">
					<view class="card-title">
						<text class="title-icon">📊</text>
						<text class="title-text">任务信息</text>
					</view>
					
					<!-- 突出显示的价格和状态 -->
					<view class="price-highlight">
						<view class="price-section">
							<text class="price-label">单价奖励</text>
							<text class="price-value">{{ taskDetail.unitPrice }}</text>
							<text class="price-unit">助力值</text>
						</view>
						<view class="status-section">
							<text class="remaining-count">{{ taskDetail.remainingCount }}</text>
							<text class="remaining-total">/{{ taskDetail.totalCount }}</text>
							<text class="remaining-label">剩余/总数</text>
						</view>
					</view>
					
					<!-- 其他信息 -->
					<view class="info-grid">
						<view class="info-item">
							<view class="info-icon">⏰</view>
							<view class="info-content">
								<text class="info-label">截止时间</text>
								<text class="info-value deadline" :class="{ expired: isExpired }">
									{{ formatDeadline(taskDetail.deadline) }}
								</text>
							</view>
						</view>
						<view class="info-item">
							<view class="info-icon">🎯</view>
							<view class="info-content">
								<text class="info-label">完成进度</text>
								<text class="info-value">{{ getProgressPercent() }}%</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 任务描述 -->
				<view class="desc-card">
					<view class="card-title">
						<text class="title-icon">📝</text>
						<text class="title-text">任务描述</text>
					</view>
					<text class="desc-content">{{ taskDetail.description }}</text>
				</view>
				
				<!-- 任务要求 -->
				<view class="requirements-card">
					<view class="card-title">
						<text class="title-icon">✅</text>
						<text class="title-text">完成要求</text>
					</view>
					<text class="requirements-content">{{ taskDetail.taskRequirements }}</text>
				</view>
				
				<!-- 发布者信息 -->
				<view class="publisher-card">
					<view class="card-title">
						<text class="title-icon">👤</text>
						<text class="title-text">发布者信息</text>
					</view>
					<view class="publisher-info">
						<view class="publisher-left">
							<text class="publisher-type">{{ getPublisherTypeText(taskDetail.publisherType) }}</text>
							<text class="publish-time">{{ formatTime(taskDetail.createTime) }}</text>
						</view>
						<view class="publisher-badge" :class="taskDetail.publisherType === '2' ? 'official' : 'user'">
							<text class="badge-text">{{ taskDetail.publisherType === '2' ? '官方' : '用户' }}</text>
						</view>
					</view>
				</view>
				
				<!-- 我的接受记录（如果有） -->
				<view v-if="myRecord" class="record-card">
					<view class="card-title">
						<text class="title-icon">📋</text>
						<text class="title-text">我的接受记录</text>
					</view>
					<view class="record-content">
						<view class="record-info">
							<view class="record-item">
								<text class="record-label">接受数量:</text>
								<text class="record-value">{{ myRecord.acceptCount }}个</text>
							</view>
							<view class="record-item">
								<text class="record-label">总奖励:</text>
								<text class="record-value price">{{ myRecord.totalReward }}助力值</text>
							</view>
							<view class="record-item">
								<text class="record-label">当前状态:</text>
								<view class="status-badge" :class="'status-' + myRecord.status">
									{{ getRecordStatusText(myRecord.status) }}
								</view>
							</view>
							<view class="record-item">
								<text class="record-label">接受时间:</text>
								<text class="record-value">{{ formatTime(myRecord.acceptTime) }}</text>
							</view>
						</view>
						
						<!-- 审核信息 -->
						<view v-if="myRecord.auditReason && myRecord.status === '4'" class="audit-info">
							<text class="audit-title">❌ 审核意见:</text>
							<text class="audit-content">{{ myRecord.auditReason }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载状态 -->
			<view v-else class="loading-state">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</scroll-view>
		
		<!-- 底部操作栏 -->
		<view v-if="taskDetail" class="bottom-actions">
			<!-- 普通用户操作 -->
			<view v-if="!isMyTask && !myRecord" class="action-group">
				<view 
					class="action-btn primary"
					:class="{ disabled: !canAccept }"
					@click="acceptTask"
				>
					<text class="btn-text">{{ getAcceptButtonText() }}</text>
				</view>
			</view>
			
			<!-- 已接受任务的操作 -->
			<view v-if="myRecord" class="action-group">
				<view v-if="myRecord.status === '1'" class="action-btn primary" @click="submitAudit">
					<text class="btn-text">提交审核</text>
				</view>
				<view v-if="myRecord.status === '4'" class="action-btn warning" @click="resubmit">
					<text class="btn-text">重新提交</text>
				</view>
				<view v-if="myRecord.status === '2'" class="action-btn disabled">
					<text class="btn-text">审核中</text>
				</view>
				<view v-if="myRecord.status === '3'" class="action-btn success">
					<text class="btn-text">已完成</text>
				</view>
			</view>
			
			<!-- 我发布的任务操作 -->
			<view v-if="isMyTask" class="action-group">
				<view v-if="taskDetail.status === '1'" class="action-btn secondary" @click="editTask">
					<text class="btn-text">编辑任务</text>
				</view>
				<view v-if="taskDetail.status === '1'" class="action-btn warning" @click="cancelTask">
					<text class="btn-text">取消任务</text>
				</view>
				<view v-if="hasAcceptors" class="action-btn primary" @click="viewAcceptors">
					<text class="btn-text">查看接受者</text>
				</view>
			</view>
		</view>
		
		<!-- 数量选择弹窗 -->
		<QuantityModal 
			v-model="showQuantityModal"
			:task="taskDetail"
			@confirm="onQuantityConfirm"
		/>
	</view>
</template>

<script>
import QuantityModal from './components/QuantityModal.vue'

export default {
	name: 'TaskDetail',
	components: {
		QuantityModal
	},
	data() {
		return {
			statusBarHeight: 0,
			taskId: '',
			recordId: '',
			taskType: '', // published表示我发布的任务
			taskDetail: null,
			myRecord: null,
			loading: false,
			showQuantityModal: false
		}
	},
	computed: {
		isMyTask() {
			return this.taskType === 'published' || (this.taskDetail && this.taskDetail.isMyTask)
		},
		hasAcceptors() {
			return this.taskDetail && (this.taskDetail.totalCount - this.taskDetail.remainingCount) > 0
		},
		canAccept() {
			if (!this.taskDetail) return false
			return this.taskDetail.status === '1' && 
				   this.taskDetail.remainingCount > 0 && 
				   !this.isExpired &&
				   !this.myRecord
		},
		isExpired() {
			if (!this.taskDetail) return false
			return new Date(this.taskDetail.deadline).getTime() < Date.now()
		}
	},
	onLoad(options) {
		// 获取系统状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight || 0
		
		// 获取参数
		this.taskId = options.id
		this.recordId = options.recordId
		this.taskType = options.type
		
		// 加载数据
		this.loadTaskDetail()
		
		if (this.recordId) {
			this.loadMyRecord()
		}
	},
	methods: {
		// 加载任务详情
		async loadTaskDetail() {
			if (!this.taskId) return

			this.loading = true

			try {
				const res = await uni.http.get(uni.api.getTaskDetail.replace('{id}', this.taskId))
				
				if (res.data.success) {
					this.taskDetail = res.data.result
				} else {
					uni.showToast({
						title: res.data.message || '加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载任务详情失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 加载我的接受记录
		async loadMyRecord() {
			if (!this.recordId) return

			try {
				const res = await uni.http.get(`/after/task/record/${this.recordId}`)
				
				if (res.data.success) {
					this.myRecord = res.data.result
				}
			} catch (error) {
				console.error('加载接受记录失败:', error)
			}
		},
		
		// 接受任务
		acceptTask() {
			if (!this.canAccept) return
			
			this.showQuantityModal = true
		},
		
		// 确认接受数量
		async onQuantityConfirm(quantity) {
			try {
				const res = await uni.http.post(uni.api.acceptTask, {
					taskId: this.taskId,
					acceptCount: quantity
				})

				if (res.data.success) {
					uni.showToast({
						title: '接受成功',
						icon: 'success'
					})

					// 刷新页面数据
					this.loadTaskDetail()

					// 跳转到我接受的任务页面
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					// 业务错误：显示后端返回的具体错误信息
					uni.showToast({
						title: res.data.message || '接受失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('接受任务失败:', error)
				// 网络错误或其他系统异常
				if (error && error.message) {
					// 如果error有具体的message，显示具体信息
					uni.showToast({
						title: error.message,
						icon: 'none'
					})
				} else {
					// 否则显示通用错误提示
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					})
				}
			}
		},
		
		// 提交审核
		submitAudit() {
			uni.navigateTo({
				url: `/pages/task/submit-audit?recordId=${this.myRecord.id}`
			})
		},
		
		// 重新提交
		resubmit() {
			uni.navigateTo({
				url: `/pages/task/resubmit?recordId=${this.myRecord.id}`
			})
		},
		
		// 编辑任务
		editTask() {
			uni.navigateTo({
				url: `/pages/task/edit?id=${this.taskId}`
			})
		},
		
		// 取消任务
		async cancelTask() {
			const res = await uni.showModal({
				title: '确认取消',
				content: '取消后任务将下架，已接受的任务将失效，确认取消吗？'
			})
			
			if (!res.confirm) return
			
			try {
				const result = await uni.http.post(uni.api.cancelTask, {
					taskId: this.taskId
				})
				
				if (result.data.success) {
					uni.showToast({
						title: '取消成功',
						icon: 'success'
					})
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({
						title: result.data.message || '取消失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('取消任务失败:', error)
				uni.showToast({
					title: '取消失败',
					icon: 'none'
				})
			}
		},
		
		// 查看接受者
		viewAcceptors() {
			uni.navigateTo({
				url: `/pages/task/acceptors?taskId=${this.taskId}`
			})
		},
		
		// 显示操作菜单
		showActionSheet() {
			const actions = []
			
			if (this.taskDetail.status === '1') {
				actions.push('编辑任务', '取消任务')
			}
			
			if (this.hasAcceptors) {
				actions.push('查看接受者')
			}
			
			if (actions.length === 0) return
			
			uni.showActionSheet({
				itemList: actions,
				success: (res) => {
					const action = actions[res.tapIndex]
					switch(action) {
						case '编辑任务':
							this.editTask()
							break
						case '取消任务':
							this.cancelTask()
							break
						case '查看接受者':
							this.viewAcceptors()
							break
					}
				}
			})
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'1': '进行中',
				'2': '已满额',
				'3': '已完成',
				'4': '已过期',
				'5': '已下架'
			}
			return statusMap[status] || '未知'
		},
		
		// 获取记录状态文本
		getRecordStatusText(status) {
			const statusMap = {
				'1': '进行中',
				'2': '待审核',
				'3': '已完成',
				'4': '未通过',
				'5': '已过期'
			}
			return statusMap[status] || '未知'
		},
		
		// 获取发布者类型文本
		getPublisherTypeText(type) {
			return type === '2' ? '官方发布' : '用户发布'
		},
		
		// 获取优先级文本
		getPriorityText(priority) {
			const map = { 0: '普通', 1: '高', 2: '紧急' }
			return map[priority] || '普通'
		},
		
		// 获取接受按钮文本
		getAcceptButtonText() {
			if (!this.taskDetail) return '接受任务'
			
			if (this.taskDetail.status !== '1') {
				return '任务已结束'
			} else if (this.taskDetail.remainingCount <= 0) {
				return '任务已满额'
			} else if (this.isExpired) {
				return '任务已过期'
			} else {
				return '接受任务'
			}
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			
			const date = new Date(time)
			return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
		},
		
		// 格式化截止时间
		formatDeadline(deadline) {
			const now = new Date()
			const end = new Date(deadline)
			const diff = end.getTime() - now.getTime()
			
			if (diff <= 0) {
				return '已过期'
			}
			
			const days = Math.floor(diff / (1000 * 60 * 60 * 24))
			const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
			
			if (days > 0) {
				return `${days}天后截止`
			} else if (hours > 0) {
				return `${hours}小时后截止`
			} else {
				return '即将截止'
			}
		},
		
		// 获取进度百分比
		getProgressPercent() {
			if (!this.taskDetail || this.taskDetail.totalCount === 0) return 0
			const completed = this.taskDetail.totalCount - this.taskDetail.remainingCount
			return Math.round((completed / this.taskDetail.totalCount) * 100)
		}
	}
}
</script>

<style lang="scss" scoped>
.task-detail {
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	width: 100%;
	overflow-x: hidden;
	box-sizing: border-box;
}

.custom-navbar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	
	.navbar-content {
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16px;
		
		.navbar-left {
			width: 40px;
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				font-size: 24px;
				font-weight: 300;
			}
		}
		
		.navbar-title {
			font-size: 18px;
			font-weight: 600;
		}
		
		.navbar-right {
			width: 40px;
			height: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.nav-btn {
				padding: 6px 12px;
				background-color: rgba(255, 255, 255, 0.2);
				border-radius: 16px;
				
				.nav-text {
					font-size: 14px;
				}
			}
		}
	}
}

.content-scroll {
	flex: 1;
	padding: 16px;
	width: 100%;
	box-sizing: border-box;
	overflow-x: hidden;
}

.detail-content {
	padding-bottom: 80px;
}

.task-header {
	background-color: transparent;
	border-radius: 0;
	padding: 0;
	margin-bottom: 0;
	box-shadow: none;
	width: 100%;
	box-sizing: border-box;
	
	.header-gradient-bg {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 16px;
		padding: 24px;
		margin-bottom: 16px;
		box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
		
		.header-content {
			.header-top {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 16px;
				
				.task-title {
					flex: 1;
					font-size: 18px;
					font-weight: 700;
					color: white;
					line-height: 1.4;
					text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				}
				
				.task-badges {
					display: flex;
					gap: 8px;
					margin-left: 12px;
					
					.official-badge, .priority-badge {
						display: flex;
						align-items: center;
						padding: 6px 12px;
						border-radius: 20px;
						font-size: 12px;
						font-weight: 600;
						backdrop-filter: blur(10px);
						box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
						
						.badge-icon {
							margin-right: 4px;
							font-size: 14px;
						}
					}
					
					.official-badge {
						background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
						color: white;
					}
					
					.priority-badge {
						color: white;
						
						&.priority-1 {
							background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
						}
						
						&.priority-2 {
							background: linear-gradient(135deg, #ef5350 0%, #f44336 100%);
						}
					}
				}
			}
			
			.task-status-wrapper {
				.task-status {
					display: inline-block;
					padding: 8px 20px;
					border-radius: 25px;
					font-size: 14px;
					font-weight: 600;
					backdrop-filter: blur(10px);
					box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
					
					&.status-1 {
						background: rgba(255, 255, 255, 0.9);
						color: #1976d2;
					}
					
					&.status-2 {
						background: rgba(255, 243, 224, 0.9);
						color: #f57c00;
					}
					
					&.status-3 {
						background: rgba(232, 245, 233, 0.9);
						color: #388e3c;
					}
					
					&.status-4 {
						background: rgba(255, 235, 238, 0.9);
						color: #d32f2f;
					}
					
					&.status-5 {
						background: rgba(245, 245, 245, 0.9);
						color: #757575;
					}
				}
			}
		}
	}
}

.info-card, .desc-card, .requirements-card, .publisher-card, .record-card {
	background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 16px;
	padding: 20px;
	margin-bottom: 16px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(102, 126, 234, 0.1);
	width: 100%;
	box-sizing: border-box;
	
	.card-title {
		display: flex;
		align-items: center;
		font-size: 16px;
		font-weight: 700;
		color: #333;
		margin-bottom: 20px;
		
		.title-icon {
			font-size: 20px;
			margin-right: 8px;
		}
		
		.title-text {
			flex: 1;
		}
	}
}

.price-highlight {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
	
	.price-section {
		flex: 1;
		text-align: center;
		
		.price-label {
			display: block;
			font-size: 14px;
			color: rgba(255, 255, 255, 0.8);
			margin-bottom: 8px;
		}
		
		.price-value {
			display: inline-block;
			font-size: 22px;
			font-weight: 800;
			color: white;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
		}
		
		.price-unit {
			font-size: 14px;
			color: rgba(255, 255, 255, 0.9);
			margin-left: 4px;
		}
	}
	
	.status-section {
		flex: 1;
		text-align: center;
		position: relative;
		
		&::before {
			content: '';
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1px;
			height: 40px;
			background: rgba(255, 255, 255, 0.3);
		}
		
		.remaining-count {
			display: inline-block;
			font-size: 20px;
			font-weight: 800;
			color: #4ade80;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
		}
		
		.remaining-total {
			font-size: 18px;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 600;
		}
		
		.remaining-label {
			display: block;
			font-size: 14px;
			color: rgba(255, 255, 255, 0.8);
			margin-top: 4px;
		}
	}
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16px;
	
	.info-item {
		display: flex;
		align-items: center;
		padding: 16px;
		background: rgba(102, 126, 234, 0.05);
		border-radius: 12px;
		border: 1px solid rgba(102, 126, 234, 0.1);
		
		.info-icon {
			font-size: 20px;
			margin-right: 12px;
			opacity: 0.8;
		}
		
		.info-content {
			flex: 1;
			
			.info-label {
				font-size: 13px;
				color: #666;
				margin-bottom: 4px;
			}
			
			.info-value {
				font-size: 15px;
				color: #333;
				font-weight: 600;
				
				&.deadline {
					&.expired {
						color: #f44336;
					}
				}
			}
		}
	}
}

.desc-content, .requirements-content {
	font-size: 14px;
	color: #333;
	line-height: 1.8;
	padding: 16px;
	background: rgba(102, 126, 234, 0.03);
	border-radius: 12px;
	border-left: 4px solid #667eea;
}

.publisher-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	
	.publisher-left {
		flex: 1;
		
		.publisher-type {
			font-size: 15px;
			color: #333;
			font-weight: 600;
			margin-bottom: 4px;
		}
		
		.publish-time {
			font-size: 14px;
			color: #999;
		}
	}
	
	.publisher-badge {
		padding: 8px 16px;
		border-radius: 20px;
		font-size: 12px;
		font-weight: 600;
		
		&.official {
			background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
			color: white;
			box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
		}
		
		&.user {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
		}
		
		.badge-text {
			text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		}
	}
}

.record-content {
	.record-info {
		.record-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			padding: 12px 0;
			border-bottom: 1px solid #f0f0f0;
			
			&:last-child {
				margin-bottom: 0;
				border-bottom: none;
			}
			
			.record-label {
				font-size: 14px;
				color: #666;
				font-weight: 500;
			}
			
			.record-value {
				font-size: 14px;
				color: #333;
				font-weight: 600;
				
				&.price {
					background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					background-clip: text;
					font-weight: 700;
				}
			}
			
			.status-badge {
				padding: 6px 12px;
				border-radius: 15px;
				font-size: 12px;
				font-weight: 600;
				
				&.status-1 {
					background-color: #e3f2fd;
					color: #1976d2;
				}
				
				&.status-2 {
					background-color: #fff3e0;
					color: #f57c00;
				}
				
				&.status-3 {
					background-color: #e8f5e8;
					color: #388e3c;
				}
				
				&.status-4 {
					background-color: #ffebee;
					color: #d32f2f;
				}
				
				&.status-5 {
					background-color: #f5f5f5;
					color: #757575;
				}
			}
		}
	}
}

.audit-info {
	margin-top: 20px;
	padding: 16px;
	background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
	border-radius: 12px;
	border-left: 4px solid #f44336;
	box-shadow: 0 2px 8px rgba(244, 67, 54, 0.1);
	
	.audit-title {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #d32f2f;
		font-weight: 600;
		margin-bottom: 8px;
	}
	
	.audit-content {
		font-size: 14px;
		color: #666;
		line-height: 1.6;
	}
}

.loading-state {
	padding: 80px 20px;
	text-align: center;
	
	.loading-spinner {
		width: 40px;
		height: 40px;
		border: 4px solid #f3f3f3;
		border-top: 4px solid #667eea;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20px;
	}
	
	.loading-text {
		font-size: 16px;
		color: #999;
		font-weight: 500;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 375px) {
	.header-gradient-bg {
		padding: 20px 16px;
		
		.header-content .header-top .task-title {
			font-size: 20px;
		}
		
		.task-badges {
			.official-badge, .priority-badge {
				padding: 4px 8px;
				font-size: 11px;
			}
		}
	}
	
	.price-highlight {
		padding: 16px;
		
		.price-section .price-value {
			font-size: 24px;
		}
		
		.status-section .remaining-count {
			font-size: 20px;
		}
	}
	
	.info-grid {
		gap: 12px;
		
		.info-item {
			padding: 12px;
		}
	}
	
	.bottom-actions {
		padding: 16px 12px;
		
		.action-group .action-btn {
			height: 48px;
			
			.btn-text {
				font-size: 14px;
			}
		}
	}
}

@media (min-width: 768px) {
	.detail-content {
		max-width: 600px;
		margin: 0 auto;
	}
	
	.price-highlight {
		.price-section .price-value {
			font-size: 32px;
		}
		
		.status-section .remaining-count {
			font-size: 28px;
		}
	}
	
	.info-grid {
		grid-template-columns: 1fr 1fr 1fr 1fr;
	}
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
	.task-detail {
		background-color: #1a1a1a;
	}
	
	.info-card, .desc-card, .requirements-card, .publisher-card, .record-card {
		background: linear-gradient(145deg, #2d2d2d 0%, #262626 100%);
		border-color: rgba(102, 126, 234, 0.2);
		
		.card-title {
			color: #ffffff;
		}
	}
	
	.desc-content, .requirements-content {
		color: #e0e0e0;
		background: rgba(102, 126, 234, 0.1);
	}
	
	.info-grid .info-item {
		background: rgba(102, 126, 234, 0.1);
		border-color: rgba(102, 126, 234, 0.2);
		
		.info-content {
			.info-label {
				color: #b0b0b0;
			}
			
			.info-value {
				color: #ffffff;
			}
		}
	}
	
	.bottom-actions {
		background: linear-gradient(to top, rgba(26, 26, 26, 0.98), rgba(26, 26, 26, 0.95));
		border-top-color: rgba(102, 126, 234, 0.2);
	}
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(to top, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.95));
	backdrop-filter: blur(20px);
	padding: 20px 16px;
	border-top: 1px solid rgba(102, 126, 234, 0.1);
	box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
	box-sizing: border-box;
	
	.action-group {
		display: flex;
		gap: 12px;
		
		.action-btn {
			flex: 1;
			height: 52px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 26px;
			position: relative;
			overflow: hidden;
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
			
			&::before {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				width: 0;
				height: 0;
				background: rgba(255, 255, 255, 0.3);
				border-radius: 50%;
				transform: translate(-50%, -50%);
				transition: all 0.6s ease;
			}
			
			&:active::before {
				width: 200px;
				height: 200px;
			}
			
			.btn-text {
				font-size: 15px;
				font-weight: 600;
				position: relative;
				z-index: 2;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
			}
			
			&.primary {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: white;
				
				&:active {
					transform: translateY(1px);
					box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
				}
				
				&.disabled {
					background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
					color: #adb5bd;
					box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
					
					&:active {
						transform: none;
					}
					
					&::before {
						display: none;
					}
				}
			}
			
			&.secondary {
				background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
				color: #495057;
				border: 2px solid rgba(102, 126, 234, 0.2);
				
				&:active {
					transform: translateY(1px);
					background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
					border-color: rgba(102, 126, 234, 0.3);
				}
			}
			
			&.warning {
				background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
				color: white;
				
				&:active {
					transform: translateY(1px);
					box-shadow: 0 2px 8px rgba(255, 167, 38, 0.4);
				}
			}
			
			&.success {
				background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
				color: white;
				
				&:active {
					transform: translateY(1px);
					box-shadow: 0 2px 8px rgba(102, 187, 106, 0.4);
				}
			}
			
			&.disabled {
				background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
				color: #adb5bd;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
				
				&:active {
					transform: none;
				}
				
				&::before {
					display: none;
				}
			}
		}
	}
}
</style>
