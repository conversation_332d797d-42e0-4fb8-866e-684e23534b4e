# 平台分销记录页面统计字段修复

## 问题描述

平台分销记录页面（MarketingDistributionSettingList.vue）中，当用户在左侧会员列表中选中某个会员时，右侧详情区域顶部显示的四个统计字段数据存在错误：

1. `直接推荐人昵称` - 显示不正确
2. `直接推荐人手机号` - 显示不正确  
3. `直接下级人数` - 统计数量错误
4. `间接下级人数` - 统计数量错误

## 问题根因分析

通过深度分析发现以下根本原因：

### 1. 推荐人手机号字段缺失
- **问题**：`findDistributionSettingList`接口的SQL查询中没有返回`promoterPhone`字段
- **影响**：推荐人手机号无法正确显示

### 2. 字段名不一致
- **问题**：表格数据使用`promoterName`，但树形数据期望`recommenderNickname`和`recommenderPhone`
- **影响**：不同选择方式显示的推荐人信息不一致

### 3. 树形数据缺少推荐人信息查询
- **问题**：`getMemberTree`接口的`buildMemberTreeNode`方法中没有查询推荐人信息
- **影响**：树形选择时推荐人信息为空

## 修复方案

采用**后端全面修复**方案，确保数据源的一致性和准确性。

## 修复内容

### 1. 修复findDistributionSettingList接口

**文件**：`jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/marketing/mapper/xml/MarketingDistributionSettingMapper.xml`

**修改内容**：
- 在SQL查询中添加`promoterPhone`字段查询
- 根据`promoter_type`的不同值，查询对应的推荐人手机号

**修改代码**：
```sql
(
    CASE
        ml.`promoter_type`
        WHEN 0 THEN (SELECT su.phone FROM sys_user su WHERE su.id = ml.`promoter`)
        WHEN 1 THEN (SELECT mls.phone FROM member_list mls WHERE mls.id = ml.`promoter`)
        WHEN 2 THEN '平台'
        ELSE (SELECT su.phone FROM sys_user su WHERE su.id = ml.`sys_user_id`)
    END
) AS promoterPhone,
```

**文件**：`jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/marketing/vo/MarketingDistributionSettingVO.java`

**修改内容**：
- 添加`promoterPhone`字段定义

### 2. 修复getMemberTree接口

**文件**：`jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/member/controller/MemberListController.java`

**修改内容**：
- 添加批量查询推荐人信息的方法`batchQueryPromoters`，避免N+1查询问题
- 创建优化版本的构建方法`buildMemberTreeNodeWithPromoter`
- 修改`getMemberTree`方法，使用批量查询优化性能

**核心优化**：
```java
// 批量查询推荐人信息，避免N+1查询问题
private Map<String, MemberList> batchQueryPromoters(List<MemberList> members) {
    // 收集所有需要查询的推荐人ID
    List<String> promoterIds = members.stream()
            .filter(member -> StringUtils.isNotBlank(member.getPromoter()) && "1".equals(member.getPromoterType()))
            .map(MemberList::getPromoter)
            .distinct()
            .collect(Collectors.toList());
    
    // 批量查询推荐人信息
    // ...
}
```

### 3. 统一字段命名规范

**文件**：`heartful-mall-web/src/views/marketing/MarketingDistributionSettingList.vue`

**修改内容**：
- 统一使用`promoterName`和`promoterPhone`字段
- 修改树形数据格式化逻辑
- 更新所有相关的字段映射

## 测试验证要点

### 1. 推荐人信息显示测试
- [ ] 表格选择会员时，推荐人昵称和手机号正确显示
- [ ] 树形选择会员时，推荐人昵称和手机号正确显示
- [ ] 不同推荐人类型（会员、平台、店铺）的显示正确

### 2. 下级统计数量测试
- [ ] 直接下级人数统计准确
- [ ] 间接下级人数统计准确（总下级 - 直接下级）
- [ ] 统计数量与实际会员层级关系一致

### 3. 性能测试
- [ ] 大量会员数据时，树形结构加载性能正常
- [ ] 批量查询优化生效，无N+1查询问题

### 4. 边界情况测试
- [ ] 推荐人被删除或状态异常时的处理
- [ ] 系统运营账号的特殊显示逻辑
- [ ] 无推荐人的会员显示"平台"

## 技术要点

### 1. 性能优化
- 使用批量查询避免N+1查询问题
- 实时查询确保数据准确性，不使用缓存

### 2. 数据一致性
- 统一前后端字段命名规范
- 确保不同数据源返回的字段结构一致

### 3. 向后兼容性
- 保持现有API接口的向后兼容
- 不影响其他模块的使用

## 部署说明

1. **后端部署**：重新编译并部署后端服务
2. **前端部署**：重新构建并部署前端应用
3. **数据验证**：部署后验证现有数据的完整性

## 风险评估

- **低风险**：修改范围明确，不影响核心业务逻辑
- **性能影响**：批量查询优化后，性能应有所提升
- **数据安全**：只涉及查询逻辑，不修改数据结构

## 完成标准

- [ ] 所有统计字段显示正确
- [ ] 性能测试通过
- [ ] 边界情况处理正常
- [ ] 用户验收通过
