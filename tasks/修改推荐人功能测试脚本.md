# 修改推荐人功能测试脚本

## 🎯 测试目标

验证会员分销设置列表中"修改推荐人"功能的完整性，包括：
- 前端界面交互
- 后端接口调用
- 业务规则验证
- 修改原因记录
- 数据一致性

## 📋 测试准备

### 1. 数据库准备

```sql
-- 1. 验证bindScene枚举值
SELECT item_text, item_value, description 
FROM sys_dict_item di
JOIN sys_dict d ON d.id = di.dict_id
WHERE d.dict_code = 'bind_scene' 
ORDER BY sort_order;

-- 2. 验证推荐人修改记录表
DESC member_promoter_change_log;

-- 3. 查看测试会员数据
SELECT 
    id,
    nick_name,
    phone,
    promoter,
    promoter_type,
    bind_scene,
    is_love_ambassador,
    unique_id
FROM member_list 
WHERE del_flag = '0'
LIMIT 5;
```

### 2. 接口测试准备

**测试会员ID**: `915b03061b686c6416b9dac8de1f3dbb`
**当前推荐人**: `db0bef2fc11a3f4727b771c24fe2b5e3` (老张)
**新推荐人**: `b609e7e5d4b6e9833158555783497319` (平台运营)

## 🧪 测试用例

### 测试用例1：查询助梦家列表接口

**接口**: `GET /sys/memberList/getDreamHelperList`

**测试参数**:
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "平台"
}
```

**预期结果**:
- 返回助梦家列表
- 支持昵称和手机号搜索
- 分页功能正常

**验证SQL**:
```sql
-- 验证助梦家数据
SELECT id, nick_name, phone, is_love_ambassador 
FROM member_list 
WHERE is_love_ambassador = '1' 
AND del_flag = '0'
AND (nick_name LIKE '%平台%' OR phone LIKE '%平台%');
```

### 测试用例2：修改推荐人接口 - 正常流程

**接口**: `PUT /sys/memberList/updatePromoterManually`

**测试参数**:
```json
{
  "memberId": "915b03061b686c6416b9dac8de1f3dbb",
  "newPromoterId": "b609e7e5d4b6e9833158555783497319",
  "reason": "业务调整需要修改推荐人关系，原推荐人不再适合"
}
```

**预期结果**:
- 返回成功消息
- 会员推荐人关系更新
- 会员层级和路径重新计算
- bindScene设置为"3"（手动调整）
- 记录修改日志

**验证SQL**:
```sql
-- 验证会员推荐人关系更新
SELECT 
    id,
    nick_name,
    promoter,
    promoter_type,
    member_level,
    member_path,
    bind_scene,
    bind_time
FROM member_list 
WHERE id = '915b03061b686c6416b9dac8de1f3dbb';

-- 验证修改日志记录
SELECT * FROM member_promoter_change_log 
WHERE member_id = '915b03061b686c6416b9dac8de1f3dbb'
ORDER BY change_time DESC;
```

### 测试用例3：业务规则验证 - 助梦家身份

**测试参数**:
```json
{
  "memberId": "915b03061b686c6416b9dac8de1f3dbb",
  "newPromoterId": "非助梦家会员ID",
  "reason": "测试非助梦家验证"
}
```

**预期结果**:
- 返回错误："推荐人必须是助梦家身份"

### 测试用例4：业务规则验证 - 自己推荐自己

**测试参数**:
```json
{
  "memberId": "915b03061b686c6416b9dac8de1f3dbb",
  "newPromoterId": "915b03061b686c6416b9dac8de1f3dbb",
  "reason": "测试自己推荐自己"
}
```

**预期结果**:
- 返回错误："不能设置自己为推荐人"

### 测试用例5：业务规则验证 - 循环推荐

**测试场景**: A推荐B，B推荐C，尝试让C推荐A

**预期结果**:
- 返回错误："不能设置下级会员为推荐人，会形成循环推荐关系"

### 测试用例6：前端表单验证

**测试场景**:
1. 不选择新推荐人 → 提示"请选择新推荐人"
2. 修改原因为空 → 提示"请输入修改原因"
3. 修改原因少于5个字符 → 提示"修改原因至少5个字符"
4. 修改原因超过200个字符 → 提示"修改原因不能超过200个字符"

## 🔍 前端测试步骤

### 1. 访问页面
- 打开浏览器访问：`http://localhost:3000/marketing/MarketingDistributionSettingList`
- 确认页面正常加载，会员列表显示正常

### 2. 操作测试
1. 在会员列表中找到测试会员（15960528452）
2. 点击操作列中的"修改推荐人"按钮
3. 确认弹窗正常打开，显示当前会员和推荐人信息
4. 在新推荐人下拉框中输入"平台"进行搜索
5. 选择"平台运营"作为新推荐人
6. 输入修改原因："业务调整需要修改推荐人关系"
7. 点击确定按钮提交

### 3. 结果验证
1. 确认提示"修改推荐人成功"
2. 确认弹窗自动关闭
3. 确认列表数据自动刷新
4. 查看会员的推荐人信息是否已更新

## 📊 性能测试

### 1. 搜索性能测试
- 在助梦家搜索框中快速输入多个字符
- 验证防抖机制（300ms延迟）
- 验证缓存机制（相同关键词直接返回缓存结果）

### 2. 并发测试
- 同时修改多个会员的推荐人
- 验证数据一致性
- 验证事务隔离性

## 🐛 异常情况测试

### 1. 网络异常
- 断网情况下操作
- 网络超时情况
- 服务器错误响应

### 2. 数据异常
- 会员不存在
- 推荐人不存在
- 数据库连接异常

## ✅ 测试检查清单

- [ ] bindScene枚举值"手动调整"已添加
- [ ] member_promoter_change_log表已创建
- [ ] 查询助梦家列表接口正常
- [ ] 修改推荐人接口正常
- [ ] 助梦家身份验证正常
- [ ] 自己推荐自己验证正常
- [ ] 循环推荐关系验证正常
- [ ] 前端表单验证正常
- [ ] 搜索防抖和缓存正常
- [ ] 修改原因正确记录
- [ ] 操作日志正确记录
- [ ] 数据一致性正常
- [ ] 错误处理友好
- [ ] 性能表现良好

## 📝 测试报告模板

### 测试环境
- 操作系统：
- 浏览器：
- 数据库版本：
- 后端服务版本：

### 测试结果
| 测试用例 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| 查询助梦家列表 | 正常返回列表 | | ✅/❌ | |
| 修改推荐人-正常流程 | 修改成功 | | ✅/❌ | |
| 助梦家身份验证 | 验证失败提示 | | ✅/❌ | |
| 自己推荐自己验证 | 验证失败提示 | | ✅/❌ | |
| 循环推荐验证 | 验证失败提示 | | ✅/❌ | |
| 前端表单验证 | 验证提示正确 | | ✅/❌ | |
| 修改原因记录 | 正确记录到数据库 | | ✅/❌ | |

### 发现问题
1. 
2. 
3. 

### 改进建议
1. 
2. 
3. 

## 🔧 问题修复

如发现问题，请按以下步骤修复：

1. **后端问题**：检查日志，修复业务逻辑
2. **前端问题**：检查控制台错误，修复交互逻辑
3. **数据库问题**：检查SQL执行，修复数据结构
4. **性能问题**：优化查询，添加索引

## 📞 技术支持

如有问题，请联系开发团队或查看相关文档：
- 技术方案：`/docs/tasks/tasks-会员分销设置列表添加修改推荐人功能.md`
- 演示说明：`/docs/tasks/会员分销设置列表修改推荐人功能-演示说明和测试结果.md`
