# heartful-mall 任务发布功能增强实施计划

## 📋 项目概述

**项目名称**：heartful-mall C端小程序任务发布功能增强 - 任务类型分类和动态截止时间逻辑
**实施方案**：方案一 - 渐进式增强（保持现有功能完全不变）
**技术栈**：Spring Boot + MyBatis Plus + Vue.js + UniApp + uv-ui
**开始时间**：2025-01-05
**预计工期**：280分钟（约4.7小时）
**负责人**：AI Assistant

## 🎯 核心目标

### 功能目标
1. **任务类型分类**：新增短期任务/长期任务选择
2. **动态截止时间**：长期任务支持"几天内完成"逻辑  
3. **智能时间显示**：任务列表根据类型显示合适的时间信息
4. **向后兼容**：现有功能和数据完全不受影响

### 技术目标
- 数据库新增2个字段，保持向后兼容
- 前端表单动态显示，用户体验优化
- 任务展示页面智能时间计算
- 确保微信小程序兼容性

## 🔧 核心技术设计

### 数据库设计
```sql
-- 新增字段（向后兼容）
ALTER TABLE task_publish ADD COLUMN task_type VARCHAR(10) DEFAULT '1' COMMENT '任务类型：1-短期任务，2-长期任务';
ALTER TABLE task_publish ADD COLUMN days_to_complete INT NULL COMMENT '几天内完成（仅长期任务）';
```

### 实际截止时间计算逻辑
```javascript
// 核心算法：实际截止时间 = min(deadline, 当前时间 + daysToComplete天)
calculateActualDeadline(task) {
  if (task.taskType === '1') {
    // 短期任务：直接使用deadline
    return task.deadline;
  } else {
    // 长期任务：取较早时间
    const deadlineFromDays = new Date(Date.now() + task.daysToComplete * 24 * 60 * 60 * 1000);
    return new Date(task.deadline) < deadlineFromDays ? task.deadline : deadlineFromDays;
  }
}
```

### 前端表单设计
- 使用uv-radio-group选择任务类型（短期任务默认选中）
- 动态显示"几天内完成"字段（仅长期任务）
- 实时预览实际截止时间
- 表单验证规则动态调整

## 📊 详细实施计划

### 阶段1：数据库改造（40分钟）
**状态**：[ ] 未开始
**优先级**：最高
**依赖关系**：无

#### 任务1.1：编写数据库变更脚本（15分钟）
**文件路径**：`/Users/<USER>/Dev/SideWork/heartful-mall/docs/sql/增量脚本/task_type_enhancement.sql`
**修改内容**：
```sql
-- 任务类型增强脚本
-- 新增任务类型字段
ALTER TABLE task_publish ADD COLUMN task_type VARCHAR(10) DEFAULT '1' COMMENT '任务类型：1-短期任务，2-长期任务';

-- 新增完成天数字段
ALTER TABLE task_publish ADD COLUMN days_to_complete INT NULL COMMENT '几天内完成（仅长期任务）';

-- 为新字段添加索引
CREATE INDEX idx_task_publish_task_type ON task_publish(task_type);
```

#### 任务1.2：创建任务类型字典配置（10分钟）
**文件路径**：`/Users/<USER>/Dev/SideWork/heartful-mall/docs/sql/增量脚本/dict_task_type.sql`
**修改内容**：
```sql
-- 任务类型字典配置
INSERT INTO sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, type) 
VALUES (REPLACE(UUID(), '-', ''), '任务类型', 'task_type', '任务类型字典', 0, 'admin', NOW(), 0);

-- 字典项配置
INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time) VALUES
(REPLACE(UUID(), '-', ''), (SELECT id FROM sys_dict WHERE dict_code = 'task_type'), '短期任务', '1', '短期任务', 1, 1, 'admin', NOW()),
(REPLACE(UUID(), '-', ''), (SELECT id FROM sys_dict WHERE dict_code = 'task_type'), '长期任务', '2', '长期任务', 2, 1, 'admin', NOW());
```

#### 任务1.3：编写数据迁移脚本（15分钟）
**文件路径**：`/Users/<USER>/Dev/SideWork/heartful-mall/docs/sql/增量脚本/task_type_migration.sql`
**修改内容**：
```sql
-- 数据迁移脚本：现有任务设为短期任务
UPDATE task_publish SET task_type = '1' WHERE task_type IS NULL;

-- 验证数据迁移结果
SELECT 
  COUNT(*) as total_tasks,
  COUNT(CASE WHEN task_type = '1' THEN 1 END) as short_term_tasks,
  COUNT(CASE WHEN task_type = '2' THEN 1 END) as long_term_tasks
FROM task_publish;
```

### 阶段2：后端API改造（60分钟）
**状态**：[ ] 未开始
**优先级**：高
**依赖关系**：依赖阶段1完成

#### 任务2.1：更新TaskPublish实体类（20分钟）
**文件路径**：`jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/taskPublish/entity/TaskPublish.java`
**修改内容**：
```java
// 新增字段
@Excel(name = "任务类型", width = 15, dicCode = "task_type")
@Dict(dicCode = "task_type")
@ApiModelProperty(value = "任务类型：1-短期任务，2-长期任务")
private String taskType;

@Excel(name = "几天内完成", width = 15)
@ApiModelProperty(value = "几天内完成（天数）")
private Integer daysToComplete;
```

#### 任务2.2：新增实际截止时间计算服务（20分钟）
**文件路径**：`jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/taskPublish/service/impl/TaskPublishServiceImpl.java`
**修改内容**：
```java
/**
 * 计算实际截止时间
 * @param task 任务对象
 * @return 实际截止时间
 */
public Date calculateActualDeadline(TaskPublish task) {
    if ("1".equals(task.getTaskType()) || task.getDaysToComplete() == null) {
        // 短期任务或天数为空：直接使用deadline
        return task.getDeadline();
    } else {
        // 长期任务：取deadline和(当前时间+天数)的较小值
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, task.getDaysToComplete());
        Date deadlineFromDays = cal.getTime();
        return task.getDeadline().before(deadlineFromDays) ? task.getDeadline() : deadlineFromDays;
    }
}
```

#### 任务2.3：更新API接口和测试（20分钟）
**文件路径**：`jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/task/api/AfterTaskController.java`
**修改内容**：
- 发布任务接口增加新字段验证
- 任务列表接口返回计算后的实际截止时间
- 添加单元测试验证计算逻辑

### 阶段3：前端表单改造（80分钟）
**状态**：[ ] 未开始
**优先级**：中
**依赖关系**：依赖阶段2完成

#### 任务3.1：新增任务类型选择组件（20分钟）
**文件路径**：`heartful-mall-app/pages/task/publish.vue`
**修改内容**：
```vue
<!-- 任务类型选择 -->
<uv-form-item label="任务类型" prop="taskType" required>
  <uv-radio-group v-model="formData.taskType" @change="onTaskTypeChange">
    <uv-radio name="1" :custom-style="radioStyle">短期任务</uv-radio>
    <uv-radio name="2" :custom-style="radioStyle">长期任务</uv-radio>
  </uv-radio-group>
  <view class="input-hint">
    <text v-if="formData.taskType === '1'">短期任务：直接设置最终截止时间</text>
    <text v-else>长期任务：可设置完成期限，系统将取较早时间作为实际截止时间</text>
  </view>
</uv-form-item>
```

#### 任务3.2：实现动态表单显示逻辑（25分钟）
**修改内容**：
```vue
<!-- 几天内完成（仅长期任务显示） -->
<transition name="slide-fade">
  <uv-form-item v-if="formData.taskType === '2'" label="几天内完成" prop="daysToComplete" required>
    <uv-input
      v-model="formData.daysToComplete"
      type="number"
      placeholder="请输入天数（1-365）"
      @input="onDaysChange"
      :custom-style="inputStyle"
    />
    <text class="unit-text">天</text>
  </uv-form-item>
</transition>

<!-- 实际截止时间预览 -->
<view v-if="showDeadlinePreview" class="deadline-preview">
  <text class="preview-label">实际截止时间：</text>
  <text class="preview-value">{{ getPreviewDeadline() }}</text>
</view>
```

#### 任务3.3：更新表单验证规则（20分钟）
**修改内容**：
```javascript
formRules() {
  const rules = {
    // 现有规则...
    taskType: [
      { required: true, message: '请选择任务类型', trigger: 'change' }
    ]
  };
  
  // 长期任务时添加天数验证
  if (this.formData.taskType === '2') {
    rules.daysToComplete = [
      { required: true, message: '请输入完成天数', trigger: 'blur' },
      { type: 'number', min: 1, max: 365, message: '天数范围1-365天', trigger: 'blur' }
    ];
  }
  
  return rules;
}
```

#### 任务3.4：添加用户体验优化（15分钟）
**修改内容**：
- 添加表单切换动画效果
- 实时预览实际截止时间
- 优化提示文案和交互反馈
- 添加表单重置逻辑

### 阶段4：前端展示改造（60分钟）
**状态**：[ ] 未开始
**优先级**：中
**依赖关系**：依赖阶段2完成，可与阶段3并行

#### 任务4.1：更新TaskHall.vue时间显示（30分钟）
**文件路径**：`heartful-mall-app/pages/task/components/TaskHall.vue`
**修改内容**：
```vue
<!-- 截止时间显示 -->
<view class="task-deadline">
  <text class="deadline-label">截止时间：</text>
  <text class="deadline-value">{{ getDisplayDeadline(task) }}</text>
  <text v-if="task.taskType === '2'" class="task-type-badge">长期</text>
</view>
```

```javascript
// 智能时间显示方法
getDisplayDeadline(task) {
  if (task.taskType === '1' || !task.daysToComplete) {
    // 短期任务：直接显示deadline
    return this.formatDateTime(task.deadline);
  } else {
    // 长期任务：显示计算后的实际截止时间
    const actualDeadline = this.calculateActualDeadline(task);
    return this.formatDateTime(actualDeadline) + ` (${task.daysToComplete}天内)`;
  }
}
```

#### 任务4.2：更新MyPublishedTasks.vue时间显示（30分钟）
**文件路径**：`heartful-mall-app/pages/task/components/MyPublishedTasks.vue`
**修改内容**：
```vue
<!-- 详细时间信息显示 -->
<view class="task-time-info">
  <view class="deadline-row">
    <text class="time-label">截止时间：</text>
    <text class="time-value">{{ formatDateTime(task.deadline) }}</text>
  </view>
  <view v-if="task.taskType === '2' && task.daysToComplete" class="days-row">
    <text class="time-label">完成期限：</text>
    <text class="time-value">{{ task.daysToComplete }}天内</text>
  </view>
  <view class="actual-deadline-row">
    <text class="time-label">实际截止：</text>
    <text class="time-value highlight">{{ getDisplayDeadline(task) }}</text>
  </view>
</view>
```

### 阶段5：测试和优化（40分钟）
**状态**：[ ] 未开始
**优先级**：低
**依赖关系**：依赖前面所有阶段完成

#### 任务5.1：功能测试和bug修复（25分钟）
**测试内容**：
- 短期任务发布和显示功能
- 长期任务发布和时间计算
- 表单验证和错误处理
- 任务列表时间显示正确性
- 向后兼容性验证

#### 任务5.2：兼容性测试和文档更新（15分钟）
**测试内容**：
- 微信小程序兼容性测试
- 不同屏幕尺寸适配测试
- 现有功能回归测试
- 更新技术文档和用户说明

## 🔍 技术风险评估（自省姐深度分析）

### 风险等级：中等（经自省分析上调）

#### 高风险项
1. **数据一致性风险**：中高（高并发下可能出现数据不一致）
2. **时间计算边界风险**：中（时区、负数、边界值处理）
3. **前端状态管理风险**：中（动态表单验证复杂性）

#### 中等风险项
4. **API向前兼容性风险**：中（老版本前端可能显示异常）
5. **工期估算风险**：中（未考虑review、部署、返工时间）
6. **依赖关系风险**：中（隐性依赖可能影响并行开发）

#### 低风险项
7. **用户体验风险**：低（保持现有功能，新增功能可选）

### 缓解措施（增强版）

#### 数据安全措施
- 数据库变更使用事务确保原子性
- 变更前完整备份，制定回滚方案
- 增加数据校验层，防止异常数据

#### 边界处理措施
```javascript
// 时间计算边界处理
validateTimeLogic(task) {
  // 检查天数范围
  if (task.daysToComplete && (task.daysToComplete <= 0 || task.daysToComplete > 365)) {
    throw new Error('完成天数必须在1-365天范围内');
  }

  // 检查截止时间
  if (new Date(task.deadline) <= new Date()) {
    throw new Error('截止时间不能早于当前时间');
  }

  // 时区统一处理
  return this.normalizeTimezone(task);
}
```

#### 兼容性保障措施
- API版本控制或字段过滤机制
- 前端降级策略：新字段查询失败时使用原有逻辑
- 增加兼容性检查接口

#### 监控和日志措施
- 时间计算逻辑详细日志记录
- 新功能使用情况埋点统计
- 异常情况告警机制

#### 工期风险控制
- 预留20%缓冲时间用于review和返工
- 制定详细的测试计划和验收标准
- 建立每日进度检查机制

## 📈 预期效果

### 功能效果
- 用户可以选择短期/长期任务类型
- 长期任务支持灵活的时间设置
- 任务列表智能显示截止时间
- 现有功能完全不受影响

### 技术效果
- 代码结构清晰，易于维护
- 向后兼容性好，升级平滑
- 用户体验提升，操作更灵活
- 为未来功能扩展奠定基础

## 📝 关键技术实施细节

### 数据库变更脚本
**执行顺序**：严格按照以下顺序执行
1. `task_type_enhancement.sql` - 表结构变更
2. `dict_task_type.sql` - 字典配置
3. `task_type_migration.sql` - 数据迁移

### API接口说明
**新增字段说明**：
- `taskType`: String类型，必填，默认值"1"
- `daysToComplete`: Integer类型，可选，仅长期任务时必填

**接口兼容性**：
- 现有接口保持不变，新增字段为可选
- 返回数据增加新字段，老版本前端忽略即可

### 前端组件设计
**uv-ui组件使用**：
- `uv-radio-group`: 任务类型选择
- `uv-input`: 天数输入（type="number"）
- `transition`: 动态表单切换动画

**响应式设计**：
- 适配不同屏幕尺寸
- 微信小程序兼容性验证
- 触摸交互优化

### 时间计算工具方法
```javascript
// 通用时间计算工具
export const TaskTimeUtils = {
  // 计算实际截止时间
  calculateActualDeadline(task) {
    if (task.taskType === '1' || !task.daysToComplete) {
      return new Date(task.deadline);
    }

    const deadlineFromDays = new Date(Date.now() + task.daysToComplete * 24 * 60 * 60 * 1000);
    const originalDeadline = new Date(task.deadline);

    return originalDeadline < deadlineFromDays ? originalDeadline : deadlineFromDays;
  },

  // 格式化时间显示
  formatDeadlineDisplay(task) {
    const actualDeadline = this.calculateActualDeadline(task);
    const formatted = this.formatDateTime(actualDeadline);

    if (task.taskType === '2' && task.daysToComplete) {
      return `${formatted} (${task.daysToComplete}天内)`;
    }

    return formatted;
  },

  // 验证时间逻辑
  validateTimeLogic(task) {
    const errors = [];

    if (task.daysToComplete && (task.daysToComplete <= 0 || task.daysToComplete > 365)) {
      errors.push('完成天数必须在1-365天范围内');
    }

    if (new Date(task.deadline) <= new Date()) {
      errors.push('截止时间不能早于当前时间');
    }

    return errors;
  }
};
```

## 🎯 验收标准

### 功能验收
- [ ] 用户可以选择短期/长期任务类型
- [ ] 长期任务可以设置"几天内完成"
- [ ] 时间计算逻辑正确（取较早时间）
- [ ] 任务列表正确显示截止时间
- [ ] 现有功能完全不受影响

### 技术验收
- [ ] 数据库字段正确添加，索引创建成功
- [ ] API接口向后兼容，新字段正确返回
- [ ] 前端表单验证逻辑正确
- [ ] 微信小程序兼容性测试通过
- [ ] 代码质量符合项目规范

### 用户体验验收
- [ ] 表单切换动画流畅
- [ ] 提示文案清晰易懂
- [ ] 错误处理友好
- [ ] 响应式设计适配良好

---

**文档版本**：v2.0（自省分析增强版）
**最后更新**：2025-01-05
**项目状态**：计划阶段（已完成深度风险分析）
**文档路径**：`/Users/<USER>/Dev/SideWork/heartful-mall/docs/tasks/tasks-任务发布功能增强-任务类型分类和动态截止时间.md`
