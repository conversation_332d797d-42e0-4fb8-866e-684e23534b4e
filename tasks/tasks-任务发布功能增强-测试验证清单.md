# heartful-mall 任务发布功能增强 - 测试验证清单

## 📋 测试概述

**测试目标**：验证任务类型分类和动态截止时间逻辑的完整功能
**测试范围**：数据库、后端API、前端表单、前端展示
**测试环境**：开发环境
**测试时间**：2025-01-05

## 🔧 数据库测试

### 1. 表结构验证
- [ ] task_publish表新增字段验证
  - [ ] task_type字段：VARCHAR(10)，默认值'1'
  - [ ] days_to_complete字段：INT，允许NULL
- [ ] task_acceptance_record表新增字段验证
  - [ ] actual_deadline字段：DATETIME，允许NULL
- [ ] 索引创建验证
  - [ ] idx_task_publish_task_type
  - [ ] idx_acceptance_actual_deadline
- [ ] 字典配置验证
  - [ ] task_type字典存在且正确
  - [ ] 字典项：1-短期任务，2-长期任务

### 2. 数据完整性测试
```sql
-- 验证字段存在
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name IN ('task_publish', 'task_acceptance_record') 
AND column_name IN ('task_type', 'days_to_complete', 'actual_deadline');

-- 验证字典配置
SELECT sd.dict_name, sdi.item_text, sdi.item_value 
FROM sys_dict sd 
JOIN sys_dict_item sdi ON sd.id = sdi.dict_id 
WHERE sd.dict_code = 'task_type';
```

## 🚀 后端API测试

### 1. 任务发布接口测试
**接口**：POST /after/task/publish

**测试用例1：短期任务发布**
```json
{
  "title": "测试短期任务",
  "description": "这是一个短期任务测试",
  "taskRequirements": "按要求完成即可",
  "totalCount": 5,
  "unitPrice": 10.00,
  "deadline": "2025-01-10 18:00:00",
  "taskType": "1"
}
```
**预期结果**：
- [ ] 任务创建成功
- [ ] task_type字段保存为'1'
- [ ] days_to_complete字段为NULL

**测试用例2：长期任务发布**
```json
{
  "title": "测试长期任务",
  "description": "这是一个长期任务测试",
  "taskRequirements": "需要较长时间完成",
  "totalCount": 3,
  "unitPrice": 50.00,
  "deadline": "2025-01-20 18:00:00",
  "taskType": "2",
  "daysToComplete": 7
}
```
**预期结果**：
- [ ] 任务创建成功
- [ ] task_type字段保存为'2'
- [ ] days_to_complete字段保存为7

### 2. 任务接受接口测试
**接口**：POST /after/task/accept

**测试用例1：接受短期任务**
- [ ] 接受记录创建成功
- [ ] actual_deadline = 原始deadline

**测试用例2：接受长期任务**
- [ ] 接受记录创建成功
- [ ] actual_deadline = min(原始deadline, 接受时间 + 7天)

### 3. 时间计算逻辑验证
```java
// 测试calculateActualDeadline方法
@Test
public void testCalculateActualDeadline() {
    // 短期任务测试
    TaskPublish shortTask = new TaskPublish();
    shortTask.setTaskType("1");
    shortTask.setDeadline(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000));
    
    Date result = taskPublishService.calculateActualDeadline(shortTask, new Date());
    assertEquals(shortTask.getDeadline(), result);
    
    // 长期任务测试
    TaskPublish longTask = new TaskPublish();
    longTask.setTaskType("2");
    longTask.setDaysToComplete(3);
    longTask.setDeadline(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000));
    
    Date acceptTime = new Date();
    Date result2 = taskPublishService.calculateActualDeadline(longTask, acceptTime);
    // 应该返回接受时间+3天（较早）
}
```

## 📱 前端功能测试

### 1. 任务发布页面测试
**页面**：pages/task/publish.vue

**测试用例1：短期任务发布流程**
- [ ] 默认选中"短期任务"
- [ ] 只显示截止时间字段
- [ ] 不显示"几天内完成"字段
- [ ] 表单验证正确
- [ ] 提交成功

**测试用例2：长期任务发布流程**
- [ ] 选择"长期任务"
- [ ] 显示截止时间和"几天内完成"字段
- [ ] 动画切换流畅
- [ ] 实时预览实际截止时间
- [ ] 表单验证正确（天数1-365）
- [ ] 提交成功

**测试用例3：表单验证测试**
- [ ] 任务类型必选验证
- [ ] 长期任务天数必填验证
- [ ] 天数范围验证（1-365）
- [ ] 截止时间不能早于当前时间

### 2. 任务展示页面测试

**TaskHall.vue（任务大厅）**
- [ ] 短期任务显示原始截止时间
- [ ] 长期任务显示动态计算的截止时间
- [ ] 长期任务显示"长期"标识
- [ ] 时间状态颜色正确（正常/警告/紧急/过期）
- [ ] 倒计时显示正确

**MyPublishedTasks.vue（我发布的任务）**
- [ ] 时间显示与TaskHall一致
- [ ] 长期任务显示完成期限信息
- [ ] 长期任务标识显示
- [ ] 时间格式化正确

**MyPendingAuditTasks.vue（审核台）**
- [ ] 显示实际截止时间
- [ ] 过期时间红色显示
- [ ] 时间格式化正确

## 🔍 兼容性测试

### 1. 向后兼容性测试
- [ ] 现有任务（task_type为NULL）正常显示
- [ ] 现有API调用不受影响
- [ ] 老版本前端可以正常工作

### 2. 微信小程序兼容性测试
- [ ] uv-ui组件正常显示
- [ ] 表单交互正常
- [ ] 时间计算正确
- [ ] 样式适配正常

### 3. 不同屏幕尺寸测试
- [ ] iPhone SE (375px)
- [ ] iPhone 12 (390px)
- [ ] iPad (768px)

## 🎯 性能测试

### 1. 时间计算性能
- [ ] 大量任务列表加载性能
- [ ] 时间计算方法执行效率
- [ ] 内存使用情况

### 2. 数据库查询性能
- [ ] 新增索引查询效率
- [ ] 复杂查询性能
- [ ] 并发访问测试

## 🐛 边界情况测试

### 1. 异常数据处理
- [ ] task_type为空或无效值
- [ ] days_to_complete为0或负数
- [ ] deadline为过去时间
- [ ] 网络异常处理

### 2. 极端场景测试
- [ ] days_to_complete为365天
- [ ] deadline与当前时间相差很小
- [ ] 同时发布大量任务

## ✅ 测试结果记录

### 通过的测试
- [ ] 数据库结构正确
- [ ] 后端API功能正常
- [ ] 前端表单交互正常
- [ ] 前端展示逻辑正确
- [ ] 兼容性测试通过

### 发现的问题
（记录测试过程中发现的问题）

### 修复的问题
（记录已修复的问题）

## 📊 测试总结

**测试完成度**：____%
**发现问题数**：____个
**已修复问题**：____个
**待修复问题**：____个

**整体评估**：
- [ ] 功能完整性：优秀/良好/一般/需改进
- [ ] 性能表现：优秀/良好/一般/需改进
- [ ] 用户体验：优秀/良好/一般/需改进
- [ ] 兼容性：优秀/良好/一般/需改进

**建议**：
（测试完成后的改进建议）

---

**测试负责人**：AI Assistant
**测试时间**：2025-01-05
**文档版本**：v1.0
