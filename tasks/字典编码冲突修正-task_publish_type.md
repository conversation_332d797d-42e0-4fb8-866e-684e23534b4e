# 字典编码冲突修正 - task_publish_type

## 📋 问题概述

**发现时间**：2025-01-06  
**问题描述**：任务发布模块与营销积分任务模块都使用了相同的字典编码 `task_type`，导致字典配置冲突  
**影响范围**：任务发布功能的字典显示错误，数据混乱  
**解决方案**：为任务发布模块创建独立的字典编码 `task_publish_type`

## 🚨 冲突详情

### 冲突的字典使用情况

#### 1. 营销积分任务模块（已存在）
- **字典编码**：`task_type`
- **使用文件**：
  - `MarketingIntegralTask.java`
  - `MarketingIntegralTaskRecord.java`
  - `MarketingIntegralTaskRecordVO.java`
- **字典值**：
  - 0：用户注册
  - 1：交易密码
  - 2：连续签到
  - 3：每日浏览
  - 4：邀请签到
  - 5：邀请注册
  - 6：身份认证
  - 7：每日签到
  - 8：好友首单
  - 9：观看广告

#### 2. 任务发布模块（新开发）
- **字典编码**：`task_type`（冲突）
- **使用文件**：
  - `TaskPublish.java`
  - `TaskPublishList.vue`
  - `TaskAcceptanceRecordList.vue`
- **字典值**：
  - 1：短期任务
  - 2：长期任务

## 🔧 解决方案实施

### 1. 新字典配置
- **新字典编码**：`task_publish_type`
- **字典名称**：任务发布类型
- **配置文件**：`sql/增量脚本/dict_task_publish_type.sql`

### 2. 需要更新的代码文件

#### 2.1 后端实体类
**文件**：`../heartful-mall/jeecg-module-system/jeecg-system-biz/src/main/java/org/jeecg/modules/taskPublish/entity/TaskPublish.java`

**修改内容**：
```java
// 修改前
@Excel(name = "任务类型", width = 15, dicCode = "task_type")
@Dict(dicCode = "task_type")
@ApiModelProperty(value = "任务类型：1-短期任务，2-长期任务")
private String taskType;

// 修改后
@Excel(name = "任务类型", width = 15, dicCode = "task_publish_type")
@Dict(dicCode = "task_publish_type")
@ApiModelProperty(value = "任务类型：1-短期任务，2-长期任务")
private String taskType;
```

#### 2.2 前端管理后台列表页
**文件**：`../heartful-mall-web/src/views/task/TaskPublishList.vue`

**修改内容**：
```vue
<!-- 修改前 -->
<j-dict-select-tag placeholder="请选择任务类型" v-model="queryParam.taskType" dictCode="task_type"/>

<!-- 修改后 -->
<j-dict-select-tag placeholder="请选择任务类型" v-model="queryParam.taskType" dictCode="task_publish_type"/>
```

#### 2.3 前端管理后台接受记录页
**文件**：`../heartful-mall-web/src/views/task/TaskAcceptanceRecordList.vue`

**修改内容**：
```javascript
// 修改前
const taskType = record.taskType || '1' // 向后兼容
const typeText = taskType === '2' ? '长期任务' : '短期任务'

// 修改后（使用字典翻译）
// 这部分代码需要通过后端关联查询获取正确的字典文本
// 或者在后端返回数据时已经包含字典翻译
```

#### 2.4 C端小程序发布页面
**文件**：`../heartful-mall-app/pages/task/publish.vue`

**修改内容**：
```javascript
// 如果有使用字典的地方，需要更新字典编码
// 检查是否有类似以下的代码：
// uni.getDictItems('task_type') 
// 改为：
// uni.getDictItems('task_publish_type')
```

### 3. 数据库执行顺序
1. **执行新字典脚本**：`dict_task_publish_type.sql`
2. **验证字典配置**：确认新字典和字典项创建成功
3. **更新代码**：按照上述文件列表逐一修改
4. **测试验证**：确保字典显示正确

## ✅ 修正完成情况

### 数据库修正 ✅
- ✅ 创建 `dict_task_publish_type.sql` 脚本
- ✅ 删除冲突的 `dict_task_type.sql` 脚本
- ✅ 新字典编码：`task_publish_type`
- ✅ 字典项配置：1-短期任务，2-长期任务

### 后端代码修正 ✅
- ✅ 更新 `TaskPublish.java` 实体类字典注解
  - 从 `@Dict(dicCode = "task_type")` 改为 `@Dict(dicCode = "task_publish_type")`
  - 从 `dicCode = "task_type"` 改为 `dicCode = "task_publish_type"`

### 前端代码修正 ✅
- ✅ 更新管理后台 `TaskPublishList.vue`
  - 从 `dictCode="task_type"` 改为 `dictCode="task_publish_type"`
- ✅ 检查C端小程序相关页面
  - `pages/task/publish.vue` 使用硬编码，无需修改
  - 其他页面未发现字典依赖

### 验证清单
- [ ] 执行 `dict_task_publish_type.sql` 脚本
- [ ] 确认 `task_publish_type` 字典创建成功
- [ ] 确认字典项（1-短期任务，2-长期任务）创建成功
- [ ] 重新编译后端项目
- [ ] 测试任务发布API接口
- [ ] 验证字典翻译功能正常
- [ ] 测试管理后台字典选择组件
- [ ] 确认营销模块功能不受影响

## 🔄 回滚方案

如果修正过程中出现问题，可以按以下步骤回滚：

1. **数据库回滚**：
```sql
-- 删除新创建的字典
START TRANSACTION;
DELETE FROM sys_dict_item WHERE dict_id IN (SELECT id FROM sys_dict WHERE dict_code = 'task_publish_type');
DELETE FROM sys_dict WHERE dict_code = 'task_publish_type';
COMMIT;
```

2. **代码回滚**：
   - 将所有 `task_publish_type` 改回 `task_type`
   - 重新编译和部署

## 📝 注意事项

1. **执行顺序**：必须先执行数据库脚本，再更新代码
2. **测试环境**：建议先在测试环境验证，再部署到生产环境
3. **备份数据**：执行前备份相关数据库表
4. **影响范围**：此修改只影响任务发布模块，不影响营销模块
5. **向后兼容**：新字典编码确保了模块间的独立性

## 🎯 预期效果

修正完成后：
- ✅ 任务发布模块使用独立的 `task_publish_type` 字典
- ✅ 营销模块继续使用原有的 `task_type` 字典
- ✅ 两个模块互不干扰，字典显示正确
- ✅ 符合heartful-mall项目字典设计规范
