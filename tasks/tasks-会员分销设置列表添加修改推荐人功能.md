# 会员分销设置列表添加"修改推荐人"功能技术方案

## 📋 需求概述

在 `MarketingDistributionSettingList.vue` 页面的会员列表中新增"修改推荐人"按钮，允许管理员手动调整指定会员的直接推荐人关系。

## 🔍 调研结果

### 数据模型分析
- **bindScene字段**：现有值为"0：被推荐人注册；1：被推荐人兑换产品；2：被推荐人充值助力值"
- **isLoveAmbassador字段**：助梦家身份标识（"0"=否，"1"=是）
- **updatePromoter方法**：已存在于MemberListMapper.xml，可直接复用
- **setPromoter方法**：已存在完整的推荐人设置逻辑

### 业务规则确认
1. 推荐人必须是助梦家（`isLoveAmbassador = "1"`）
2. 需要更新字段：promoter、promoterType、memberLevel、memberPath、bindTime、bindScene
3. 支持分销门槛设置（无门槛/需成为助梦家）
4. 需要可追溯性标识（手动调整）

## 🎯 技术方案设计

### 1. 数据库扩展方案

```sql
-- 扩展bindScene数据字典，新增手动调整枚举值
INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time) 
VALUES (
  REPLACE(UUID(), '-', ''), 
  (SELECT id FROM sys_dict WHERE dict_code = 'bind_scene'), 
  '手动调整', 
  '3', 
  '管理员手动调整推荐人关系', 
  4, 
  1, 
  'admin', 
  NOW()
);
```

### 2. 后端接口设计

#### A. 查询助梦家列表接口
```java
/**
 * 查询助梦家列表（用于推荐人选择）
 */
@AutoLog(value = "查询助梦家列表")
@ApiOperation(value = "查询助梦家列表", notes = "查询助梦家列表")
@GetMapping("/getDreamHelperList")
public Result<IPage<MemberList>> getDreamHelperList(
    @RequestParam(defaultValue = "1") Integer pageNo,
    @RequestParam(defaultValue = "10") Integer pageSize,
    @RequestParam(required = false) String keyword
) {
    Page<MemberList> page = new Page<>(pageNo, pageSize);
    LambdaQueryWrapper<MemberList> queryWrapper = new LambdaQueryWrapper<MemberList>()
        .eq(MemberList::getIsLoveAmbassador, "1") // 必须是助梦家
        .eq(MemberList::getDelFlag, "0"); // 未删除

    if (StringUtils.isNotBlank(keyword)) {
        queryWrapper.and(wrapper -> wrapper
            .like(MemberList::getNickName, keyword)
            .or()
            .like(MemberList::getPhone, keyword)
        );
    }

    IPage<MemberList> pageList = memberListService.page(page, queryWrapper);
    return Result.OK(pageList);
}
```

#### B. 修改推荐人接口
```java
/**
 * 手动修改推荐人
 */
@AutoLog(value = "手动修改推荐人")
@ApiOperation(value = "手动修改推荐人", notes = "手动修改推荐人")
@RequestMapping(value = "/updatePromoterManually", method = {RequestMethod.PUT, RequestMethod.POST})
public Result<String> updatePromoterManually(@RequestBody UpdatePromoterRequest request) {
    try {
        // 1. 参数验证
        if (StringUtils.isBlank(request.getMemberId()) || StringUtils.isBlank(request.getNewPromoterId())) {
            return Result.error("参数不能为空");
        }
        
        // 2. 获取会员信息
        MemberList member = memberListService.getById(request.getMemberId());
        if (member == null) {
            return Result.error("会员不存在");
        }
        
        // 3. 验证新推荐人
        MemberList newPromoter = memberListService.getById(request.getNewPromoterId());
        if (newPromoter == null) {
            return Result.error("推荐人不存在");
        }
        
        // 4. 验证推荐人资格（必须是助梦家）
        if (!"1".equals(newPromoter.getIsLoveAmbassador())) {
            return Result.error("推荐人必须是助梦家身份");
        }
        
        // 5. 验证不能自己推荐自己
        if (request.getMemberId().equals(request.getNewPromoterId())) {
            return Result.error("不能设置自己为推荐人");
        }
        
        // 6. 验证不会形成循环推荐关系
        if (isCircularReference(member, newPromoter)) {
            return Result.error("不能设置下级会员为推荐人，会形成循环推荐关系");
        }
        
        // 7. 更新推荐人关系
        member.setPromoterType("1");
        member.setPromoter(request.getNewPromoterId());
        member.setMemberLevel(newPromoter.getMemberLevel() + 1);
        member.setMemberPath(newPromoter.getMemberPath() + "->" + member.getUniqueId());
        member.setBindTime(new Date());
        member.setBindScene("3"); // 手动调整
        
        memberListService.updatePromoter(member);
        
        // 8. 记录操作日志
        log.info("手动修改推荐人：会员ID={}, 原推荐人={}, 新推荐人={}, 操作员={}, 原因={}", 
            request.getMemberId(), member.getPromoter(), request.getNewPromoterId(), 
            SecurityUtils.getCurrentUsername(), request.getReason());
        
        return Result.OK("修改推荐人成功");
        
    } catch (Exception e) {
        log.error("修改推荐人失败", e);
        return Result.error("修改推荐人失败：" + e.getMessage());
    }
}

/**
 * 检查是否会形成循环推荐关系
 */
private boolean isCircularReference(MemberList member, MemberList newPromoter) {
    // 检查新推荐人的路径中是否包含当前会员
    if (StringUtils.isNotBlank(newPromoter.getMemberPath()) && member.getUniqueId() != null) {
        return newPromoter.getMemberPath().contains(member.getUniqueId().toString());
    }
    return false;
}
```

#### C. 请求参数类
```java
@Data
public class UpdatePromoterRequest {
    @ApiModelProperty("会员ID")
    private String memberId;
    
    @ApiModelProperty("新推荐人ID")
    private String newPromoterId;
    
    @ApiModelProperty("修改原因")
    private String reason;
}
```

### 3. 前端组件设计

#### A. 在列表页面添加操作按钮
```vue
<!-- 在MarketingDistributionSettingList.vue的操作列添加 -->
<template #action="{ record }">
  <a-space>
    <!-- 现有操作按钮 -->
    
    <!-- 新增修改推荐人按钮 -->
    <a-button
      type="link"
      size="small"
      @click="handleUpdatePromoter(record)"
    >
      修改推荐人
    </a-button>
  </a-space>
</template>

<script>
export default {
  methods: {
    // 处理修改推荐人点击事件
    handleUpdatePromoter(record) {
      this.$refs.updatePromoterModal.show(record);
    }
  }
}
</script>
```

#### B. 修改推荐人弹窗组件
```vue
<!-- UpdatePromoterModal.vue -->
<template>
  <a-modal
    title="修改推荐人"
    :visible="visible"
    :confirmLoading="confirmLoading"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form ref="form" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <!-- 当前会员信息展示 -->
      <a-form-item label="当前会员">
        <span>{{ currentMember.nickName }}（{{ currentMember.phone }}）</span>
      </a-form-item>
      
      <a-form-item label="当前推荐人">
        <span>{{ currentMember.promoterName || '平台' }}</span>
      </a-form-item>
      
      <!-- 新推荐人选择 -->
      <a-form-item label="新推荐人" prop="newPromoterId">
        <a-select
          v-model:value="form.newPromoterId"
          placeholder="请搜索并选择新推荐人"
          show-search
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="handleSearch"
          @change="handlePromoterChange"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option v-for="item in promoterOptions" :key="item.id" :value="item.id">
            {{ item.nickName }}（{{ item.phone }}）
          </a-select-option>
        </a-select>
      </a-form-item>
      
      <!-- 修改原因 -->
      <a-form-item label="修改原因" prop="reason">
        <a-textarea 
          v-model:value="form.reason" 
          placeholder="请输入修改推荐人的原因"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'

// API方法定义
const getDreamHelperList = (params) => {
  return getAction('/sys/memberList/getDreamHelperList', params)
}

const updatePromoterManually = (data) => {
  return httpAction('/sys/memberList/updatePromoterManually', data, 'put')
}

export default {
  name: 'UpdatePromoterModal',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      fetching: false,
      currentMember: {},
      promoterOptions: [],
      form: {
        newPromoterId: undefined,
        reason: ''
      },
      rules: {
        newPromoterId: [{ required: true, message: '请选择新推荐人' }],
        reason: [{ required: true, message: '请输入修改原因' }]
      }
    }
  },
  methods: {
    show(member) {
      this.visible = true;
      this.currentMember = member;
      this.resetForm();
    },
    
    hide() {
      this.visible = false;
      this.resetForm();
    },
    
    resetForm() {
      this.form = {
        newPromoterId: undefined,
        reason: ''
      };
      this.promoterOptions = [];
      this.$refs.form?.resetFields();
    },
    
    // 搜索助梦家
    async handleSearch(keyword) {
      if (!keyword) {
        this.promoterOptions = [];
        return;
      }

      this.fetching = true;
      try {
        const res = await getDreamHelperList({
          pageNo: 1,
          pageSize: 20,
          keyword
        });
        if (res.success) {
          this.promoterOptions = res.result.records || [];
        }
      } catch (error) {
        console.error('搜索助梦家失败', error);
      } finally {
        this.fetching = false;
      }
    },
    
    handlePromoterChange(value) {
      // 推荐人选择变化处理
    },
    
    // 提交修改
    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        
        this.confirmLoading = true;
        const res = await updatePromoterManually({
          memberId: this.currentMember.id,
          newPromoterId: this.form.newPromoterId,
          reason: this.form.reason
        });

        if (res.success) {
          this.$message.success('修改推荐人成功');
          this.hide();
          this.$emit('success');
        } else {
          this.$message.error(res.message || '修改推荐人失败');
        }
      } catch (error) {
        console.error('修改推荐人失败', error);
      } finally {
        this.confirmLoading = false;
      }
    },
    
    handleCancel() {
      this.hide();
    }
  }
}
</script>
```

### 4. 前端API调用方式

按照项目规范，使用封装好的网络请求方法：

```javascript
// 在Vue组件中直接使用
import { getAction, httpAction } from '@/api/manage'

// 查询助梦家列表
const getDreamHelperList = (params) => {
  return getAction('/sys/memberList/getDreamHelperList', params)
}

// 手动修改推荐人
const updatePromoterManually = (data) => {
  return httpAction('/sys/memberList/updatePromoterManually', data, 'put')
}
```

## 🔧 实施计划

1. **数据库扩展**：新增bindScene枚举值
2. **后端开发**：实现查询助梦家和修改推荐人接口
3. **前端开发**：添加操作按钮和弹窗组件
4. **集成测试**：验证完整功能流程
5. **性能优化**：优化查询性能和用户体验

## ⚠️ 风险控制

1. **循环推荐检测**：防止形成循环推荐关系
2. **权限控制**：只有管理员可以修改推荐人
3. **操作日志**：记录所有修改操作，便于审计
4. **数据一致性**：使用事务确保数据更新的一致性

## 📊 预期效果

- 管理员可以灵活调整会员推荐关系
- 所有修改操作可追溯和审计
- 保持数据一致性和业务规则完整性
- 提升运营管理效率
