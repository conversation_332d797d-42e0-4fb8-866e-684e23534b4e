<template>
  <a-card :bordered="false">
    <a-row :gutter="4" :class="['main-content-row', siderCollapsed ? 'collapsed' : '']">
      <!-- 左侧树形结构 -->
      <a-col :span="siderCollapsed ? 0 : 5" style="transition: all 0.3s ease;" v-if="!siderCollapsed || transitionActive">
        <a-card :bordered="false" :bodyStyle="{padding: '10px', height: 'calc(100vh - 230px)', overflow: 'auto'}" v-if="!siderCollapsed">
          <template slot="title">
            <span>平台分销会员</span>
            <a-button
              type="link"
              class="collapse-btn"
              @click="toggleSider"
              :style="{float: 'right'}"
            >
              <a-icon type="menu-fold" />
            </a-button>
          </template>
          <div class="tree-wrapper">
            <div class="tree-search">
              <a-input-search
                placeholder="搜索会员"
                v-model="treeSearchValue"
                @change="onTreeSearchChange"
                style="margin-bottom: 8px"
              />
            </div>
            <div class="tree-operations">
              <a @click="expandAll">展开全部</a>
              <a-divider type="vertical" />
              <a @click="collapseAll">收起全部</a>
              <a-divider type="vertical" />
              <a @click="clearSelection" class="clear-selection">清除选中</a>
            </div>
            <a-spin :spinning="treeLoading">
              <a-tree
                :treeData="memberTreeData"
                :defaultExpandedKeys="expandedKeys"
                :expandedKeys="expandedKeys"
                :autoExpandParent="autoExpandParent"
                :selectedKeys="selectedKeys"
                @select="onTreeSelect"
                @expand="onExpand"
                :filterTreeNode="filterTreeNode"
                :showLine="true"
              >
                <template slot="title" slot-scope="{title, key, dataRef}">
                  <span v-if="dataRef.phone" class="member-tree-node">
                    <span
                      class="member-name"
                      :style="{
                        color: dataRef.isSystemOperation === '1' ? '#f5222d' :
                              dataRef.memberType === '2' ? '#52c41a' :
                              dataRef.memberType === '0' && dataRef.isLoveAmbassador === '1' ? '#722ed1' : '#000'
                      }"
                    >{{ dataRef.title }}</span>
                    <span class="member-phone">
                      ({{ dataRef.directChildrenCount || 0 }})
                    </span>
                  </span>
                  <span v-else>{{ title }}</span>
                </template>
              </a-tree>
            </a-spin>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧会员列表 -->
      <a-col :span="siderCollapsed && !transitionActive ? 24 : 19" style="transition: all 0.3s ease; position: relative;">
        <!-- 会员信息展示区域 -->
        <a-card v-if="selectedMemberInfo" :bordered="false" :bodyStyle="{padding: '16px', marginBottom: '16px'}" class="selected-member-info">
          <div class="member-info-title">当前选中会员信息：</div>
          <a-row :gutter="16">
            <a-col :span="12">
              <div class="member-info-item">
                <span class="member-info-label">会员昵称：</span>
                <span class="member-info-value">{{ selectedMemberInfo.nickname }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="member-info-item">
                <span class="member-info-label">会员手机号：</span>
                <span class="member-info-value">{{ selectedMemberInfo.phone }}</span>
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <div class="member-info-item">
                <span class="member-info-label">直接推荐人昵称：</span>
                <span class="member-info-value">{{ selectedMemberInfo.recommenderNickname }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="member-info-item">
                <span class="member-info-label">直接推荐人手机号：</span>
                <span class="member-info-value">{{ selectedMemberInfo.recommenderPhone }}</span>
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <div class="member-info-item">
                <span class="member-info-label">直接下级人数：</span>
                <span class="member-info-value">{{ selectedMemberInfo.directChildrenCount || 0 }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="member-info-item">
                <span class="member-info-label">间接下级人数：</span>
                <span class="member-info-value">{{ selectedMemberInfo.indirectChildrenCount || 0 }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline">
            <a-row :gutter="24">

              <a-col :md="8" :sm="8">
                <a-form-item label="手机号">
                  <a-input placeholder="请输入" v-model="queryParam.phone"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="8">
                <a-form-item label="昵称">
                  <a-input placeholder="请输入" v-model="queryParam.nickName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="8" :sm="8">
                <a-form-item label="会员类型">
                  <j-dict-select-tag  v-model="queryParam.memberType"  placeholder="请选择会员类型"
                                      dictCode="member_type"/>
                </a-form-item>
              </a-col>
              <template v-if="toggleSearchStatus">
                <a-col :md="8" :sm="8">
                  <a-form-item label="推荐人">
                    <a-input placeholder="请输入" v-model="queryParam.promoterName"></a-input>
                  </a-form-item>
                </a-col>
                <a-col :md="8" :sm="8">
                  <a-form-item label="注册时间">
                    <a-range-picker
                      style="width: 100%"
                      v-model="queryParam.createTime"
                      format="YYYY-MM-DD"
                      :placeholder="['开始时间', '结束时间']"
                      @change="onDateChange"
                    />
                  </a-form-item>
                </a-col>
              </template>
              <a-col :md="6" :sm="8">
                <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                  <a @click="handleToggleSearch" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
                  </a>
                </span>
              </a-col>

            </a-row>
          </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <!--<a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>-->
          <!--<a-button type="primary" icon="download"  :loading="exportLoading"  @click="handleExportXls('平台分销设置')">导出</a-button>
          <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl"
                    @change="handleImportExcel">
            <a-button type="primary" icon="import">导入</a-button>
          </a-upload>-->
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel">
                <a-icon type="delete"/>
                删除
              </a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作
              <a-icon type="down"/>
            </a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a
            style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
          </div>

          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
            @change="handleTableChange">
            <template slot="headPortrait" slot-scope="text, record, index">
              <img class="clickShowImage " :preview="'headPortrait' + index" :src="record.headPortrait" alt="">
            </template>
            <template slot="address" slot-scope="text, record, index">
              <img class="clickShowImage " :preview="'address' + index" :src="ssAddressView(record.address)" alt="">
            </template>
            <template slot="mlSum" slot-scope="text, record, index">
              <div class="anty-img-wrap">
                <a @click="showGoodInformation(record.id)">{{ record.mlSum }}</a>
              </div>
            </template>
            <template slot="firstLevelCommission" slot-scope="text, record, index">
              <div class="anty-img-wrap">
                <a @click="showBrokerage(record.id)">{{ record.firstLevelCommission }}</a>
              </div>
            </template>
            <template slot="balance" slot-scope="text, record">
              <a @click="showBalanceDetail(record)">{{text}}</a>
            </template>
            <template slot="entrepreneurCommission" slot-scope="text, record">
              <a @click="showEntrepreneurCommissionDetail(record)">{{text}}</a>
            </template>
            <template slot="totalCommission" slot-scope="text, record">
              <a @click="showTotalCommissionDetail(record)">{{text}}</a>
            </template>
            <template slot="accountFrozen" slot-scope="text, record">
              <a @click="showFrozenAmountDetail(record)">{{text}}</a>
            </template>
            <template slot="nickName" slot-scope="text, record">
              <a @click="openMemberDetail(record)">{{ text }}</a>
            </template>
            <template slot="totalPerformance" slot-scope="text, record">
              <a @click="showUserPerformanceDetail(record, 'total')">{{text}}</a>
            </template>
            <template slot="helpFarmersPerformance" slot-scope="text, record">
              <a @click="showUserPerformanceDetail(record, 'brand')">{{text}}</a>
            </template>
            <template slot="helpDisabledPerformance" slot-scope="text, record">
              <a @click="showUserPerformanceDetail(record, 'life')">{{text}}</a>
            </template>
            <template slot="helpStudentPerformance" slot-scope="text, record">
              <a @click="showUserPerformanceDetail(record, 'startup')">{{text}}</a>
            </template>
            <span slot="action" slot-scope="text, record">
              <!-- <a @click="handleEdit(record)">编辑</a>

              <a-divider type="vertical"/> -->
              <a @click="handleUpdatePromoter(record)">修改推荐人</a>

              <!-- <a-divider type="vertical"/>
              <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down"/></a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown> -->
            </span>

          </a-table>
        </div>
        <!-- table区域-end -->
      </a-col>
    </a-row>

    <!-- 表单区域 -->
    <marketingDistributionSetting-modal ref="modalForm" @ok="modalFormOk"></marketingDistributionSetting-modal>
    <AppMemberDistributionModal ref="AppMemberDistributionModal"></AppMemberDistributionModal>
    <AppMemberbrokerageModal ref="AppMemberbrokerageModal"></AppMemberbrokerageModal>
    <MemberBalanceDetailModal ref="memberBalanceDetailModal"></MemberBalanceDetailModal>
    <EntrepreneurCommissionDetailModal ref="entrepreneurCommissionDetailModal"></EntrepreneurCommissionDetailModal>
    <TotalCommissionDetailModal ref="totalCommissionDetailModal"></TotalCommissionDetailModal>
    <FrozenAmountDetailModal ref="frozenAmountDetailModal"></FrozenAmountDetailModal>
    <UserPerformanceDetailModal ref="userPerformanceDetailModal"></UserPerformanceDetailModal>
    <UpdatePromoterModal ref="updatePromoterModal" @success="loadData"></UpdatePromoterModal>
  </a-card>
</template>

<script>
  import MarketingDistributionSettingModal from './modules/MarketingDistributionSettingModal'
  import {JeecgListMixin} from '@/mixins/JeecgListMixin'
  import {filterObj} from '@/utils/util';
  import AppMemberDistributionModal from './modules/AppMemberDistributionModal'
  import AppMemberbrokerageModal from './modules/AppMemberbrokerageModal'
  import MemberBalanceDetailModal from '../member/modules/MemberBalanceDetailModal'
  import EntrepreneurCommissionDetailModal from './modules/EntrepreneurCommissionDetailModal'
  import TotalCommissionDetailModal from './modules/TotalCommissionDetailModal'
  import FrozenAmountDetailModal from './modules/FrozenAmountDetailModal'
  import UserPerformanceDetailModal from './modules/UserPerformanceDetailModal'
  import UpdatePromoterModal from './modules/UpdatePromoterModal'
  import { getAction } from '@/api/manage'

  export default {
    name: "MarketingDistributionSettingList",
    mixins: [JeecgListMixin],
    components: {
      MarketingDistributionSettingModal,
      AppMemberDistributionModal,
      AppMemberbrokerageModal,
      MemberBalanceDetailModal,
      EntrepreneurCommissionDetailModal,
      TotalCommissionDetailModal,
      FrozenAmountDetailModal,
      UserPerformanceDetailModal,
      UpdatePromoterModal
    },
    data () {
      return {
        description: '平台分销会员管理页面',
        siderCollapsed: false, // 侧边栏折叠状态
        transitionActive: false, // 过渡状态标记
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: "center",
            customRender: function (_, __, index) {
              return parseInt(index) + 1;
            }
          },
          {
            title: '头像',
            align: "center",
            dataIndex: 'headPortrait',
            scopedSlots: {customRender: "headPortrait"}
          },
          {
            title: '手机号',
            align: "center",
            dataIndex: 'phone'
          },
          {
            title: '昵称',
            align: "center",
            dataIndex: 'nickName',
            scopedSlots: {customRender: "nickName"}
          },
          {
            title: '会员类型',
            align: "center",
            dataIndex: 'memberType_dictText'
          },
          {
            title: '账户余额',
            align: "center",
            dataIndex: 'balance',
            scopedSlots: {customRender: "balance"}
          },
          {
            title: '冻结金额',
            align: "center",
            dataIndex: 'accountFrozen',
            scopedSlots: {customRender: "accountFrozen"}
          },
          {
            title: '已提现金额',
            align: "center",
            dataIndex: 'haveWithdrawal'
          },
          {
            title: '团队成员',
            align: "center",
            dataIndex: 'mlSum',
            scopedSlots: {customRender: "mlSum"}
          },
          {
            title: '累计佣金',
            align: "center",
            dataIndex: 'totalCommission',
            scopedSlots: {customRender: "totalCommission"}
          },
          {
            title: '分销佣金',
            align: "center",
            dataIndex: 'firstLevelCommission',
            scopedSlots: {customRender: "firstLevelCommission"}
          },
          {
            title: '企业家佣金',
            align: "center",
            dataIndex: 'entrepreneurCommission',
            scopedSlots: {customRender: "entrepreneurCommission"}
          },
          {
            title: '累计总业绩',
            align: "center",
            dataIndex: 'totalPerformance',
            scopedSlots: {customRender: "totalPerformance"}
          },
          {
            title: '品牌馆总业绩',
            align: "center",
            dataIndex: 'helpFarmersPerformance',
            scopedSlots: {customRender: "helpFarmersPerformance"}
          },
          {
            title: '生活馆总业绩',
            align: "center",
            dataIndex: 'helpDisabledPerformance',
            scopedSlots: {customRender: "helpDisabledPerformance"}
          },
          {
            title: '创业馆总业绩',
            align: "center",
            dataIndex: 'helpStudentPerformance',
            scopedSlots: {customRender: "helpStudentPerformance"}
          },
          {
            title: '直接推荐人',
            align: "center",
            dataIndex: 'promoterName'
          },
          {
            title: '注册时间',
            align: "center",
            dataIndex: 'createTime'
          },
           {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width: 180,
            fixed:'right',
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/marketingDistributionSetting/marketingDistributionSetting/findDistributionSettingList",
          delete: "/marketingDistributionSetting/marketingDistributionSetting/delete",
          deleteBatch: "/marketingDistributionSetting/marketingDistributionSetting/deleteBatch",
          exportXlsUrl: "marketingDistributionSetting/marketingDistributionSetting/exportXls",
          importExcelUrl: "marketingDistributionSetting/marketingDistributionSetting/importExcel",
          // 树形结构相关接口，后端实现后替换
          getTreeData: "/memberList/memberList/getMemberTree",
          getChildrenByParentId: "/marketingDistributionSetting/marketingDistributionSetting/getChildrenByParentId",
        },
        // 树形结构相关
        memberTreeData: [],
        expandedKeys: [],
        selectedKeys: [],
        autoExpandParent: true,
        treeLoading: false,
        treeSearchValue: '',
        // 选中会员信息
        selectedMemberInfo: null,
      }
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    mounted () {
      this.loadMemberTreeData();
      this.loadData();
      this.addToggleListener();
      this.checkUrlParams(); // 检查URL参数，自动选中指定会员
      // 如果没有URL参数，默认选中系统运营人员
      setTimeout(() => {
        if (!this.selectedMemberInfo) {
          this.selectSystemOperationMember();
        }
      }, 500);
    },
    methods: {
      // 重写onClearSelected方法，清除选中会员信息
      onClearSelected() {
        this.selectedRowKeys = [];
        this.selectedMemberInfo = null; // 清除选中会员信息
      },

      // 重写onSelectChange方法，在表格中选中会员时显示会员信息
      onSelectChange(selectedRowKeys, selectionRows) {
        this.selectedRowKeys = selectedRowKeys;
        this.selectionRows = selectionRows;

        // 如果选中了会员，显示会员信息
        if (selectionRows && selectionRows.length > 0) {
          const record = selectionRows[0];

          // 显示会员信息
          this.selectedMemberInfo = {
            nickname: record.nickName,
            phone: record.phone,
            recommenderNickname: record.promoterName || '平台',
            recommenderPhone: record.promoterPhone || '平台',
            directChildrenCount: record.directChildrenCount || 0,
            indirectChildrenCount: (record.totalChildrenCount || 0) - (record.directChildrenCount || 0)
          };

          // 在会员树中查找并选中该会员
          this.findAndSelectMemberInTree(record.id, this.memberTreeData);
        } else {
          // 清除选中会员信息
          this.selectedMemberInfo = null;
          this.selectedKeys = [];
        }
      },

      showGoodInformation(id){
        this.$refs.AppMemberDistributionModal.showModalVisible(id);
      },
      showBrokerage(id){
        this.$refs.AppMemberbrokerageModal.showModalVisible(id);
      },
      showBalanceDetail(record){
        this.$refs.memberBalanceDetailModal.showModal(record);
      },
      showEntrepreneurCommissionDetail(record){
        this.$refs.entrepreneurCommissionDetailModal.showModal(record);
      },
      showTotalCommissionDetail(record){
        this.$refs.totalCommissionDetailModal.showModal(record);
      },
      showFrozenAmountDetail(record){
        this.$refs.frozenAmountDetailModal.showModal(record);
      },
      showUserPerformanceDetail(record, type){
        this.$refs.userPerformanceDetailModal.showModal(record, type);
      },

      // 点击会员昵称，直接选中该会员，不再跳转页面
      openMemberDetail(record) {
        // 直接选中该会员
        this.selectedRowKeys = [record.id];

        // 显示会员信息
        this.selectedMemberInfo = {
          nickname: record.nickName,
          phone: record.phone,
          recommenderNickname: record.promoterName || '平台',
          recommenderPhone: record.promoterPhone || '平台',
          directChildrenCount: record.directChildrenCount || 0,
          indirectChildrenCount: (record.totalChildrenCount || 0) - (record.directChildrenCount || 0)
        };

        // 在会员树中查找并选中该会员
        this.findAndSelectMemberInTree(record.id, this.memberTreeData);

        // 查询该会员的下级会员
        const oldQueryParam = {...this.queryParam};
        this.queryParam = {
          ...oldQueryParam,
          parentUniqueId: record.uniqueId || record.id, // 如果没有uniqueId，使用id
          promoterId: record.id
        };
        this.loadData(1);
      },

      // 在会员树中查找并选中指定会员，不改变当前选中的会员信息
      findAndSelectMemberInTree(memberId, data) {
        if (!data || data.length === 0) return null;

        for (const item of data) {
          if (item.id === memberId) {
            // 找到会员，选中并展开到该会员
            this.selectedKeys = [item.key];
            this.expandToMember(item.key);
            return item;
          }

          // 递归查找子节点
          if (item.children && item.children.length > 0) {
            const found = this.findAndSelectMemberInTree(memberId, item.children);
            if (found) return found;
          }
        }

        return null;
      },

      // 检查URL参数，自动选中指定会员
      checkUrlParams() {
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const memberId = urlParams.get('memberId');

        if (memberId) {
          // 如果URL中包含memberId参数，自动查找并选中该会员
          this.findAndSelectMember(memberId);
        }
      },

      // 查找并选中指定会员
      findAndSelectMember(memberId) {
        // 首先尝试从表格数据中查找
        const member = this.dataSource.find(item => item.id === memberId);

        if (member) {
          // 如果在表格中找到了会员，直接显示会员信息
          this.selectedMemberInfo = {
            nickname: member.nickName,
            phone: member.phone,
            recommenderNickname: member.promoterName || '平台',
            recommenderPhone: member.promoterPhone || '平台',
            directChildrenCount: member.directChildrenCount || 0,
            indirectChildrenCount: (member.totalChildrenCount || 0) - (member.directChildrenCount || 0)
          };
        } else {
          // 如果表格中没有，则需要从会员树中查找
          this.findMemberInTree(memberId, this.memberTreeData);
        }
      },

      // 在会员树中递归查找指定会员
      findMemberInTree(memberId, data) {
        if (!data || data.length === 0) return null;

        for (const item of data) {
          if (item.id === memberId) {
            // 找到会员，选中并显示信息
            this.selectedKeys = [item.key];
            this.expandToMember(item.key);

            // 显示会员信息
            this.selectedMemberInfo = {
              nickname: item.title,
              phone: item.phone,
              recommenderNickname: item.promoterName || '平台',
              recommenderPhone: item.promoterPhone || '平台',
              directChildrenCount: item.directChildrenCount || 0,
              indirectChildrenCount: (item.totalChildrenCount || 0) - (item.directChildrenCount || 0)
            };

            // 查询该会员的下级会员
            this.queryParam.parentUniqueId = item.key;
            this.queryParam.promoterId = item.id;
            this.loadData(1);

            return item;
          }

          // 递归查找子节点
          if (item.children && item.children.length > 0) {
            const found = this.findMemberInTree(memberId, item.children);
            if (found) return found;
          }
        }

        return null;
      },

      // 展开到指定会员所在的路径
      expandToMember(key) {
        // 展开到指定节点的路径
        const expandKeys = [];
        const findPath = (data, targetKey, parentKeys = []) => {
          for (const item of data) {
            const currentKeys = [...parentKeys, item.key];

            if (item.key === targetKey) {
              expandKeys.push(...currentKeys);
              return true;
            }

            if (item.children && item.children.length > 0) {
              if (findPath(item.children, targetKey, currentKeys)) {
                return true;
              }
            }
          }

          return false;
        };

        findPath(this.memberTreeData, key);
        this.expandedKeys = [...new Set(expandKeys)];
        this.autoExpandParent = true;
      },

      // 默认选中系统运营人员
      selectSystemOperationMember() {
        // 首先尝试从表格数据中查找系统运营人员
        const systemOperationMember = this.dataSource.find(item => item.isSystemOperation === '1');

        if (systemOperationMember) {
          // 如果在表格中找到了系统运营人员，直接显示会员信息
          this.selectedMemberInfo = {
            nickname: systemOperationMember.nickName,
            phone: systemOperationMember.phone,
            recommenderNickname: systemOperationMember.promoterName || '平台',
            recommenderPhone: systemOperationMember.promoterPhone || '平台',
            directChildrenCount: systemOperationMember.directChildrenCount || 0,
            indirectChildrenCount: (systemOperationMember.totalChildrenCount || 0) - (systemOperationMember.directChildrenCount || 0)
          };
          // 选中该会员对应的行
          this.selectedRowKeys = [systemOperationMember.id];
        } else {
          // 如果表格中没有，则需要从会员树中查找
          this.findSystemOperationMemberInTree(this.memberTreeData);
        }
      },

      // 在会员树中递归查找系统运营人员
      findSystemOperationMemberInTree(data) {
        if (!data || data.length === 0) return null;

        for (const item of data) {
          if (item.isSystemOperation === '1') {
            // 找到系统运营人员，选中并显示信息
            this.selectedKeys = [item.key];
            this.expandToMember(item.key);

            // 显示会员信息
            this.selectedMemberInfo = {
              nickname: item.title,
              phone: item.phone,
              recommenderNickname: item.promoterName || '平台',
              recommenderPhone: item.promoterPhone || '平台',
              directChildrenCount: item.directChildrenCount || 0,
              indirectChildrenCount: (item.totalChildrenCount || 0) - (item.directChildrenCount || 0)
            };

            // 查询该会员的下级会员
            this.queryParam.parentUniqueId = item.key;
            this.queryParam.promoterId = item.id;
            this.loadData(1);

            return item;
          }

          // 递归查找子节点
          if (item.children && item.children.length > 0) {
            const found = this.findSystemOperationMemberInTree(item.children);
            if (found) return found;
          }
        }

        return null;
      },
      // 重写loadData方法，在加载数据后处理推荐人信息
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error('请设置url.list属性!');
          return;
        }
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        var params = this.getQueryParams(); //查询条件
        this.loading = true;
        getAction(this.url.list, params).then(res => {
          if (res.success) {
            // 处理返回的数据
            let records = res.result.records || res.result || [];

            // 处理每个会员的数据，确保有uniqueId和promoterPhone字段
            this.dataSource = records.map(item => ({
              ...item,
              // 如果没有uniqueId，使用id作为uniqueId
              uniqueId: item.uniqueId || item.id
            }));
            this.ipagination.total = res.result.total || 0;

            // 如果当前有选中的会员，更新其信息
            if (this.selectedMemberInfo && this.dataSource.length > 0) {
              const selectedMember = this.dataSource.find(item =>
                item.nickName === this.selectedMemberInfo.nickname ||
                item.phone === this.selectedMemberInfo.phone
              );

              if (selectedMember) {
                this.selectedMemberInfo = {
                  ...this.selectedMemberInfo,
                  recommenderNickname: selectedMember.promoterName || '平台',
                  recommenderPhone: selectedMember.promoterPhone || '平台'
                };
              }
            }
          } else {
            this.$message.warning(res.message);
          }
        }).finally(() => {
          this.loading = false;
        });
      },

      getQueryParams(){
        let param = Object.assign({}, this.queryParam);
        param.field = this.getQueryField();
        param.pageNo = this.ipagination.current;
        param.pageSize = this.ipagination.pageSize;
        return filterObj(param);
      },
      onDateChange(_, dateString) {
        this.queryParam.beginDate = dateString[0];
        this.queryParam.endDate = dateString[1];
      },
      getAvatarView(headPortrait){
        return headPortrait;
      },
      ssAddressView(address){
        return address;
      },

      // 加载会员树形结构数据
      loadMemberTreeData() {
        this.treeLoading = true;

        // 使用模拟数据
        // setTimeout(() => {
        //   this.memberTreeData = this.getMockTreeData();
        //   if (this.memberTreeData.length > 0) {
        //     this.expandedKeys = [this.memberTreeData[0].key];
        //   }
        //   this.treeLoading = false;
        // }, 500);

        // 实际接口调用，后端实现后启用
        getAction(this.url.getTreeData).then(res => {
          if (res.success) {
            this.memberTreeData = this.formatTreeData(res.result);
            if (this.memberTreeData.length > 0) {
              this.expandedKeys = [this.memberTreeData[0].key];
            }
          } else {
            this.$message.error(res.message || '获取会员层级结构失败');
          }
          this.treeLoading = false;
        }).catch(() => {
          this.treeLoading = false;
          this.$message.error('获取会员层级结构失败');
        });
      },

      // 生成模拟树形数据
      getMockTreeData() {
        return [
          {
            key: '1',
            title: '张三',
            phone: '15505903237',
            uniqueId: '1',
            memberLevel: 1,
            children: [
              {
                key: '1-1',
                title: '李四',
                phone: '18950686697',
                uniqueId: '1-1',
                memberLevel: 2,
                children: [
                  {
                    key: '1-1-1',
                    title: '王五',
                    phone: '15159333758',
                    uniqueId: '1-1-1',
                    memberLevel: 3,
                  },
                  {
                    key: '1-1-2',
                    title: '赵六',
                    phone: '13960701117',
                    uniqueId: '1-1-2',
                    memberLevel: 3,
                  }
                ]
              },
              {
                key: '1-2',
                title: '钱七',
                phone: '18559879305',
                uniqueId: '1-2',
                memberLevel: 2,
                children: [
                  {
                    key: '1-2-1',
                    title: '孙八',
                    phone: '13960736598',
                    uniqueId: '1-2-1',
                    memberLevel: 3,
                  }
                ]
              }
            ]
          },
          {
            key: '2',
            title: '周九',
            phone: '15960528452',
            uniqueId: '2',
            memberLevel: 1,
            children: [
              {
                key: '2-1',
                title: '吴十',
                phone: '17350055588',
                uniqueId: '2-1',
                memberLevel: 2,
              }
            ]
          },
          {
            key: '3',
            title: '郑十一',
            phone: '16678907890',
            uniqueId: '3',
            memberLevel: 1,
          }
        ];
      },

      // 格式化树形结构数据
      formatTreeData(data) {
        if (!data || data.length === 0) return [];

        return data.map(item => {
          const node = {
            key: item.uniqueId,
            title: item.nickName || item.phone || '未命名会员',
            phone: item.phone,
            uniqueId: item.uniqueId,
            memberLevel: item.memberLevel,
            memberType: item.memberType,
            isSystemOperation: item.isSystemOperation,
            isLoveAmbassador: item.isLoveAmbassador, // 添加助梦家标识
            id: item.id, // 添加 id 字段
            directChildrenCount: item.directChildrenCount || 0, // 添加直接下级会员数量字段
            totalChildrenCount: item.totalChildrenCount || 0, // 添加总下级会员数量字段
            promoterName: item.promoterName, // 统一使用promoterName字段
            promoterPhone: item.promoterPhone, // 统一使用promoterPhone字段
            isLeaf: !item.hasChildren
          };

          if (item.children && item.children.length > 0) {
            node.children = this.formatTreeData(item.children);
          }

          return node;
        });
      },

      // 树节点展开/收起
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys;
        this.autoExpandParent = false;
      },

      // 展开全部节点
      expandAll() {
        const getAllKeys = (data) => {
          let keys = [];
          data.forEach(item => {
            keys.push(item.key);
            if (item.children) {
              keys = keys.concat(getAllKeys(item.children));
            }
          });
          return keys;
        };

        this.expandedKeys = getAllKeys(this.memberTreeData);
      },

      // 收起全部节点
      collapseAll() {
        this.expandedKeys = [];
      },

      // 清除选中状态
      clearSelection() {
        this.selectedKeys = [];
        this.selectedMemberInfo = null; // 清除选中会员信息
        // 清除parentUniqueId和promoterId查询条件，保留其他条件
        const { parentUniqueId, promoterId, ...restQueryParam } = this.queryParam;
        this.queryParam = restQueryParam;
        this.loadData(1);
      },

      // 切换侧边栏折叠状态
      toggleSider() {
        // 如果是展开操作，先设置过渡状态
        if (this.siderCollapsed) {
          this.transitionActive = true;
          // 先显示左侧区域，然后切换折叠状态
          this.$nextTick(() => {
            setTimeout(() => {
              this.siderCollapsed = false;
              // 完成过渡后清除过渡状态
              setTimeout(() => {
                this.transitionActive = false;
              }, 300);
            }, 50);
          });
        } else {
          // 折叠操作直接执行
          this.siderCollapsed = true;
        }
      },

      // 添加展开按钮点击监听器
      addToggleListener() {
        this.$nextTick(() => {
          const rowElement = document.querySelector('.main-content-row');
          if (rowElement) {
            rowElement.addEventListener('click', (e) => {
              // 检查点击是否在左侧边缘的展开按钮区域
              if (this.siderCollapsed && e.clientX < 20) {
                this.toggleSider();
              }
            });
          }
        });
      },

      // 树节点选择
      onTreeSelect(selectedKeys, e) {
        if (selectedKeys.length > 0) {
          this.selectedKeys = selectedKeys;
          const memberData = e.node.dataRef;

          // 提取并显示会员信息
          this.selectedMemberInfo = {
            nickname: memberData.title,
            phone: memberData.phone,
            recommenderNickname: memberData.promoterName || '平台',
            recommenderPhone: memberData.promoterPhone || '平台',
            directChildrenCount: memberData.directChildrenCount || 0,
            indirectChildrenCount: (memberData.totalChildrenCount || 0) - (memberData.directChildrenCount || 0)
          };

          // 根据选中的会员ID查询其下级会员
          const oldQueryParam = {...this.queryParam};
          this.queryParam = {
            ...oldQueryParam,
            parentUniqueId: selectedKeys[0],
            promoterId: memberData.id // 使用 id 作为 promoterId 参数
          };
          this.loadData(1);
        } else {
          this.selectedKeys = [];
          this.selectedMemberInfo = null; // 清除选中会员信息
          // 清除parentUniqueId查询条件，保留其他条件
          const { parentUniqueId, promoterId, ...restQueryParam } = this.queryParam;
          this.queryParam = restQueryParam;
          this.loadData(1);
        }
      },

      // 树节点搜索
      onTreeSearchChange(e) {
        const value = e.target.value;
        if (!value) {
          this.expandedKeys = [];
          this.autoExpandParent = false;
          return;
        }

        const expandedKeys = this.findExpandedKeysByValue(value, this.memberTreeData);
        this.expandedKeys = expandedKeys;
        this.autoExpandParent = true;
      },

      // 根据搜索值查找需要展开的节点
      findExpandedKeysByValue(value, data) {
        const keys = [];
        const searchValue = value.toLowerCase();

        const traverse = (data, parentKeys = []) => {
          data.forEach(item => {
            const title = item.title.toLowerCase();
            const phone = item.phone ? item.phone.toLowerCase() : '';

            const currentKeys = [...parentKeys, item.key];

            if (title.includes(searchValue) || phone.includes(searchValue)) {
              keys.push(...currentKeys);
            }

            if (item.children) {
              traverse(item.children, currentKeys);
            }
          });
        };

        traverse(data);
        return [...new Set(keys)];
      },

      // 过滤树节点
      filterTreeNode(node) {
        const searchValue = this.treeSearchValue.toLowerCase();
        if (!searchValue) return true;

        const title = node.title.toLowerCase();
        const phone = node.dataRef.phone ? node.dataRef.phone.toLowerCase() : '';

        return title.includes(searchValue) || phone.includes(searchValue);
      },

      // 处理修改推荐人点击事件
      handleUpdatePromoter(record) {
        this.$refs.updatePromoterModal.show(record);
      }
    }
  }
</script>
<style scoped>
  .tree-wrapper {
    height: 100%;
    overflow: auto;
  }

  .tree-search {
    margin-bottom: 8px;
  }

  .tree-operations {
    margin-bottom: 8px;
    text-align: right;
  }

  .member-tree-node {
    display: flex;
    align-items: center;
  }

  .member-name {
    font-weight: 500;
  }

  .member-phone {
    margin-left: 5px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
  }

  /* 添加清除选中状态的按钮 */
  .clear-selection {
    margin-left: 8px;
    color: #1890ff;
    cursor: pointer;
  }

  .clickShowImage {
    max-width: 40px;
    max-height: 40px;
    border-radius: 50%;
  }

  /* 折叠按钮样式 */
  .collapse-btn {
    padding: 0;
    font-size: 16px;
  }

  /* 主内容区域相对定位容器 */
  .main-content-row {
    position: relative;
    min-height: calc(100vh - 230px);
  }

  /* 会员信息展示区域样式 */
  .selected-member-info {
    margin-bottom: 16px;
    background-color: #f9f9f9;
  }

  .member-info-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #1890ff;
  }

  .member-info-item {
    margin-bottom: 8px;
    line-height: 24px;
  }

  .member-info-label {
    font-weight: 500;
    margin-right: 8px;
  }

  .member-info-value {
    color: #333;
  }

  /* 折叠状态下的展开按钮 */
  .main-content-row.collapsed::before {
    content: '';
    position: fixed;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 50px;
    background-color: #1890ff;
    border-radius: 0 4px 4px 0;
    z-index: 100;
    cursor: pointer;
  }

  /* 折叠状态下的展开按钮图标 */
  .main-content-row.collapsed::after {
    content: '\E61F'; /* menu-unfold 图标的 Unicode */
    font-family: 'anticon' !important;
    position: fixed;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    z-index: 101;
    cursor: pointer;
    font-size: 12px;
  }
</style>
