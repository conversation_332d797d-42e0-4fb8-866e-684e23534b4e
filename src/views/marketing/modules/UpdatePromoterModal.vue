<template>
  <a-modal
    title="修改推荐人"
    :visible="visible"
    :confirmLoading="confirmLoading"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <!-- 当前会员信息展示 -->
      <a-form-model-item label="当前会员">
        <span>{{ currentMember.nickName }}（{{ currentMember.phone }}）</span>
      </a-form-model-item>

      <a-form-model-item label="当前推荐人">
        <span>{{ currentMember.promoterName || '平台' }}</span>
      </a-form-model-item>

      <!-- 新推荐人选择 -->
      <a-form-model-item label="新推荐人" prop="newPromoterId">
        <a-select
          v-model="form.newPromoterId"
          placeholder="请搜索并选择新推荐人"
          show-search
          :filter-option="false"
          :not-found-content="fetching ? undefined : null"
          @search="handleSearch"
          @change="handlePromoterChange"
        >
          <a-spin v-if="fetching" slot="notFoundContent" size="small" />
          <a-select-option v-for="item in promoterOptions" :key="item.id" :value="item.id">
            {{ item.nickName }}（{{ item.phone }}）
          </a-select-option>
        </a-select>
      </a-form-model-item>

      <!-- 修改原因 -->
      <a-form-model-item label="修改原因" prop="reason">
        <a-textarea
          v-model="form.reason"
          placeholder="请输入修改推荐人的原因"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { getAction, httpAction } from '@/api/manage'

// API方法定义
const getDreamHelperList = (params) => {
  return getAction('/memberList/memberList/getDreamHelperList', params)
}

const updatePromoterManually = (data) => {
  return httpAction('/memberList/memberList/updatePromoterManually', data, 'put')
}

export default {
  name: 'UpdatePromoterModal',
  data() {
    return {
      visible: false,
      confirmLoading: false,
      fetching: false,
      currentMember: {},
      promoterOptions: [],
      searchCache: new Map(), // 搜索结果缓存
      searchTimer: null, // 防抖定时器
      form: {
        newPromoterId: undefined,
        reason: ''
      },
      rules: {
        newPromoterId: [
          { required: true, message: '请选择新推荐人' },
          { validator: this.validatePromoter, trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入修改原因' },
          { min: 5, message: '修改原因至少5个字符' },
          { max: 200, message: '修改原因不能超过200个字符' }
        ]
      }
    }
  },
  methods: {
    show(member) {
      this.visible = true;
      this.currentMember = member;
      this.resetForm();
    },
    
    hide() {
      this.visible = false;
      this.resetForm();
    },
    
    resetForm() {
      this.form = {
        newPromoterId: undefined,
        reason: ''
      };
      this.promoterOptions = [];
      // 清理定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
        this.searchTimer = null;
      }
      // 安全地重置表单字段
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    
    // 搜索助梦家（带防抖和缓存）
    handleSearch(keyword) {
      // 清理之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      if (!keyword) {
        this.promoterOptions = [];
        return;
      }

      // 检查缓存
      if (this.searchCache.has(keyword)) {
        this.promoterOptions = this.searchCache.get(keyword);
        return;
      }

      // 防抖处理，300ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.doSearch(keyword);
      }, 300);
    },

    // 执行搜索
    async doSearch(keyword) {
      this.fetching = true;
      try {
        const res = await getDreamHelperList({
          pageNo: 1,
          pageSize: 20,
          keyword
        });
        if (res.success) {
          const results = res.result.records || [];
          this.promoterOptions = results;
          // 缓存搜索结果（最多缓存50个）
          if (this.searchCache.size >= 50) {
            const firstKey = this.searchCache.keys().next().value;
            this.searchCache.delete(firstKey);
          }
          this.searchCache.set(keyword, results);
        } else {
          this.$message.error(res.message || '搜索助梦家失败');
          this.promoterOptions = [];
        }
      } catch (error) {
        console.error('搜索助梦家失败', error);
        this.$message.error('搜索助梦家失败，请稍后重试');
        this.promoterOptions = [];
      } finally {
        this.fetching = false;
      }
    },
    
    handlePromoterChange(value) {
      // 推荐人选择变化处理
      // 触发表单验证
      if (this.$refs.form && this.$refs.form.validateField) {
        this.$refs.form.validateField('newPromoterId');
      }
    },

    // 验证推荐人
    validatePromoter(rule, value, callback) {
      if (!value) {
        callback(new Error('请选择新推荐人'));
        return;
      }

      // 验证不能选择自己
      if (value === this.currentMember.id) {
        callback(new Error('不能设置自己为推荐人'));
        return;
      }

      callback();
    },
    
    // 提交修改
    handleSubmit() {
      // 使用a-form-model的验证方法
      this.$refs.form.validate(async (valid) => {
        if (!valid) {
          return;
        }

        try {
          this.confirmLoading = true;
          const res = await updatePromoterManually({
            memberId: this.currentMember.id,
            newPromoterId: this.form.newPromoterId,
            reason: this.form.reason
          });

          if (res.success) {
            this.$message.success('修改推荐人成功');
            this.hide();
            this.$emit('success');
          } else {
            this.$message.error(res.message || '修改推荐人失败');
          }
        } catch (error) {
          console.error('修改推荐人失败', error);
          // 处理网络错误或其他异常
          if (error.response && error.response.data && error.response.data.message) {
            this.$message.error(error.response.data.message);
          } else {
            this.$message.error('网络错误，请稍后重试');
          }
        } finally {
          this.confirmLoading = false;
        }
      });
    },
    
    handleCancel() {
      this.hide();
    }
  },

  // 组件销毁时清理资源
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    this.searchCache.clear();
  }
}
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>
