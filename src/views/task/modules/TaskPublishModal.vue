<template>
  <a-modal
    :title="title"
    :width="isMobile ? '95%' : 1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    :bodyStyle="isMobile ? {padding: '12px'} : {padding: '24px'}"
    class="responsive-modal task-publish-modal">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" :label-col="labelCol" :wrapper-col="wrapperCol">

        <!-- 两列布局 -->
        <a-row :gutter="24">
          <!-- 左列：基础信息 -->
          <a-col :span="12">
            <a-form-model-item label="任务标题" prop="title">
              <a-input v-model="model.title" placeholder="请输入任务标题" :disabled="!isEdit"/>
            </a-form-model-item>

            <a-form-model-item label="任务描述" prop="description">
              <a-textarea v-model="model.description" placeholder="请输入任务描述" :rows="3" :disabled="!isEdit"/>
            </a-form-model-item>

            <a-form-model-item label="任务要求">
              <a-textarea v-model="model.taskRequirements" placeholder="请输入任务要求和完成标准" :rows="4" :disabled="!isEdit"/>
            </a-form-model-item>

            <a-form-model-item label="任务总数量" prop="totalCount">
              <a-input-number v-model="model.totalCount" placeholder="请输入任务总数量" :min="1" :disabled="!isEdit" style="width: 100%"/>
            </a-form-model-item>

            <a-form-model-item label="单价(助力值)" prop="unitPrice">
              <a-input-number v-model="model.unitPrice" placeholder="请输入单价" :min="0" :precision="2" :disabled="!isEdit" style="width: 100%"/>
            </a-form-model-item>
          </a-col>

          <!-- 右列：配置状态 -->
          <a-col :span="12">
            <a-form-model-item label="任务类型" prop="taskType">
              <j-dict-select-tag
                ref="taskTypeSelect"
                type="radio"
                placeholder="请选择任务类型"
                :disabled="isViewMode || isEditingExistingTask"
                v-model="model.taskType"
                dictCode="task_publish_type"
                @change="onTaskTypeChange"/>
              <div style="margin-top: 8px; color: #666; font-size: 12px;">
                <span v-if="model.taskType === '1'">短期任务：用户需在指定截止时间前完成</span>
                <span v-if="model.taskType === '2'">长期任务：用户可在接受后的指定天数内完成</span>
              </div>

            </a-form-model-item>

            <a-form-model-item label="截止时间" prop="deadline">
              <a-date-picker
                v-model="model.deadline"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择截止时间"
                :disabled="isViewMode || isEditingExistingTask"
                style="width: 100%"/>
            </a-form-model-item>

            <!-- 长期任务的天数设置 -->
            <a-form-model-item v-if="model.taskType === '2'" label="几天内完成" prop="daysToComplete">
              <a-input-number
                v-model="model.daysToComplete"
                placeholder="请输入完成天数"
                :min="1"
                :max="365"
                :disabled="isViewMode || isEditingExistingTask"
                style="width: 100%"/>
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                用户接受任务后，需在此天数内完成（1-365天）
              </div>
            </a-form-model-item>

            <!-- 详情模式下的状态信息 -->
            <template v-if="!isEdit">
              <a-form-model-item label="剩余数量">
                <a-input v-model="model.remainingCount" disabled/>
              </a-form-model-item>

              <a-form-model-item label="任务状态">
                <j-dict-select-tag v-model="model.status" dictCode="task_status" disabled/>
              </a-form-model-item>

              <a-form-model-item label="预扣助力值">
                <a-input v-model="model.deductedBalance" disabled/>
              </a-form-model-item>

              <a-form-model-item label="已发放助力值">
                <a-input v-model="model.paidBalance" disabled/>
              </a-form-model-item>

              <a-form-model-item label="退回助力值">
                <a-input v-model="model.refundBalance" disabled/>
              </a-form-model-item>
            </template>
          </a-col>
        </a-row>

        <!-- 发布者类型和是否官方字段已移除，由后端自动判断 -->

      </a-form-model>

      <!-- 任务接受列表（仅详情模式显示） -->
      <div v-if="!isEdit && model.id" class="task-acceptance-list">
        <a-divider>任务接受记录</a-divider>

        <!-- 筛选区域 -->
        <div class="filter-section" style="margin-bottom: 16px;">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-select
                v-model="acceptanceQuery.status"
                placeholder="请选择状态"
                allowClear
                style="width: 100%"
                @change="loadAcceptanceList">
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="1">进行中</a-select-option>
                <a-select-option value="2">待审核</a-select-option>
                <a-select-option value="3">已完成</a-select-option>
                <a-select-option value="4">未通过</a-select-option>
                <a-select-option value="5">已过期</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="8">
              <a-button @click="loadAcceptanceList" type="primary">查询</a-button>
              <a-button @click="resetAcceptanceQuery" style="margin-left: 8px;">重置</a-button>
            </a-col>
          </a-row>
        </div>

        <!-- 接受记录表格 -->
        <a-table
          :columns="acceptanceColumns"
          :dataSource="acceptanceList"
          :pagination="acceptancePagination"
          :loading="acceptanceLoading"
          rowKey="id"
          size="small"
          :scroll="{x: 800}"
          @change="handleAcceptanceTableChange"
          :locale="{emptyText: '暂无任务接受记录'}"
          :emptyText="acceptanceList.length === 0 && !acceptanceLoading ? '暂无任务接受记录' : undefined">

          <!-- 状态列自定义渲染 -->
          <template slot="status" slot-scope="text">
            <a-tag :color="getStatusColor(text)" :style="{ color: getStatusTextColor(text) }">{{ getStatusText(text) }}</a-tag>
          </template>

          <!-- 时间列自定义渲染 -->
          <template slot="acceptTime" slot-scope="text">
            {{ formatTime(text) }}
          </template>

          <template slot="actualDeadline" slot-scope="text">
            <span :style="{color: isExpired(text) ? '#ff4d4f' : '#52c41a'}">
              {{ formatTime(text) }}
            </span>
          </template>


        </a-table>
      </div>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import moment from 'moment'

  export default {
    name: 'TaskPublishModal',
    components: {
    },
    data () {
      return {
        title:"操作",
        visible: false,
        isEdit: true,
        model: {
          taskType: '1', // 默认为短期任务，确保响应式绑定
          daysToComplete: null
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 6 },
          md: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 18 },
          md: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          title: [
            { required: true, message: '请输入任务标题!' },
            { min: 2, max: 200, message: '任务标题长度在2到200个字符之间!' }
          ],
          description: [
            { required: true, message: '请输入任务描述!' }
          ],
          totalCount: [
            { required: true, message: '请输入任务总数量!' }
          ],
          unitPrice: [
            { required: true, message: '请输入单价!' }
          ],
          deadline: [
            { required: true, message: '请选择截止时间!' }
          ],
          taskType: [
            { required: true, message: '请选择任务类型!' }
          ],
          daysToComplete: [
            {
              validator: this.validateDaysToComplete,
              trigger: ['blur', 'change', 'input']
            }
          ]
          // priority: [
          //   { required: true, message: '请选择优先级!' }
          // ]
        },
        url: {
          add: "/taskPublish/taskPublish/add",
          edit: "/taskPublish/taskPublish/edit",
          queryById: "/taskPublish/taskPublish/queryById"
        },
        // 任务接受列表相关数据
        acceptanceList: [],
        acceptanceLoading: false,
        acceptanceQuery: {
          status: '',
          taskId: ''
        },
        acceptancePagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
        },
        acceptanceColumns: [
          {
            title: '接受者信息',
            dataIndex: 'acceptorInfo',
            width: 180,
            align: 'center',
            customRender: (_, record) => {
              if (record.acceptorNickName && record.acceptorPhone) {
                return `${record.acceptorNickName}(${record.acceptorPhone})`
              } else if (record.acceptorNickName) {
                return record.acceptorNickName
              } else if (record.acceptorPhone) {
                return record.acceptorPhone
              } else {
                return record.acceptorId || '-'
              }
            }
          },
          {
            title: '接受数量',
            dataIndex: 'acceptCount',
            width: 80,
            align: 'center'
          },
          {
            title: '单价',
            dataIndex: 'unitPrice',
            width: 80,
            align: 'center'
          },
          {
            title: '总奖励',
            dataIndex: 'totalReward',
            width: 100,
            align: 'center'
          },
          {
            title: '状态',
            dataIndex: 'status',
            width: 100,
            align: 'center',
            scopedSlots: { customRender: 'status' }
          },
          {
            title: '接受时间',
            dataIndex: 'acceptTime',
            width: 150,
            align: 'center',
            scopedSlots: { customRender: 'acceptTime' }
          },
          {
            title: '实际截止时间',
            dataIndex: 'actualDeadline',
            width: 150,
            align: 'center',
            scopedSlots: { customRender: 'actualDeadline' }
          },

        ]
      }
    },
    computed: {
      isMobile() {
        return this.$store.state.app.isMobile;
      },

      // 判断是否为编辑已存在的任务
      isEditingExistingTask() {
        return this.isEdit && this.model.id
      },

      // 判断是否为查看模式
      isViewMode() {
        return !this.isEdit
      }
    },
    watch: {
      // 监听taskType变化，确保组件状态同步
      'model.taskType': {
        immediate: true,
        handler() {
          // 确保组件状态同步
          this.$nextTick(() => {
            const taskTypeComponent = this.$refs.taskTypeSelect
            if (taskTypeComponent && this.model.taskType !== taskTypeComponent.value) {
              this.$forceUpdate()
            }
          })
        }
      }
    },

    created () {
    },
    methods: {
      // 任务类型切换事件
      onTaskTypeChange(taskType) {
        // 记录切换前的任务类型
        const previousTaskType = this.model.taskType

        // 使用Vue.$set确保响应式更新
        this.$set(this.model, 'taskType', taskType)

        // 只在真正从长期任务切换到短期任务时，才清空天数字段
        // 这样可以避免用户输入的内容被意外清空
        if (previousTaskType === '2' && taskType === '1') {
          this.model.daysToComplete = null
          console.log('从长期任务切换到短期任务，清空天数字段')
        }

        // 强制更新组件状态，解决j-dict-select-tag响应式更新问题
        this.$nextTick(() => {
          // 重新验证相关表单字段
          if (this.$refs.form) {
            // 验证天数字段（长期任务时必填，短期任务时不需要）
            this.$refs.form.validateField('daysToComplete')
            // 同时验证任务类型字段
            this.$refs.form.validateField('taskType')
          }

          // 检查并修复组件状态同步问题
          const taskTypeComponent = this.$refs.taskTypeSelect
          if (taskTypeComponent && this.model.taskType !== taskTypeComponent.value) {
            this.$forceUpdate()
          }
        })
      },

      // 自定义验证函数：完成天数
      validateDaysToComplete(rule, value, callback) {
        // 短期任务不需要验证天数
        if (this.model.taskType === '1') {
          callback()
          return
        }

        // 长期任务必须填写天数
        if (!value || value === '') {
          callback(new Error('长期任务必须设置完成天数'))
          return
        }

        const days = parseInt(value)
        if (isNaN(days) || days <= 0) {
          callback(new Error('完成天数必须是大于0的整数'))
          return
        }

        if (days > 365) {
          callback(new Error('完成天数不能超过365天'))
          return
        }

        // 验证通过
        callback()
      },

      add () {
        this.edit({});
      },
      edit (record) {
        this.$refs.form && this.$refs.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.isEdit = true;
        this.title = record.id ? "编辑任务" : "发布任务";

        // 新增任务时设置默认值
        if (!record.id) {
          this.model.taskType = '1' // 默认为短期任务
          this.model.daysToComplete = null
        }

        // 向后兼容：确保taskType有值
        if (!this.model.taskType) {
          this.model.taskType = '1'
        }

        // 处理时间格式
        if (this.model.deadline) {
          this.model.deadline = moment(this.model.deadline);
        }
      },
      detail (record) {
        this.$refs.form && this.$refs.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.isEdit = false;
        this.title = "任务详情";

        // 加载任务接受记录
        this.$nextTick(() => {
          this.loadAcceptanceList()
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
        // 重置接受列表数据
        this.acceptanceList = []
        this.acceptanceQuery.status = ''
        this.acceptancePagination.current = 1
      },
      handleOk () {
        if (!this.isEdit) {
          this.handleCancel();
          return;
        }
        
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            
            let formData = Object.assign({}, this.model);
            // 处理时间格式
            if (formData.deadline) {
              formData.deadline = formData.deadline.format('YYYY-MM-DD HH:mm:ss');
            }
            
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
                that.close();
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },

      // 任务接受列表相关方法
      loadAcceptanceList() {
        if (!this.model.id) return

        // 防止重复请求
        if (this.acceptanceLoading) return

        this.acceptanceLoading = true
        this.acceptanceQuery.taskId = this.model.id

        const params = {
          pageNo: this.acceptancePagination.current,
          pageSize: this.acceptancePagination.pageSize,
          taskId: this.acceptanceQuery.taskId,
          status: this.acceptanceQuery.status || undefined
        }

        httpAction('/taskAcceptanceRecord/taskAcceptanceRecord/list', params, 'get')
          .then(res => {
            if (res.success) {
              this.acceptanceList = res.result.records || []
              this.acceptancePagination.total = res.result.total || 0
            } else {
              this.$message.error(res.message || '加载任务接受记录失败')
              this.acceptanceList = []
              this.acceptancePagination.total = 0
            }
          })
          .catch(error => {
            console.error('加载任务接受记录失败:', error)
            this.$message.error('网络错误，请稍后重试')
            this.acceptanceList = []
            this.acceptancePagination.total = 0
          })
          .finally(() => {
            this.acceptanceLoading = false
          })
      },

      resetAcceptanceQuery() {
        this.acceptanceQuery.status = ''
        this.acceptancePagination.current = 1
        this.loadAcceptanceList()
      },

      handleAcceptanceTableChange(pagination) {
        this.acceptancePagination.current = pagination.current
        this.acceptancePagination.pageSize = pagination.pageSize
        this.loadAcceptanceList()
      },

      getStatusColor(status) {
        // 使用具体的背景色而不是预设颜色，避免样式冲突
        const colorMap = {
          '1': '#1890ff', // 进行中 - 蓝色
          '2': '#faad14', // 待审核 - 橙色
          '3': '#52c41a', // 已完成 - 绿色
          '4': '#f5222d', // 未通过 - 红色
          '5': '#d9d9d9'  // 已过期 - 灰色
        }
        return colorMap[status] || '#d9d9d9'
      },

      getStatusText(status) {
        const textMap = {
          '1': '进行中',
          '2': '待审核',
          '3': '已完成',
          '4': '未通过',
          '5': '已过期'
        }
        return textMap[status] || '未知'
      },

      getStatusTextColor(status) {
        // 为不同状态的a-tag设置明确的文字颜色，确保与背景色有良好对比度
        const colorMap = {
          '1': '#fff',      // 进行中 - 白色文字（蓝色背景）
          '2': '#fff',      // 待审核 - 白色文字（橙色背景）
          '3': '#fff',      // 已完成 - 白色文字（绿色背景）
          '4': '#fff',      // 未通过 - 白色文字（红色背景）
          '5': '#666'       // 已过期 - 深灰色文字（浅灰背景）
        }
        return colorMap[status] || '#666'
      },

      formatTime(time) {
        if (!time) return '-'
        return moment(time).format('MM-DD HH:mm')
      },

      isExpired(time) {
        if (!time) return false
        return moment().isAfter(moment(time))
      }
    }
  }
</script>

<style scoped>
/* 任务发布弹窗样式优化 */
.task-publish-modal .ant-form-item {
  margin-bottom: 16px;
}

/* 两列布局样式 */
.task-publish-modal .ant-row {
  margin-bottom: 0;
}

.task-publish-modal .ant-col {
  padding-left: 12px;
  padding-right: 12px;
}

/* 表单标签样式 */
.task-publish-modal .ant-form-item-label {
  font-weight: 500;
}

/* 输入框样式统一 */
.task-publish-modal .ant-input,
.task-publish-modal .ant-input-number,
.task-publish-modal .ant-select,
.task-publish-modal .ant-date-picker {
  border-radius: 4px;
}

/* 文本域样式 */
.task-publish-modal .ant-input {
  resize: vertical;
}

/* 任务接受列表样式 */
.task-acceptance-list {
  margin-top: 24px;
  padding-top: 16px;
}

.task-acceptance-list .ant-divider {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.task-acceptance-list .filter-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.task-acceptance-list .ant-table-small {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.task-acceptance-list .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 500;
}

.task-acceptance-list .ant-tag {
  border-radius: 4px;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .task-publish-modal .ant-form-item-label {
    text-align: left;
    padding-bottom: 4px;
  }

  .task-publish-modal .ant-form-item-control {
    line-height: 1.5;
  }

  .task-publish-modal .ant-row {
    margin-left: 0;
    margin-right: 0;
  }

  .task-publish-modal .ant-col {
    padding-left: 8px;
    padding-right: 8px;
  }
}

@media (max-width: 576px) {
  .task-publish-modal .ant-col {
    padding-left: 4px;
    padding-right: 4px;
  }

  .task-publish-modal .ant-form-item {
    margin-bottom: 12px;
  }

  .task-publish-modal .ant-input,
  .task-publish-modal .ant-input-number,
  .task-publish-modal .ant-select,
  .task-publish-modal .ant-date-picker {
    font-size: 14px;
  }
}
</style>
