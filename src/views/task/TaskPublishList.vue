<template>
  <a-card :bordered="false" style="min-height: 800px">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="任务标题">
              <a-input placeholder="请输入任务标题" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="发布者类型">
              <j-dict-select-tag placeholder="请选择发布者类型" v-model="queryParam.publisherType" dictCode="publisher_type"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="任务类型">
              <j-dict-select-tag placeholder="请选择任务类型" v-model="queryParam.taskType" dictCode="task_publish_type"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="任务状态">
              <j-dict-select-tag placeholder="请选择任务状态" v-model="queryParam.status" dictCode="task_status"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'taskPublish:add'">发布任务</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel" v-has="'taskPublish:deleteBatch'"><a-icon type="delete"/>删除</a-menu-item>
          <a-menu-item key="2" @click="showBatchTaskTypeModal" v-has="'taskPublish:batchUpdate'"><a-icon type="edit"/>批量修改类型</a-menu-item>
          <a-menu-item key="3" @click="showBatchDaysModal" v-has="'taskPublish:batchUpdate'"><a-icon type="clock-circle"/>批量设置天数</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:1800, y: 600}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange"
        class="responsive-table">

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>

          <template v-if="canEdit(record)">
            <a-divider type="vertical" />
            <a @click="handleEdit(record)" v-has="'taskPublish:edit'">编辑</a>
          </template>

          <template v-if="canOnline(record)">
            <a-divider type="vertical" />
            <a @click="handleOnline(record)" v-has="'taskPublish:updateStatus'">上架</a>
          </template>

          <template v-if="canOffline(record)">
            <a-divider type="vertical" />
            <a @click="handleOffline(record)" v-has="'taskPublish:updateStatus'">下架</a>
          </template>

          <template v-if="canDelete(record)">
            <a-divider type="vertical" />
            <a-popconfirm
              :title="`确定删除任务「${record.title}」吗？`"
              :content="record.status === '4' ? '已过期的任务删除后无法恢复' : '已下架的任务删除后无法恢复'"
              @confirm="handleDeleteConfirm(record.id)"
              v-has="'taskPublish:delete'">
              <a style="color: #ff4d4f;">删除</a>
            </a-popconfirm>
          </template>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <task-publish-modal ref="modalForm" @ok="modalFormOk"></task-publish-modal>

    <!-- 批量修改任务类型模态框 -->
    <a-modal
      title="批量修改任务类型"
      :visible="batchTaskTypeVisible"
      @ok="handleBatchTaskType"
      @cancel="batchTaskTypeVisible = false"
      :confirmLoading="batchLoading">
      <p>已选择 <strong>{{ selectedRowKeys.length }}</strong> 个任务</p>
      <a-form-model :model="batchTaskTypeForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-model-item label="任务类型">
          <a-radio-group v-model="batchTaskTypeForm.taskType">
            <a-radio value="1">短期任务</a-radio>
            <a-radio value="2">长期任务</a-radio>
          </a-radio-group>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 批量设置完成天数模态框 -->
    <a-modal
      title="批量设置完成天数"
      :visible="batchDaysVisible"
      @ok="handleBatchDays"
      @cancel="batchDaysVisible = false"
      :confirmLoading="batchLoading">
      <p>已选择 <strong>{{ selectedRowKeys.length }}</strong> 个任务</p>
      <a-form-model :model="batchDaysForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-model-item label="完成天数">
          <a-input-number
            v-model="batchDaysForm.daysToComplete"
            placeholder="请输入完成天数"
            :min="1"
            :max="365"
            style="width: 100%"/>
          <div style="margin-top: 4px; color: #666; font-size: 12px;">
            仅对长期任务生效（1-365天）
          </div>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

    <!-- 数据统计模态框 -->
    <a-modal
      title="任务发布数据统计"
      :visible="statisticsVisible"
      @cancel="statisticsVisible = false"
      :footer="null"
      width="800px">
      <a-spin :spinning="statisticsLoading">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-card size="small" title="任务类型分布">
              <a-statistic title="短期任务" :value="statistics.shortTermCount" suffix="个" />
              <a-statistic title="长期任务" :value="statistics.longTermCount" suffix="个" style="margin-top: 16px;" />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card size="small" title="时间状态分布">
              <a-statistic title="正常任务" :value="statistics.normalCount" suffix="个" />
              <a-statistic title="过期任务" :value="statistics.expiredCount" suffix="个" style="margin-top: 16px;" />
            </a-card>
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="12">
            <a-card size="small" title="任务状态分布">
              <a-statistic title="进行中" :value="statistics.activeCount" suffix="个" />
              <a-statistic title="已完成" :value="statistics.completedCount" suffix="个" style="margin-top: 16px;" />
            </a-card>
          </a-col>
          <a-col :span="12">
            <a-card size="small" title="平均完成期限">
              <a-statistic title="短期任务" :value="statistics.avgShortTermDays" suffix="天" :precision="1" />
              <a-statistic title="长期任务" :value="statistics.avgLongTermDays" suffix="天" :precision="1" style="margin-top: 16px;" />
            </a-card>
          </a-col>
        </a-row>
      </a-spin>
    </a-modal>
  </a-card>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import TaskPublishModal from './modules/TaskPublishModal'
  import TimeUtils from '@/utils/TimeUtils'

  export default {
    name: 'TaskPublishList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      TaskPublishModal
    },
    data () {
      return {
        description: '任务发布管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title: '任务标题',
            align:"center",
            dataIndex: 'title',
            width: 200
          },
          {
            title: '发布者类型',
            align:"center",
            dataIndex: 'publisherType_dictText',
            width: 100
          },
          {
            title: '任务类型',
            align:"center",
            dataIndex: 'taskType_dictText',
            width: 100
          },
          {
            title: '单价(助力值)',
            align:"center",
            dataIndex: 'unitPrice',
            width: 120
          },
          {
            title: '总数量',
            align:"center",
            dataIndex: 'totalCount',
            width: 80
          },
           {
            title: '总助力值',
            align:"center",
            dataIndex: 'deductedBalance',
            width: 120
          },
          {
            title: '剩余数量',
            align:"center",
            dataIndex: 'remainingCount',
            width: 80
          },
          {
            title: '截止时间',
            align:"center",
            dataIndex: 'displayDeadline',
            width: 180,
            customRender: (text, record) => {
              // 优先使用后端计算的displayDeadline
              if (text) {
                return text
              }
              // 向后兼容：如果displayDeadline为空，使用原始deadline
              if (record.deadline) {
                return this.$moment(record.deadline).format('YYYY-MM-DD HH:mm:ss')
              }
              return '无截止时间'
            }
          },
          {
            title: '任务状态',
            align:"center",
            dataIndex: 'status_dictText',
            width: 100
          },
          // {
          //   title: '优先级',
          //   align:"center",
          //   dataIndex: 'priority_dictText',
          //   width: 80
          // },
          {
            title: '已发放助力值',
            align:"center",
            dataIndex: 'paidBalance',
            width: 120
          },
          {
            title: '创建时间',
            align:"center",
            dataIndex: 'createTime',
            width: 150
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width: 180,
            fixed:'right',
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/taskPublish/taskPublish/list",
          delete: "/taskPublish/taskPublish/delete",
          deleteBatch: "/taskPublish/taskPublish/deleteBatch",
          exportXlsUrl: "taskPublish/taskPublish/exportXls",
          importExcelUrl: "taskPublish/taskPublish/importExcel",
          updateStatus: "/taskPublish/taskPublish/updateStatus",
          batchUpdateTaskType: "/taskPublish/taskPublish/batchUpdateTaskType",
          batchUpdateDays: "/taskPublish/taskPublish/batchUpdateDays"
        },
        // 批量操作相关数据
        batchTaskTypeVisible: false,
        batchDaysVisible: false,
        batchLoading: false,
        batchTaskTypeForm: {
          taskType: '1'
        },
        batchDaysForm: {
          daysToComplete: null
        },
        // 数据统计相关
        statisticsVisible: false,
        statisticsLoading: false,
        statistics: {
          shortTermCount: 0,
          longTermCount: 0,
          normalCount: 0,
          expiredCount: 0,
          activeCount: 0,
          completedCount: 0,
          avgShortTermDays: 0,
          avgLongTermDays: 0
        },
        // 导出加载状态
        exportLoading: false,
        // 统计数据缓存
        statisticsCache: null,
        statisticsCacheTime: null
      }
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    methods: {


      // 显示批量修改任务类型模态框
      showBatchTaskTypeModal() {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请先选择要操作的任务')
          return
        }
        this.batchTaskTypeForm.taskType = '1'
        this.batchTaskTypeVisible = true
      },

      // 显示批量设置天数模态框
      showBatchDaysModal() {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请先选择要操作的任务')
          return
        }
        this.batchDaysForm.daysToComplete = null
        this.batchDaysVisible = true
      },

      // 处理批量修改任务类型
      handleBatchTaskType() {
        if (!this.batchTaskTypeForm.taskType) {
          this.$message.error('请选择任务类型')
          return
        }

        const typeText = this.batchTaskTypeForm.taskType === '2' ? '长期任务' : '短期任务'

        this.$confirm({
          title: '确认批量修改',
          content: `确定要将选中的 ${this.selectedRowKeys.length} 个任务修改为${typeText}吗？`,
          onOk: async () => {
            this.batchLoading = true
            try {
              const params = {
                ids: this.selectedRowKeys.join(','),
                taskType: this.batchTaskTypeForm.taskType
              }

              await this.$http.post(this.url.batchUpdateTaskType, params)
              this.$message.success(`成功修改 ${this.selectedRowKeys.length} 个任务的类型`)
              this.batchTaskTypeVisible = false
              this.onClearSelected()
              this.loadData()
            } catch (error) {
              console.error('批量修改任务类型失败:', error)
              this.$message.error('批量修改任务类型失败')
            } finally {
              this.batchLoading = false
            }
          }
        })
      },

      // 处理批量设置完成天数
      handleBatchDays() {
        if (!this.batchDaysForm.daysToComplete) {
          this.$message.error('请输入完成天数')
          return
        }

        if (this.batchDaysForm.daysToComplete < 1 || this.batchDaysForm.daysToComplete > 365) {
          this.$message.error('完成天数必须在1-365天之间')
          return
        }

        this.$confirm({
          title: '确认批量设置',
          content: `确定要将选中的 ${this.selectedRowKeys.length} 个任务的完成天数设置为 ${this.batchDaysForm.daysToComplete} 天吗？`,
          onOk: async () => {
            this.batchLoading = true
            try {
              const params = {
                ids: this.selectedRowKeys.join(','),
                daysToComplete: this.batchDaysForm.daysToComplete
              }

              await this.$http.post(this.url.batchUpdateDays, params)
              this.$message.success(`成功设置 ${this.selectedRowKeys.length} 个任务的完成天数`)
              this.batchDaysVisible = false
              this.onClearSelected()
              this.loadData()
            } catch (error) {
              console.error('批量设置完成天数失败:', error)
              this.$message.error('批量设置完成天数失败')
            } finally {
              this.batchLoading = false
            }
          }
        })
      },

      // 显示数据统计
      async showStatistics() {
        this.statisticsVisible = true

        // 检查缓存（5分钟有效期）
        const now = Date.now()
        if (this.statisticsCache && this.statisticsCacheTime &&
            (now - this.statisticsCacheTime) < 5 * 60 * 1000) {
          this.statistics = this.statisticsCache
          return
        }

        this.statisticsLoading = true

        try {
          // 模拟统计数据计算（实际应该调用后端API）
          await this.calculateStatistics()

          // 缓存统计结果
          this.statisticsCache = { ...this.statistics }
          this.statisticsCacheTime = now
        } catch (error) {
          console.error('获取统计数据失败:', error)
          this.$message.error('获取统计数据失败')
        } finally {
          this.statisticsLoading = false
        }
      },

      // 计算统计数据
      async calculateStatistics() {
        // 这里应该调用后端API获取统计数据
        // 暂时使用前端计算模拟
        const allData = await this.getAllTaskData()

        let shortTermCount = 0
        let longTermCount = 0
        let normalCount = 0
        let expiredCount = 0
        let activeCount = 0
        let completedCount = 0
        let longTermDaysSum = 0
        let longTermDaysCount = 0

        allData.forEach(task => {
          // 任务类型统计
          if (task.taskType === '2') {
            longTermCount++
            if (task.daysToComplete) {
              longTermDaysSum += task.daysToComplete
              longTermDaysCount++
            }
          } else {
            shortTermCount++
          }

          // 时间状态统计
          const taskObj = {
            taskType: task.taskType || '1',
            deadline: task.deadline,
            daysToComplete: task.daysToComplete
          }

          if (TimeUtils.isTaskExpired(taskObj)) {
            expiredCount++
          } else {
            normalCount++
          }

          // 任务状态统计
          if (task.status === '1' || task.status === '2') {
            activeCount++
          } else {
            completedCount++
          }
        })

        this.statistics = {
          shortTermCount,
          longTermCount,
          normalCount,
          expiredCount,
          activeCount,
          completedCount,
          avgShortTermDays: shortTermCount > 0 ? 7 : 0, // 短期任务平均7天（示例）
          avgLongTermDays: longTermDaysCount > 0 ? longTermDaysSum / longTermDaysCount : 0
        }
      },

      // 获取所有任务数据（用于统计）
      async getAllTaskData() {
        try {
          const params = {
            pageNo: 1,
            pageSize: 9999, // 获取所有数据
            ...this.queryParam
          }
          const response = await this.$http.get(this.url.list, { params })
          return response.result.records || []
        } catch (error) {
          console.error('获取任务数据失败:', error)
          return []
        }
      },

      // 增强导出功能
      async handleEnhancedExport() {
        this.exportLoading = true

        try {
          // 获取当前筛选条件下的数据（避免导出所有数据）
          const exportData = await this.getFilteredDataForExport()

          // 生成带时间戳的文件名
          const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
          const filename = `任务发布列表_${timestamp}`

          // 调用原有导出方法
          this.handleExportXls(filename, exportData)
        } catch (error) {
          console.error('导出失败:', error)
          this.$message.error('导出失败')
        } finally {
          this.exportLoading = false
        }
      },

      // 获取筛选后的导出数据
      async getFilteredDataForExport() {
        try {
          const params = {
            pageNo: 1,
            pageSize: 1000, // 限制导出数量，避免性能问题
            ...this.queryParam
          }
          const response = await this.$http.get(this.url.list, { params })
          const records = response.result.records || []

          // 处理导出数据，添加计算字段
          return records.map(record => {
            const task = {
              taskType: record.taskType || '1',
              deadline: record.deadline,
              daysToComplete: record.daysToComplete
            }

            return {
              ...record,
              taskType_dictText: TimeUtils.getTaskTypeText(record.taskType || '1'),
              timeStatus: TimeUtils.getTimeStatus(task),
              displayDeadline: TimeUtils.formatTime(TimeUtils.calculateDisplayDeadline(task)),
              isExpired: TimeUtils.isTaskExpired(task) ? '是' : '否'
            }
          })
        } catch (error) {
          console.error('获取导出数据失败:', error)
          return []
        }
      },

      handleAdd() {
        this.$refs.modalForm.add();
      },
      handleEdit(record) {
        this.$refs.modalForm.edit(record);
      },
      handleDetail(record) {
        this.$refs.modalForm.detail(record);
      },
      handleOffline(record) {
        const statusText = record.status === '1' ? '进行中' : '已满额';
        this.$confirm({
          title: '确认下架任务',
          content: `确定要下架任务"${record.title}"吗？\n当前状态：${statusText}\n下架后用户将无法接受此任务，已接受的任务不受影响。`,
          onOk: () => {
            this.updateTaskStatus(record.id, '5');
          }
        });
      },
      handleOnline(record) {
        this.$confirm({
          title: '确认上架任务',
          content: `确定要重新上架任务"${record.title}"吗？\n上架后用户可以继续接受此任务。`,
          onOk: () => {
            this.updateTaskStatus(record.id, '1');
          }
        });
      },
      updateTaskStatus(id, status) {
        const { postAction } = require('@/api/manage');
        postAction(this.url.updateStatus, {id: id, status: status}).then((res) => {
          if (res.success) {
            this.$message.success(res.message);
            this.loadData();
          } else {
            this.$message.warning(res.message);
          }
        });
      },
      handleDeleteConfirm(id) {
        this.handleDelete(id);
      },
      // 权限判断方法
      canEdit(record) {
        // 进行中或已下架的任务可以编辑
        return record.status === '1' || record.status === '5';
      },
      canOnline(record) {
        // 只有已下架的任务可以上架
        return record.status === '5';
      },
      canOffline(record) {
        // 进行中或已满额的任务可以下架
        return record.status === '1' || record.status === '2';
      },
      canDelete(record) {
        // 已过期或已下架的任务可以删除
        return record.status === '4' || record.status === '5';
      }
    }
  }
</script>

<style scoped>
  @import '~@assets/less/common.less';

  /* 响应式设计优化 */
  .responsive-table {
    overflow-x: auto;
  }

  @media (max-width: 768px) {
    .table-page-search-wrapper .ant-form-item {
      margin-bottom: 16px;
    }

    .table-operator {
      margin-bottom: 16px;
    }

    .table-operator .ant-btn {
      margin-bottom: 8px;
    }

    .responsive-table .ant-table-thead > tr > th,
    .responsive-table .ant-table-tbody > tr > td {
      padding: 8px 4px;
      font-size: 12px;
    }
  }

  @media (max-width: 576px) {
    .ant-card {
      margin: 0 -12px;
    }

    .table-page-search-wrapper {
      padding: 12px;
    }

    .table-operator {
      padding: 0 12px;
    }

    .responsive-table .ant-table-thead > tr > th,
    .responsive-table .ant-table-tbody > tr > td {
      padding: 6px 2px;
      font-size: 11px;
    }
  }
</style>
