<template>
  <a-card :bordered="false" style="min-height: 800px">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="任务ID">
              <a-input placeholder="请输入任务ID" v-model="queryParam.taskId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="接受者ID">
              <a-input placeholder="请输入接受者ID" v-model="queryParam.acceptorId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="任务状态">
              <j-dict-select-tag placeholder="请选择记录状态" v-model="queryParam.status" dictCode="acceptance_status"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="时间状态">
              <a-select placeholder="请选择时间状态" v-model="queryParam.timeStatus" allowClear>
                <a-select-option value="normal">正常</a-select-option>
                <a-select-option value="expired">已过期</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchAudit('3')" v-has="'taskAcceptanceRecord:batchAudit'"><a-icon type="check"/>批量通过</a-menu-item>
          <a-menu-item key="2" @click="batchAudit('4')" v-has="'taskAcceptanceRecord:batchAudit'"><a-icon type="close"/>批量拒绝</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:2000, y: 600}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange"
        class="responsive-table">

        <template slot="submitImages" slot-scope="text">
          <div v-if="text">
            <a-button size="small" @click="previewImages(text)">查看图片</a-button>
          </div>
          <span v-else>-</span>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)" v-has="'taskAcceptanceRecord:detail'">详情</a>
          <a-divider type="vertical" />
          <a v-if="record.status === '2'" @click="handleAudit(record, '3')" v-has="'taskAcceptanceRecord:audit'">通过</a>
          <a v-if="record.status === '2'" @click="handleAudit(record, '4')" v-has="'taskAcceptanceRecord:audit'">拒绝</a>
          <a-divider type="vertical" v-if="record.status === '2'" v-has="'taskAcceptanceRecord:audit'"/>
          <a-popconfirm title="确定删除吗?" @confirm="handleDeleteConfirm(record.id)" v-has="'taskAcceptanceRecord:delete'">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>
    <!-- table区域-end -->

    <!-- 表单区域 -->
    <task-acceptance-record-modal ref="modalForm" @ok="modalFormOk"></task-acceptance-record-modal>
    
    <!-- 审核弹窗 -->
    <a-modal
      title="审核任务"
      :visible="auditVisible"
      @ok="handleAuditSubmit"
      @cancel="auditVisible = false"
      :confirmLoading="auditLoading">
      <a-form-model :model="auditForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-model-item label="审核结果">
          <a-radio-group v-model="auditForm.status">
            <a-radio value="3">通过</a-radio>
            <a-radio value="4">拒绝</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="审核意见" v-if="auditForm.status === '3'">
          <a-textarea v-model="auditForm.auditOpinion" placeholder="请输入审核意见（可选）" :rows="3"/>
        </a-form-model-item>
        <a-form-model-item label="拒绝理由" v-if="auditForm.status === '4'">
          <a-textarea v-model="auditForm.auditReason" placeholder="请输入拒绝理由" :rows="3"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    
    <!-- 图片预览弹窗 -->
    <a-modal
      title="证明材料"
      :visible="imagePreviewVisible"
      @cancel="imagePreviewVisible = false"
      :footer="null"
      width="800px">
      <div v-if="previewImageList.length > 0">
        <a-carousel autoplay>
          <div v-for="(image, index) in previewImageList" :key="index">
            <img :src="image" style="width: 100%; max-height: 500px; object-fit: contain;"/>
          </div>
        </a-carousel>
      </div>
    </a-modal>
  </a-card>
</template>

<script>
  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import TaskAcceptanceRecordModal from './modules/TaskAcceptanceRecordModal'
  import { httpAction, postAction } from '@/api/manage'
  import TimeUtils from '@/utils/TimeUtils'

  export default {
    name: 'TaskAcceptanceRecordList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      TaskAcceptanceRecordModal
    },
    data () {
      return {
        description: '任务接受记录管理页面',
        auditVisible: false,
        auditLoading: false,
        auditForm: {
          id: '',
          status: '3',
          auditReason: '',
          auditOpinion: ''
        },
        imagePreviewVisible: false,
        previewImageList: [],
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title: '任务ID',
            align:"center",
            dataIndex: 'taskId',
            width: 120
          },
          {
            title: '任务类型',
            align:"center",
            dataIndex: 'taskType',
            width: 100,
            customRender: (text, record) => {
              return this.renderTaskTypeColumn(record)
            }
          },
          {
            title: '接受者ID',
            align:"center",
            dataIndex: 'acceptorId',
            width: 120
          },
          {
            title: '接受数量',
            align:"center",
            dataIndex: 'acceptCount',
            width: 80
          },
          {
            title: '单价',
            align:"center",
            dataIndex: 'unitPrice',
            width: 100
          },
          {
            title: '总奖励',
            align:"center",
            dataIndex: 'totalReward',
            width: 100
          },
          {
            title: '状态',
            align:"center",
            dataIndex: 'status_dictText',
            width: 100
          },
          {
            title: '接受时间',
            align:"center",
            dataIndex: 'acceptTime',
            width: 150
          },
          {
            title: '实际截止时间',
            align:"center",
            dataIndex: 'actualDeadline',
            width: 180,
            customRender: (text, record) => {
              return this.renderActualDeadlineColumn(record)
            }
          },
          {
            title: '完成说明',
            align:"center",
            dataIndex: 'submitContent',
            width: 200,
            customRender: function(text) {
              if (!text) return '-';
              return text.length > 50 ? text.substring(0, 50) + '...' : text;
            }
          },
          {
            title: '证明材料',
            align:"center",
            dataIndex: 'submitImages',
            width: 100,
            scopedSlots: { customRender: 'submitImages' }
          },
          {
            title: '提交时间',
            align:"center",
            dataIndex: 'submitTime',
            width: 150
          },
          {
            title: '审核时间',
            align:"center",
            dataIndex: 'auditTime',
            width: 150
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            width:"150px",
            fixed:'right',
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/taskAcceptanceRecord/taskAcceptanceRecord/list",
          delete: "/taskAcceptanceRecord/taskAcceptanceRecord/delete",
          deleteBatch: "/taskAcceptanceRecord/taskAcceptanceRecord/deleteBatch",
          exportXlsUrl: "taskAcceptanceRecord/taskAcceptanceRecord/exportXls",
          audit: "/taskAcceptanceRecord/taskAcceptanceRecord/audit",
          batchAudit: "/taskAcceptanceRecord/taskAcceptanceRecord/batchAudit"
        }
      }
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    methods: {
      // 渲染任务类型列
      renderTaskTypeColumn(record) {
        // 这里需要通过关联查询获取任务类型，暂时使用默认值
        // TODO: 后续需要在后端添加关联查询
        const taskType = record.taskType || '1' // 向后兼容
        const typeText = taskType === '2' ? '长期任务' : '短期任务'
        const color = taskType === '2' ? '#722ed1' : '#52c41a'
        return `<span style="color: ${color}; font-weight: 500;">${typeText}</span>`
      },

      // 渲染实际截止时间列
      renderActualDeadlineColumn(record) {
        if (!record.actualDeadline) {
          return '<span style="color: #999;">未设置</span>'
        }

        const isExpired = TimeUtils.isActualDeadlineExpired(record.actualDeadline)
        const color = isExpired ? '#ff4d4f' : '#52c41a'
        const displayTime = TimeUtils.formatTime(record.actualDeadline, 'MM-DD HH:mm')
        const statusText = isExpired ? '已过期' : '正常'

        return `
          <div>
            <div style="color: ${color}; font-weight: 500;">${displayTime}</div>
            <small style="color: ${color};">${statusText}</small>
          </div>
        `
      },

      handleDetail(record) {
        this.$refs.modalForm.detail(record);
      },
      handleAudit(record, status) {
        this.auditForm = {
          id: record.id,
          status: status,
          auditReason: '',
          auditOpinion: ''
        };
        this.auditVisible = true;
      },
      handleAuditSubmit() {
        this.auditLoading = true;
        httpAction(this.url.audit, this.auditForm, 'post').then((res) => {
          if (res.success) {
            this.$message.success(res.message);
            this.auditVisible = false;
            this.loadData();
          } else {
            this.$message.warning(res.message);
          }
        }).finally(() => {
          this.auditLoading = false;
        });
      },
      batchAudit(status) {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请选择要操作的记录');
          return;
        }
        
        const statusText = status === '3' ? '通过' : '拒绝';
        this.$confirm({
          title: `确认批量${statusText}`,
          content: `确定要批量${statusText}选中的${this.selectedRowKeys.length}条记录吗？`,
          onOk: () => {
            postAction(this.url.batchAudit, {
              ids: this.selectedRowKeys.join(','),
              status: status
            }).then((res) => {
              if (res.success) {
                this.$message.success(res.message);
                this.onClearSelected();
                this.loadData();
              } else {
                this.$message.warning(res.message);
              }
            });
          }
        });
      },
      previewImages(imageUrls) {
        if (!imageUrls) return;
        this.previewImageList = imageUrls.split(',').filter(url => url.trim());
        this.imagePreviewVisible = true;
      },
      handleDeleteConfirm(id) {
        this.handleDelete(id);
      }
    }
  }
</script>

<style scoped>
  @import '~@assets/less/common.less';

  /* 响应式设计优化 */
  .responsive-table {
    overflow-x: auto;
  }

  @media (max-width: 768px) {
    .table-page-search-wrapper .ant-form-item {
      margin-bottom: 16px;
    }

    .table-operator {
      margin-bottom: 16px;
    }

    .table-operator .ant-btn {
      margin-bottom: 8px;
    }

    .responsive-table .ant-table-thead > tr > th,
    .responsive-table .ant-table-tbody > tr > td {
      padding: 8px 4px;
      font-size: 12px;
    }

    .ant-modal {
      margin: 0;
      max-width: 100vw;
    }
  }

  @media (max-width: 576px) {
    .ant-card {
      margin: 0 -12px;
    }

    .table-page-search-wrapper {
      padding: 12px;
    }

    .table-operator {
      padding: 0 12px;
    }

    .responsive-table .ant-table-thead > tr > th,
    .responsive-table .ant-table-tbody > tr > td {
      padding: 6px 2px;
      font-size: 11px;
    }

    .ant-modal {
      padding: 0;
    }

    .ant-modal-content {
      margin: 0;
    }
  }
</style>
