/**
 * 时间工具类 - 任务发布功能专用
 * 提供统一的时间计算和格式化方法，确保前后端逻辑一致
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 * @version 1.0
 */

import moment from 'moment'

export default {
  /**
   * 计算显示用的截止时间
   * 与C端逻辑保持完全一致
   * 
   * @param {Object} task 任务对象
   * @param {String} task.taskType 任务类型：'1'-短期任务，'2'-长期任务
   * @param {String|Date} task.deadline 原始截止时间
   * @param {Number} task.daysToComplete 几天内完成（仅长期任务）
   * @param {Date} baseTime 基准时间，默认为当前时间
   * @returns {Date} 计算后的显示截止时间
   */
  calculateDisplayDeadline(task, baseTime = new Date()) {
    if (!task || !task.deadline) {
      return new Date()
    }
    
    // 短期任务或没有天数限制：直接使用原始deadline
    // 向后兼容：taskType为null/undefined时当作短期任务处理
    if (!task.taskType || task.taskType === '1' || !task.daysToComplete || task.daysToComplete <= 0) {
      return new Date(task.deadline)
    }
    
    // 长期任务：计算从基准时间开始的截止时间
    const deadlineFromBase = new Date(baseTime.getTime() + task.daysToComplete * 24 * 60 * 60 * 1000)
    const originalDeadline = new Date(task.deadline)
    
    // 返回较早的时间
    return originalDeadline < deadlineFromBase ? originalDeadline : deadlineFromBase
  },

  /**
   * 判断任务是否已过期
   * 
   * @param {Object} task 任务对象
   * @param {Date} currentTime 当前时间，默认为系统当前时间
   * @returns {Boolean} 是否已过期
   */
  isTaskExpired(task, currentTime = new Date()) {
    if (!task || !task.deadline) return false
    
    const actualDeadline = this.calculateDisplayDeadline(task, currentTime)
    return actualDeadline.getTime() < currentTime.getTime()
  },

  /**
   * 获取时间状态
   * 
   * @param {Object} task 任务对象
   * @param {Date} currentTime 当前时间，默认为系统当前时间
   * @returns {String} 时间状态：'expired'-已过期，'urgent'-紧急，'warning'-警告，'normal'-正常
   */
  getTimeStatus(task, currentTime = new Date()) {
    if (!task || !task.deadline) return 'normal'
    
    const actualDeadline = this.calculateDisplayDeadline(task, currentTime)
    const diff = actualDeadline.getTime() - currentTime.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    
    if (diff <= 0) {
      return 'expired'
    } else if (hours <= 24) {
      return 'urgent'
    } else if (hours <= 72) {
      return 'warning'
    } else {
      return 'normal'
    }
  },

  /**
   * 格式化截止时间显示
   * 
   * @param {Object} task 任务对象
   * @param {Date} currentTime 当前时间，默认为系统当前时间
   * @returns {String} 格式化后的时间字符串
   */
  formatDeadline(task, currentTime = new Date()) {
    if (!task || !task.deadline) return '无截止时间'
    
    const actualDeadline = this.calculateDisplayDeadline(task, currentTime)
    const diff = actualDeadline.getTime() - currentTime.getTime()
    
    if (diff <= 0) {
      return '已过期'
    }
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) {
      return `${days}天后截止`
    } else if (hours > 0) {
      return `${hours}小时后截止`
    } else {
      return '即将截止'
    }
  },

  /**
   * 格式化时间为标准格式
   * 
   * @param {String|Date} time 时间
   * @param {String} format 格式，默认为 'YYYY-MM-DD HH:mm:ss'
   * @returns {String} 格式化后的时间字符串
   */
  formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!time) return ''
    return moment(time).format(format)
  },

  /**
   * 获取任务类型文本
   * 
   * @param {String} taskType 任务类型
   * @returns {String} 任务类型文本
   */
  getTaskTypeText(taskType) {
    const typeMap = {
      '1': '短期任务',
      '2': '长期任务'
    }
    return typeMap[taskType] || '短期任务'
  },

  /**
   * 获取时间状态对应的颜色
   * 
   * @param {String} status 时间状态
   * @returns {String} 颜色值
   */
  getStatusColor(status) {
    const colorMap = {
      'expired': '#ff4d4f',    // 红色
      'urgent': '#fa8c16',     // 橙色
      'warning': '#faad14',    // 黄色
      'normal': '#52c41a'      // 绿色
    }
    return colorMap[status] || '#52c41a'
  },

  /**
   * 获取时间状态对应的标签类型（Ant Design）
   * 
   * @param {String} status 时间状态
   * @returns {String} 标签类型
   */
  getStatusTagType(status) {
    const tagMap = {
      'expired': 'error',
      'urgent': 'warning',
      'warning': 'orange',
      'normal': 'success'
    }
    return tagMap[status] || 'success'
  },

  /**
   * 计算实际截止时间（基于接受时间）
   * 用于显示TaskAcceptanceRecord中的actualDeadline字段
   * 
   * @param {Object} task 任务对象
   * @param {Date} acceptTime 接受时间
   * @returns {Date} 实际截止时间
   */
  calculateActualDeadline(task, acceptTime) {
    if (!task || !task.deadline || !acceptTime) {
      return task ? new Date(task.deadline) : new Date()
    }
    
    // 短期任务或天数为空：直接使用deadline
    // 向后兼容：taskType为null时当作短期任务处理
    if (!task.taskType || task.taskType === '1' || !task.daysToComplete || task.daysToComplete <= 0) {
      return new Date(task.deadline)
    }
    
    // 长期任务：计算从接受时间开始的截止时间
    const deadlineFromAcceptTime = new Date(acceptTime.getTime() + task.daysToComplete * 24 * 60 * 60 * 1000)
    const originalDeadline = new Date(task.deadline)
    
    // 返回较早的时间
    return originalDeadline < deadlineFromAcceptTime ? originalDeadline : deadlineFromAcceptTime
  },

  /**
   * 判断实际截止时间是否过期
   * 
   * @param {String|Date} actualDeadline 实际截止时间
   * @param {Date} currentTime 当前时间，默认为系统当前时间
   * @returns {Boolean} 是否已过期
   */
  isActualDeadlineExpired(actualDeadline, currentTime = new Date()) {
    if (!actualDeadline) return false
    return new Date(actualDeadline).getTime() < currentTime.getTime()
  }
}
