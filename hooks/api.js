const api = {
	//通过code进行登录
	loginByCode: '/front/weixin/loginByCode',
	// 商城首页
	indexNew: '/front/index/indexNew',
	//广告接口
	findarketingAdvertisingList: '/front/MarketingAdvertising/findarketingAdvertisingList',
	//热销产品
	searchHostStoreGoodList: "/front/goodList/searchHostStoreGoodList",
	//获取页面二维码(用户端)
	getQrCodeByPage: "after/weixin/getQrCodeByPage",
	//推荐店铺
	getStoreManageByRecommend: "front/storeManage/getStoreManageByRecommend",
	//店铺列表
	findStoreManageList: "front/storeManage/findStoreManageList",
	//app获取验证码
	appVerificationCode: "captcha/verificationCode",
	//app手机号登录
	loginByPhone: "front/member/loginByPhone",
	//app登录获取图片二维码信息
	getCaptcha: "captcha/getCaptcha",
	//请求token,测试用
	refreshToken: "/front/weixin/refreshToken",
	//获取会员信息
	getMemberInfo: '/after/member/getMemberInfo',
	//根据商品id获取商品详情
	findGoodListByGoodId: 'front/goodList/findGoodListByGoodId',
	//商品单件收藏
	addMemberGoodsCollectionByGoodId: "after/memberGoodsCollection/addMemberGoodsCollectionByGoodId",
	//商品取消收藏（单个取消）
	delMemberGoodsCollectionByGoodId: 'after/memberGoodsCollection/delMemberGoodsCollectionByGoodId',
	//店铺首页
	storeManageIndex: "front/storeManage/index",
	//店铺分类 获取二级和三级分类
	findMoreGoodType: 'front/goodType/findMoreGoodType',
	//根据类型id获取搜索商品列表
	findGoodListByGoodType: 'front/goodList/findGoodListByGoodType',
	//通过商品收藏id批量取消
	delMemberGoodsCollectionByIds: "after/memberGoodsCollection/delMemberGoodsCollectionByIds",
	//分页查询用户收藏列表
	findMemberGoodsCollections: "after/memberGoodsCollection/findMemberGoodsCollections",
	//商品浏览记录
	findBrowsingHistorys: "after/memberBrowsingHistory/findBrowsingHistorys",
	//批量删除浏览记录
	delMemberBrowsingHistoryByIds: "after/memberBrowsingHistory/delMemberBrowsingHistoryByIds",
	//收藏店铺列表
	findMemberAttentionStoreByMember: "after/memberAttentionStore/findMemberAttentionStoreByMember",
	// 用户获取收货地址
	getAddress: 'after/memberShippingAddress/getAddress',
	// 用户新增和编辑收货地址
	addAddress: 'after/memberShippingAddress/addAddress',
	//删除地址
	delAddress: "after/memberShippingAddress/delAddress",
	//获取省市县数据信息
	findSysAreaByParentId: 'front/sysArea/findSysAreaByParentId',
	//添加店铺收藏
	addMemberAttentionStore: "after/memberAttentionStore/addMemberAttentionStore",
	//店铺商品列表
	searchGoodList: "front/goodList/searchGoodList",
	/* 银行卡列表 */
	returnMemberBankCard: "after/memberBankCard/returnMemberBankCard",
	//添加,编辑银行卡
	addMemberBankCard: "after/memberBankCard/addMemberBankCard",
	//开户银行列表
	getSysBankList: "front/sysBank/getSysBankList",
	//银行卡详情
	returnMemberBankCardInfo: 'after/memberBankCard/returnMemberBankCardInfo',
	//取消关注店铺
	delMemberAttentionStore: "after/memberAttentionStore/delMemberAttentionStore",
	//字典取值
	getDicts: "front/sysDict/getDicts",
	// 可用余额明细
	findAccountCapitalByMemberId: "after/memberAccountCapital/findAccountCapitalByMemberId",
	// 待结算明细and不可用明细(new)
	findMemberRechargeRecordPage: "after/memberRechargeRecord/findMemberRechargeRecordPage",
	// 提现明细(new)
	findMemberWithdrawDepositPageByMemberId: "after/memberWithdrawDeposit/findMemberWithdrawDepositPageByMemberId",
	//提现金额计算
	withdrawXchanger: "after/memberWithdrawDeposit/withdrawXchanger",
	//获取提现说明
	getWithdrawWarmPrompt: "after/memberWithdrawDeposit/getWithdrawWarmPrompt",
	//获取最低提现金额
	getWithdrawMinimum: "after/memberWithdrawDeposit/getWithdrawMinimum",
	//获取商品规格
	getGoodBySpecification: 'front/goodList/getGoodBySpecification',
	//添加商品到购物车
	addGoodToShoppingCart: "after/memberShoppingCart/addGoodToShoppingCart",
	//地图获取附近位置（1）
	geocoder: "front/map/geocoder",
	//根据id删除购物车信息
	delCarGood: "after/memberShoppingCart/delCarGood",
	//修改购物车购买商品数量
	updateCarGood: "after/memberShoppingCart/updateCarGood",
	//商品批量收藏
	addMemberGoodsCollectionByGoodIds: "after/memberGoodsCollection/addMemberGoodsCollectionByGoodIds",
	// 获取购物车列表商品
	getCarGoodByMemberId: "/after/memberShoppingCart/getCarGoodByMemberId",
	//用户获取购物车商品数
	findCarGoods: 'after/memberShoppingCart/findCarGoods',
	//获取个人历史数据列表
	getUser: "front/searchHistory/getUser",
	//删除个人历史数据
	del: "front/searchHistory/del",
	//单品到确认订单（1）
	promptlyAffirmOrder: 'after/order/promptlyAffirmOrder',
	//购物车到确认订单接口
	affirmOrder: "after/order/affirmOrder",
	//兑换券线上兑换
	orderSubmitCertificate: "after/order/submitCertificate",
	//确认收货
	affirmOrderDelivery: "after/order/affirmOrderDelivery",
	//待付款取消订单
	abrogateOrder: "after/order/abrogateOrder",
	//订单查询接口
	orderList: "after/order/orderList",
	//提交订单到收银台(商品)
	submitOrder: "after/order/submitOrder",
	//收银台支付接口（1）
	payOrderCarLog: "after/payOrderCarLog/pay",
	//订单详情
	viewOrderInfo: "after/order/viewOrderInfo",
	//查看海报
	findMemberPromoter: "after/member/findMemberPromoter",
	//待支付订单计时器(1)
	prepaidOrderTimer: "after/order/prepaidOrderTimer",
	//确认收货计时器(1)
	confirmReceiptTimer: "after/order/confirmReceiptTimer",
	//查看物流
	parcelInformation: "after/orderProvider/parcelInformation",
	// 余额收银台（1）
	balanceToCashierDesk: "after/blance/toCashierDesk",
	//余额收银台支付
	blancePay: "after/blance/pay",
	//待付款订单支付(1)
	unpaidOrderSubmit: "after/order/unpaidOrderSubmit",
	//申请订单售后
	refundApply: "after/order/refund/apply",
	//修改申请
	refundEdit: "/after/order/refund/edit",
	//订单售后列表
	refundList: "after/order/refund/list",
	//撤销订单
	refundUndo: "after/order/refund/undo",
	//删除售后订单
	refundDelete: "/after/order/refund/delete",
	//售后订单详情
	refundQueryById: "after/order/refund/queryById",
	//待退货售后单单计时器
	refundOrderTimer: "after/order/refund/refundOrderTimer",
	//显示我的订单个数,浏览收藏个数(1)
	memberGoodAndOrderCount: "after/member/memberGoodAndOrderCount",
	//退货退款(换货)：填写物流信息
	editLogisticsInfo: '/after/order/refund/editLogisticsInfo',
	/* 用户完善资料接口 */
	completeInformation: "after/member/completeInformation",
	//图片上传 (图片显示地址前缀: /sys/common/view)
	upload: "after/common/upload",
	//提现到银行卡
	withdrawalCard: "after/memberWithdrawDeposit/withdrawalCard",
	//查看佣金明细
	findMemberRechargeRecordProtomerList: "after/memberRechargeRecord/findMemberRechargeRecordProtomerList",
	//删除银行卡
	deleteMemberBankCard: "after/memberBankCard/deleteMemberBankCard",
	//app手机号登录
	loginByPhone: "front/member/loginByPhone",
	//赚钱攻略
	findMarketingDistributionSetting: "front/marketingDistributionSetting/findMarketingDistributionSetting",
	//获取分销佣金统计
	distributionCommission: "/after/member/distributionCommission",
	//获取创业业绩统计
	businessPerformance: "/after/member/businessPerformance",
	//获取团队明细列表
	teamDetail: "/after/member/teamDetail",
	//获取分销订单列表
	distributionOrders:"/after/member/distributionOrders",
	//获取用户在指定店铺的补助金信息
	getMemberStoreSubsidyInfo: "/after/member/getMemberStoreSubsidyInfo",
	//计算订单确认页面的店铺补助金抵扣信息
	calculateOrderSubsidyDeduction: "/after/member/calculateOrderSubsidyDeduction",
	
	// ==================== 协议相关接口 ====================
	// 获取当前需用户同意的协议列表（公开接口）
	getCurrentRequiredAgreements: '/front/agreement/current-required',
	// 获取协议内容（公开接口）
	getAgreementContent: '/front/agreement/content',
	// 检查用户协议同意状态（需要登录）
	checkMemberAgreementStatus: '/after/agreement/check-status',
	// 确认用户同意协议（公开接口，用于登录前确认）
	confirmMemberAgreement: '/front/agreement/confirm',
	// 确认用户同意协议（需要登录，用于登录后记录）
	confirmMemberAgreementAfterLogin: '/after/agreement/confirm',

	// ==================== 二维码相关接口 ====================
	// 根据二维码id获取信息（公开接口）
	findSysSmallcodeById: '/front/sysSmallcode/findSysSmallcodeById',

	// ==================== 班级相关接口 ====================
	// 获取我的班级信息（专为排行榜页面优化）
	getMyClassInfo: '/after/eduClassMember/myClassInfo',
	// 获取当前班级信息
	getMyCurrentClass: '/after/eduClassMember/myCurrentClass',
	// 获取班级排行榜
	getClassRanking: '/after/eduClassMember/ranking',
	// 获取我的排名信息
	getMyRanking: '/after/eduClassMember/myRanking',
	// 获取排行榜统计数据
	getRankingStats: '/after/eduClassMember/rankingStats',

	// ==================== 任务发布模块 API ====================
	// 公开接口（无需登录）
	getTaskList: '/front/task/list',                    // 获取任务列表
	getTaskDetail: '/front/task/detail/{id}',           // 获取任务详情

	// 认证接口（需要登录）
	publishTask: '/after/task/publish',                 // 发布任务
	editTask: '/after/task/edit',                       // 编辑任务
	acceptTask: '/after/task/accept',                   // 接受任务
	submitTaskAudit: '/after/task/submit',              // 提交任务审核
	auditTask: '/after/task/audit',                     // 审核任务
	cancelTask: '/after/task/cancel',                   // 取消任务

	// 任务接受记录相关接口
	cancelAcceptanceRecord: '/after/taskAcceptanceRecord/cancel',  // 取消任务接受记录
	cancelAcceptanceRecord: '/after/taskAcceptanceRecord/cancel', // 取消任务接受记录
	getMyPublishedTasks: '/after/task/my-published',    // 我发布的任务列表
	getMyAcceptedTasks: '/after/task/my-accepted',      // 我接受的任务列表
	getPendingAuditTasks: '/after/task/pending-audit',  // 待审核任务列表
	getTaskRecord: '/after/task/record',                // 任务接受记录详情
};
uni.api = api;