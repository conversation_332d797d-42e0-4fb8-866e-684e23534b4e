# 后台管理系统网络请求最佳实践规范

## 概述
本文档总结了heartful-mall-web后台管理系统的网络请求最佳实践规范，基于axios和jeecg-boot框架的API封装体系。

## 技术栈版本信息
- **Vue版本**: 2.6.10
- **Axios版本**: 0.18.0
- **jeecg-boot框架**: 3.4.3
- **请求签名**: MD5签名机制
- **超时设置**: 180秒

## 核心架构

### 1. 请求封装层次结构
```
src/
├── utils/
│   ├── request.js          # axios实例配置、拦截器
│   └── axios.js            # VueAxios插件封装
├── api/
│   ├── manage.js           # 基础API方法封装
│   └── api.js              # 具体业务API定义
└── utils/encryption/
    └── signMd5Utils.js     # 签名工具
```

### 2. 基础配置
**文件位置**: `src/utils/request.js`

```javascript
import axios from 'axios'
import store from '@/store'
import { ACCESS_TOKEN, TENANT_ID } from "@/store/mutation-types"

// 动态获取API基础URL
let apiBaseUrl = window._CONFIG['domianURL'] || "/jeecg-boot";

// 创建axios实例
const service = axios.create({
  baseURL: apiBaseUrl,
  timeout: 180000  // 3分钟超时
})
```

## API方法封装

### 1. 标准HTTP方法
**文件位置**: `src/api/manage.js`

#### GET请求
```javascript
import { getAction } from '@/api/manage'

// 基础用法
getAction('/sys/user/list', { pageNo: 1, pageSize: 10 })
  .then(res => {
    if (res.success) {
      console.log(res.result)
    }
  })

// 在组件中使用
export default {
  methods: {
    async loadUserList() {
      try {
        const res = await getAction('/sys/user/list', this.queryParam)
        if (res.success) {
          this.dataSource = res.result.records
          this.ipagination.total = res.result.total
        } else {
          this.$message.warning(res.message)
        }
      } catch (error) {
        this.$message.error('加载数据失败')
      }
    }
  }
}
```

#### POST请求
```javascript
import { postAction } from '@/api/manage'

// 新增数据
postAction('/sys/user/add', {
  username: 'admin',
  realname: '管理员',
  email: '<EMAIL>'
}).then(res => {
  if (res.success) {
    this.$message.success('添加成功')
    this.loadData()
  } else {
    this.$message.error(res.message)
  }
})
```

#### PUT请求
```javascript
import { putAction } from '@/api/manage'

// 更新数据
putAction('/sys/user/edit', {
  id: '123',
  username: 'admin',
  realname: '超级管理员'
}).then(res => {
  if (res.success) {
    this.$message.success('更新成功')
  }
})
```

#### DELETE请求
```javascript
import { deleteAction } from '@/api/manage'

// 删除数据
deleteAction('/sys/user/delete', { id: '123' })
  .then(res => {
    if (res.success) {
      this.$message.success('删除成功')
      this.loadData()
    }
  })
```

### 2. 通用HTTP方法
```javascript
import { httpAction } from '@/api/manage'

// 自定义HTTP方法
httpAction('/sys/user/batch', userData, 'patch')
  .then(res => {
    console.log(res)
  })
```

## 文件操作

### 1. 文件上传
```javascript
import { uploadAction } from '@/api/manage'

// 文件上传
const formData = new FormData()
formData.append('file', file)

uploadAction('/sys/common/upload', formData)
  .then(res => {
    if (res.success) {
      this.form.avatar = res.result.url
      this.$message.success('上传成功')
    }
  })
```

### 2. 文件下载
```javascript
import { downloadFile } from '@/api/manage'

// Excel导出下载
downloadFile('/sys/user/exportXls', this.queryParam, 'user_list.xlsx')
  .then(() => {
    this.$message.success('导出成功')
  })
```

### 3. 获取文件访问URL
```javascript
import { getFileAccessHttpUrl } from '@/api/manage'

// 获取文件完整访问路径
const imageUrl = getFileAccessHttpUrl(this.record.avatar)
```

## 请求拦截器配置

### 1. 请求前拦截器
```javascript
// 自动添加认证Token
service.interceptors.request.use(config => {
  const token = Vue.ls.get(ACCESS_TOKEN)
  if (token) {
    config.headers['X-Access-Token'] = token
  }

  // 多租户支持
  let tenantid = Vue.ls.get(TENANT_ID) || 0
  config.headers['tenant-id'] = tenantid

  // GET请求添加时间戳防缓存
  if (config.method === 'get') {
    config.params = {
      _t: Date.parse(new Date()) / 1000,
      ...config.params
    }
  }

  return config
})
```

### 2. 响应拦截器
```javascript
// 统一响应处理
service.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 403:
          Vue.prototype.$notification.error({
            message: '系统提示',
            description: '拒绝访问'
          })
          break
        case 500:
          if (error.response.data.message.includes("Token失效")) {
            // Token失效处理
            Vue.prototype.$modal.error({
              title: '登录已过期',
              content: '很抱歉，登录已过期，请重新登录',
              onOk: () => {
                store.dispatch('Logout').then(() => {
                  window.location.reload()
                })
              }
            })
          }
          break
      }
    }
    return Promise.reject(error)
  }
)
```

## 签名机制

### 1. 自动签名
所有POST、GET、PUT请求都会自动添加MD5签名：

```javascript
// 签名自动添加到请求头
let signHeader = {
  "X-Sign": sign,
  "X-TIMESTAMP": timestamp
}
```

### 2. 签名算法
```javascript
// 签名生成过程
1. 合并URL参数和请求体参数
2. 参数按key升序排序
3. JSON.stringify(sortedParams) + signatureSecret
4. MD5加密并转大写
```

## 错误处理最佳实践

### 1. 统一错误处理模式
```javascript
export default {
  methods: {
    async handleApiCall() {
      try {
        const res = await getAction('/api/data', this.params)
        
        if (res.success) {
          // 成功处理
          this.dataSource = res.result
          return res.result
        } else {
          // 业务错误处理
          this.$message.warning(res.message || '操作失败')
          return null
        }
      } catch (error) {
        // 网络错误处理
        console.error('API调用失败:', error)
        this.$message.error('网络错误，请稍后重试')
        return null
      }
    }
  }
}
```

### 2. 特殊状态码处理
```javascript
// 在API调用中处理特殊状态码
getAction('/api/data', params).then(res => {
  if (res.success) {
    // 成功处理
  } else if (res.code === 510) {
    // 特殊业务状态码
    this.$message.warning(res.message)
  } else {
    // 其他错误
    this.$message.error(res.message)
  }
})
```

## 业务API定义规范

### 1. API路径集中管理
**文件位置**: `src/api/api.js`

```javascript
// 用户管理API
const addUser = (params) => postAction("/sys/user/add", params)
const editUser = (params) => putAction("/sys/user/edit", params)
const getUserList = (params) => getAction("/sys/user/list", params)

// 角色管理API
const addRole = (params) => postAction("/sys/role/add", params)
const editRole = (params) => putAction("/sys/role/edit", params)

// 导出API方法
export {
  addUser,
  editUser,
  getUserList,
  addRole,
  editRole
}
```

### 2. 在组件中使用
```javascript
import { getUserList, addUser, editUser } from '@/api/api'

export default {
  methods: {
    // 加载用户列表
    async loadData() {
      const res = await getUserList(this.queryParam)
      if (res.success) {
        this.dataSource = res.result.records
      }
    },

    // 保存用户
    async saveUser() {
      const apiMethod = this.form.id ? editUser : addUser
      const res = await apiMethod(this.form)
      
      if (res.success) {
        this.$message.success('保存成功')
        this.loadData()
      }
    }
  }
}
```

## 性能优化建议

### 1. 请求防抖
```javascript
import { debounce } from 'lodash'

export default {
  methods: {
    // 搜索防抖
    handleSearch: debounce(function(keyword) {
      this.queryParam.keyword = keyword
      this.loadData()
    }, 300)
  }
}
```

### 2. 请求取消
```javascript
import axios from 'axios'

export default {
  data() {
    return {
      cancelTokenSource: null
    }
  },
  methods: {
    async loadData() {
      // 取消之前的请求
      if (this.cancelTokenSource) {
        this.cancelTokenSource.cancel('新请求取消旧请求')
      }

      // 创建新的取消令牌
      this.cancelTokenSource = axios.CancelToken.source()

      try {
        const res = await getAction('/api/data', this.params, {
          cancelToken: this.cancelTokenSource.token
        })
        // 处理响应
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error('请求失败:', error)
        }
      }
    }
  },
  beforeDestroy() {
    // 组件销毁时取消请求
    if (this.cancelTokenSource) {
      this.cancelTokenSource.cancel('组件销毁')
    }
  }
}
```

## JeecgListMixin网络请求模式

### 1. 标准列表页面网络请求
**最重要的jeecg-boot特性**：使用JeecgListMixin简化列表页面开发

```javascript
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  mixins: [JeecgListMixin],
  data() {
    return {
      // 必须配置的URL
      url: {
        list: "/sys/user/list",           // 列表查询
        delete: "/sys/user/delete",       // 单个删除
        deleteBatch: "/sys/user/deleteBatch", // 批量删除
        exportXls: "/sys/user/exportXls"  // 导出Excel
      }
    }
  },

  // 自动获得以下功能：
  // - loadData() 方法
  // - 分页参数 ipagination
  // - 查询参数 queryParam
  // - 批量删除 batchDel()
  // - 导出功能 handleExportXls()
}
```

### 2. 分页查询标准实现
```javascript
// JeecgListMixin自动提供的分页参数
data() {
  return {
    ipagination: {
      current: 1,                    // 当前页
      pageSize: 10,                  // 每页条数
      pageSizeOptions: ['10', '20', '30'],
      showTotal: (total, range) => {
        return range[0] + '-' + range[1] + ' 共' + total + '条'
      },
      showQuickJumper: true,         // 快速跳转
      showSizeChanger: true,         // 页面大小选择
      total: 0                       // 总条数
    }
  }
}

// 自动生成查询参数
getQueryParams() {
  let sqp = {}
  if (this.superQueryParams) {
    sqp['superQueryParams'] = encodeURI(this.superQueryParams)
    sqp['superQueryMatchType'] = this.superQueryMatchType
  }

  var param = Object.assign(sqp, this.queryParam, this.isorter, this.filters)
  param.field = this.getQueryField()
  param.pageNo = this.ipagination.current
  param.pageSize = this.ipagination.pageSize
  return filterObj(param)
}
```

### 3. 批量操作标准模式
```javascript
// JeecgListMixin提供的批量删除
batchDel() {
  if (!this.url.deleteBatch) {
    this.$message.error('请设置url.deleteBatch属性!')
    return
  }

  if (this.selectedRowKeys.length <= 0) {
    this.$message.warning('请选择一条记录！')
    return
  }

  var ids = this.selectedRowKeys.join(',')

  this.$confirm({
    title: '确认删除',
    content: '是否删除选中数据?',
    onOk: () => {
      this.loading = true
      deleteAction(this.url.deleteBatch, { ids })
        .then(res => {
          if (res.success) {
            // 重新计算分页
            this.reCalculatePage(this.selectedRowKeys.length)
            this.$message.success(res.message)
            this.loadData()
            this.onClearSelected()
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    }
  })
}
```

## 字典数据网络请求处理

### 1. 字典数据获取机制
```javascript
import { ajaxGetDictItems, getDictItemsFromCache } from '@/api/api'

// 字典数据获取（带缓存）
async function initDictOptions(dictCode) {
  if (!dictCode) {
    return '字典Code不能为空!'
  }

  // 优先从缓存中读取
  if (getDictItemsFromCache(dictCode)) {
    let res = {}
    res.result = getDictItemsFromCache(dictCode)
    res.success = true
    return res
  }

  // 缓存中没有，请求API
  let res = await ajaxGetDictItems(dictCode)
  return res
}
```

### 2. 字典组件网络请求
```javascript
// 在组件中使用字典
export default {
  data() {
    return {
      dictOptions: []
    }
  },

  created() {
    this.initDictConfig()
  },

  methods: {
    async initDictConfig() {
      // 初始化字典配置
      const res = await initDictOptions('user_status')
      if (res.success) {
        this.dictOptions = res.result
      }
    }
  }
}
```

## 特殊Content-Type处理

### 1. application/x-www-form-urlencoded请求
```javascript
import { postApplicationAction } from '@/api/manage'

// 特殊的表单提交方式
postApplicationAction('/sys/user/submit', {
  username: 'admin',
  password: '123456'
}).then(res => {
  console.log(res)
})

// 注意：postApplicationAction使用params而不是data
// 自动设置Content-Type为application/x-www-form-urlencoded
```

### 2. 文件上传的特殊处理
```javascript
// 文件上传时的Token失效处理
handleUploadChange(info) {
  if (info.file.status === 'uploading') {
    this.loading = true
  } else if (info.file.status === 'done') {
    this.loading = false
    // 上传成功处理
  } else if (info.file.status === 'error') {
    this.loading = false

    // 特殊的Token失效处理
    if (info.file.response.status === 500) {
      let data = info.file.response
      const token = Vue.ls.get(ACCESS_TOKEN)

      if (token && data.message.includes('Token失效')) {
        this.$error({
          title: '登录已过期',
          content: '很抱歉，登录已过期，请重新登录',
          okText: '重新登录',
          onOk: () => {
            store.dispatch('Logout').then(() => {
              Vue.ls.remove(ACCESS_TOKEN)
              window.location.reload()
            })
          }
        })
      }
    }
  }
}
```

## 图片预览和文件处理

### 1. 图片预览标准处理
```javascript
// JeecgListMixin提供的图片预览方法
getImgView(text) {
  if (text && text.indexOf(',') > 0) {
    text = text.substring(0, text.indexOf(','))
  }
  return getFileAccessHttpUrl(text)
}

// 在模板中使用
// <img :src="getImgView(record.avatar)" />
```

### 2. 文件访问URL处理
```javascript
import { getFileAccessHttpUrl } from '@/api/manage'

// 获取完整的文件访问路径
const getFullFileUrl = (filePath) => {
  return getFileAccessHttpUrl(filePath)
}

// 处理多个文件的情况
const getFirstImageUrl = (imageList) => {
  if (imageList && imageList.indexOf(',') > 0) {
    return getFileAccessHttpUrl(imageList.split(',')[0])
  }
  return getFileAccessHttpUrl(imageList)
}
```

## 高级网络请求模式

### 1. 条件查询和高级搜索
```javascript
// JeecgListMixin支持的高级查询
export default {
  mixins: [JeecgListMixin],
  data() {
    return {
      superQueryFlag: false,        // 高级查询标志
      superQueryParams: '',         // 高级查询参数
      superQueryMatchType: 'and'    // 查询匹配方式
    }
  },

  methods: {
    // 搜索前的参数处理
    beforeSearch(params) {
      // 对查询参数进行预处理
      if (params.createTime) {
        params.createTime_begin = params.createTime[0]
        params.createTime_end = params.createTime[1]
        delete params.createTime
      }
    }
  }
}
```

### 2. 导出功能网络请求
```javascript
// JeecgListMixin提供的导出功能
handleExportXls(fileName) {
  if (!this.url.exportXls) {
    this.$message.error('请设置url.exportXls属性!')
    return
  }

  if (!fileName || typeof fileName !== 'string') {
    fileName = '导出文件'
  }

  let param = { ...this.queryParam }
  if (this.selectedRowKeys && this.selectedRowKeys.length > 0) {
    param['selections'] = this.selectedRowKeys.join(',')
  }

  this.exportLoading = true
  downloadFile(this.url.exportXls, param, `${fileName}.xlsx`)
    .finally(() => {
      this.exportLoading = false
    })
}
```

## 总结

1. **必须使用JeecgListMixin简化列表页面开发**
2. **充分利用jeecg-boot的分页、批量操作、导出功能**
3. **正确处理字典数据的缓存和网络请求**
4. **使用正确的Content-Type处理不同类型的请求**
5. **实现完整的Token失效处理机制**
6. **遵循jeecg-boot的图片预览和文件处理规范**

遵循这些规范可以确保网络请求的安全性、一致性和可维护性，充分发挥jeecg-boot框架的优势。
