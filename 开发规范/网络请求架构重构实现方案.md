# 🏗️ 网络请求架构重构实现方案

## 📋 概述

本文档提供了heartful-mall-app C端用户小程序网络请求架构重构的具体实现方案，包括配置化管理、模块化设计、插件化架构等。

---

## ⚙️ 1. 配置化管理实现

### 1.1 环境配置管理

**文件位置**: `hooks/config/EnvConfig.js`

```javascript
/**
 * 环境配置管理器
 * 统一管理不同环境的配置信息
 */
class EnvConfig {
  constructor() {
    this.currentEnv = this.detectEnvironment();
    this.configs = {
      development: {
        baseURL: 'https://dev-api.heartful-mall.com',
        timeout: 30000,
        enableDebug: true,
        enableMock: false,
        enableSigning: false,
        enableRateLimit: false,
        logLevel: 'debug'
      },
      testing: {
        baseURL: 'https://test-api.heartful-mall.com',
        timeout: 30000,
        enableDebug: true,
        enableMock: false,
        enableSigning: true,
        enableRateLimit: true,
        logLevel: 'info'
      },
      production: {
        baseURL: 'https://www.zehuaiit.top/heartful-mall-api/',
        timeout: 30000,
        enableDebug: false,
        enableMock: false,
        enableSigning: true,
        enableRateLimit: true,
        logLevel: 'error'
      }
    };
  }

  /**
   * 检测当前环境
   * @returns {string} 环境名称
   */
  detectEnvironment() {
    // 通过uni.env或其他方式检测环境
    if (uni.env && uni.env.NODE_ENV) {
      return uni.env.NODE_ENV;
    }
    
    // 根据域名判断环境
    const url = uni.env.REQUEST_URL || '';
    if (url.includes('dev-api')) return 'development';
    if (url.includes('test-api')) return 'testing';
    return 'production';
  }

  /**
   * 获取当前环境配置
   * @returns {Object} 环境配置
   */
  getCurrentConfig() {
    return this.configs[this.currentEnv] || this.configs.production;
  }

  /**
   * 获取指定配置项
   * @param {string} key - 配置键
   * @param {any} defaultValue - 默认值
   * @returns {any} 配置值
   */
  get(key, defaultValue = null) {
    const config = this.getCurrentConfig();
    return config[key] !== undefined ? config[key] : defaultValue;
  }

  /**
   * 动态更新配置
   * @param {string} key - 配置键
   * @param {any} value - 配置值
   */
  set(key, value) {
    const config = this.getCurrentConfig();
    config[key] = value;
    console.log(`配置已更新: ${key} = ${value}`);
  }

  /**
   * 获取当前环境名称
   * @returns {string} 环境名称
   */
  getEnvironment() {
    return this.currentEnv;
  }

  /**
   * 是否为开发环境
   * @returns {boolean} 是否为开发环境
   */
  isDevelopment() {
    return this.currentEnv === 'development';
  }

  /**
   * 是否为生产环境
   * @returns {boolean} 是否为生产环境
   */
  isProduction() {
    return this.currentEnv === 'production';
  }
}

// 创建单例实例
const envConfig = new EnvConfig();

export default envConfig;
```

### 1.2 API配置管理

**文件位置**: `hooks/config/ApiConfig.js`

```javascript
import envConfig from './EnvConfig.js';

/**
 * API配置管理器
 * 统一管理API接口配置和元数据
 */
class ApiConfig {
  constructor() {
    this.apiRoutes = this.initApiRoutes();
    this.apiMetadata = this.initApiMetadata();
    this.cacheConfig = this.initCacheConfig();
    this.rateLimitConfig = this.initRateLimitConfig();
  }

  /**
   * 初始化API路由配置
   * @returns {Object} API路由配置
   */
  initApiRoutes() {
    return {
      // 用户认证相关
      auth: {
        loginByCode: '/front/weixin/loginByCode',
        refreshToken: '/front/weixin/refreshToken',
        getMemberInfo: '/after/member/getMemberInfo',
        logout: '/after/member/logout'
      },
      
      // 商城相关
      mall: {
        indexNew: '/front/index/indexNew',
        advertisingList: '/front/MarketingAdvertising/findarketingAdvertisingList',
        searchGoods: '/front/goodList/searchHostStoreGoodList',
        goodDetail: '/front/goodList/findGoodListByGoodId'
      },
      
      // 订单相关
      order: {
        promptlyAffirm: '/after/order/promptlyAffirmOrder',
        submit: '/after/order/submitOrder',
        list: '/after/order/orderList',
        detail: '/after/order/viewOrderInfo'
      },
      
      // 任务相关
      task: {
        myPublished: '/after/task/my-published',
        myAccepted: '/after/task/my-accepted',
        pendingAudit: '/after/task/pending-audit',
        cancelAcceptance: '/after/taskAcceptanceRecord/cancel'
      },
      
      // 班级相关
      class: {
        myInfo: '/after/eduClassMember/myClassInfo',
        ranking: '/after/eduClassMember/ranking'
      },
      
      // 购物车相关
      cart: {
        addGood: '/after/memberShoppingCart/addGoodToShoppingCart',
        findGoods: '/after/memberShoppingCart/findCarGoods'
      }
    };
  }

  /**
   * 初始化API元数据配置
   * @returns {Object} API元数据配置
   */
  initApiMetadata() {
    return {
      // 需要登录的接口前缀
      authRequiredPrefixes: ['/after/', '/back/'],
      
      // 公开接口前缀
      publicPrefixes: ['/front/', '/before/'],
      
      // 特殊处理接口
      specialHandling: {
        'orderStorePickUp/verification': { 
          allowError500: true,
          description: '扫码核销接口'
        },
        'payment/callback': { 
          allowError500: true,
          description: '支付回调接口'
        }
      },
      
      // 接口版本映射
      versionMapping: {
        'v1': {
          prefix: '/api/v1',
          deprecated: false
        },
        'v2': {
          prefix: '/api/v2',
          deprecated: false
        }
      }
    };
  }

  /**
   * 初始化缓存配置
   * @returns {Object} 缓存配置
   */
  initCacheConfig() {
    return {
      // 首页数据缓存5分钟
      '/front/index/indexNew': {
        ttl: 5 * 60 * 1000,
        enabled: true,
        strategy: 'memory'
      },
      
      // 用户信息缓存10分钟
      '/after/member/getMemberInfo': {
        ttl: 10 * 60 * 1000,
        enabled: true,
        strategy: 'storage'
      },
      
      // 商品详情缓存3分钟
      '/front/goodList/findGoodListByGoodId': {
        ttl: 3 * 60 * 1000,
        enabled: true,
        strategy: 'memory'
      },
      
      // 广告列表缓存30分钟
      '/front/MarketingAdvertising/findarketingAdvertisingList': {
        ttl: 30 * 60 * 1000,
        enabled: true,
        strategy: 'storage'
      }
    };
  }

  /**
   * 初始化频率限制配置
   * @returns {Object} 频率限制配置
   */
  initRateLimitConfig() {
    return {
      // 登录接口限制
      '/after/': {
        limit: 100,
        window: 60000,
        description: '登录接口限制'
      },
      
      // 公开接口限制
      '/front/': {
        limit: 200,
        window: 60000,
        description: '公开接口限制'
      },
      
      // 订单接口限制
      '/after/order/': {
        limit: 10,
        window: 60000,
        description: '订单接口限制'
      },
      
      // 支付接口限制
      '/after/payment/': {
        limit: 5,
        window: 60000,
        description: '支付接口限制'
      }
    };
  }

  /**
   * 获取API路径
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @returns {string} API路径
   */
  getApiPath(category, action) {
    const categoryRoutes = this.apiRoutes[category];
    if (!categoryRoutes) {
      throw new Error(`API分类不存在: ${category}`);
    }
    
    const apiPath = categoryRoutes[action];
    if (!apiPath) {
      throw new Error(`API动作不存在: ${category}.${action}`);
    }
    
    return apiPath;
  }

  /**
   * 获取完整的API URL
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {string} version - API版本
   * @returns {string} 完整API URL
   */
  getFullApiUrl(category, action, version = null) {
    const apiPath = this.getApiPath(category, action);
    const baseURL = envConfig.get('baseURL');
    
    if (version && this.apiMetadata.versionMapping[version]) {
      const versionPrefix = this.apiMetadata.versionMapping[version].prefix;
      return `${baseURL}${versionPrefix}${apiPath}`;
    }
    
    return `${baseURL}${apiPath}`;
  }

  /**
   * 检查API是否需要认证
   * @param {string} apiPath - API路径
   * @returns {boolean} 是否需要认证
   */
  requiresAuth(apiPath) {
    return this.apiMetadata.authRequiredPrefixes.some(prefix => 
      apiPath.startsWith(prefix)
    );
  }

  /**
   * 获取API缓存配置
   * @param {string} apiPath - API路径
   * @returns {Object|null} 缓存配置
   */
  getCacheConfig(apiPath) {
    return this.cacheConfig[apiPath] || null;
  }

  /**
   * 获取API频率限制配置
   * @param {string} apiPath - API路径
   * @returns {Object|null} 频率限制配置
   */
  getRateLimitConfig(apiPath) {
    // 按最具体的匹配规则查找配置
    const sortedKeys = Object.keys(this.rateLimitConfig)
      .sort((a, b) => b.length - a.length);
    
    for (const prefix of sortedKeys) {
      if (apiPath.startsWith(prefix)) {
        return this.rateLimitConfig[prefix];
      }
    }
    
    return null;
  }

  /**
   * 检查API是否需要特殊处理
   * @param {string} apiPath - API路径
   * @returns {Object|null} 特殊处理配置
   */
  getSpecialHandling(apiPath) {
    for (const [pattern, config] of Object.entries(this.apiMetadata.specialHandling)) {
      if (apiPath.includes(pattern)) {
        return config;
      }
    }
    return null;
  }

  /**
   * 动态添加API路由
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {string} path - API路径
   */
  addApiRoute(category, action, path) {
    if (!this.apiRoutes[category]) {
      this.apiRoutes[category] = {};
    }
    
    this.apiRoutes[category][action] = path;
    console.log(`API路由已添加: ${category}.${action} -> ${path}`);
  }

  /**
   * 动态更新缓存配置
   * @param {string} apiPath - API路径
   * @param {Object} config - 缓存配置
   */
  updateCacheConfig(apiPath, config) {
    this.cacheConfig[apiPath] = {
      ttl: config.ttl || 5 * 60 * 1000,
      enabled: config.enabled !== false,
      strategy: config.strategy || 'memory'
    };
    console.log(`缓存配置已更新: ${apiPath}`, this.cacheConfig[apiPath]);
  }

  /**
   * 获取所有API路由（用于调试）
   * @returns {Object} 所有API路由
   */
  getAllRoutes() {
    return this.apiRoutes;
  }

  /**
   * 验证配置完整性
   * @returns {Object} 验证结果
   */
  validateConfig() {
    const result = {
      valid: true,
      errors: [],
      warnings: []
    };

    // 检查必需的配置项
    const requiredConfigs = ['baseURL', 'timeout'];
    for (const config of requiredConfigs) {
      if (!envConfig.get(config)) {
        result.valid = false;
        result.errors.push(`缺少必需配置: ${config}`);
      }
    }

    // 检查API路由完整性
    for (const [category, routes] of Object.entries(this.apiRoutes)) {
      for (const [action, path] of Object.entries(routes)) {
        if (!path || typeof path !== 'string') {
          result.valid = false;
          result.errors.push(`无效的API路径: ${category}.${action}`);
        }
      }
    }

    return result;
  }
}

// 创建单例实例
const apiConfig = new ApiConfig();

export default apiConfig;
```

### 1.3 请求配置构建器

**文件位置**: `hooks/config/RequestConfigBuilder.js`

```javascript
import envConfig from './EnvConfig.js';
import apiConfig from './ApiConfig.js';

/**
 * 请求配置构建器
 * 根据环境和API配置动态构建请求配置
 */
class RequestConfigBuilder {
  constructor() {
    this.defaultConfig = this.buildDefaultConfig();
  }

  /**
   * 构建默认配置
   * @returns {Object} 默认配置
   */
  buildDefaultConfig() {
    return {
      baseURL: envConfig.get('baseURL'),
      timeout: envConfig.get('timeout', 30000),
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      dataType: 'json',
      responseType: 'text'
    };
  }

  /**
   * 构建请求配置
   * @param {Object} options - 配置选项
   * @returns {Object} 请求配置
   */
  build(options = {}) {
    const {
      category,
      action,
      method = 'GET',
      params = {},
      data = {},
      headers = {},
      timeout,
      enableCache,
      cacheTime,
      version
    } = options;

    // 基础配置
    const config = {
      ...this.defaultConfig,
      method: method.toUpperCase()
    };

    // 设置URL
    if (category && action) {
      config.url = apiConfig.getApiPath(category, action);
    } else if (options.url) {
      config.url = options.url;
    } else {
      throw new Error('必须提供API路径或category/action');
    }

    // 设置参数
    if (method.toUpperCase() === 'GET') {
      config.params = params;
    } else {
      config.data = data;
      if (Object.keys(params).length > 0) {
        config.params = params;
      }
    }

    // 合并请求头
    config.header = {
      ...config.header,
      ...headers
    };

    // 设置超时时间
    if (timeout) {
      config.timeout = timeout;
    }

    // 缓存配置
    const cacheConfig = apiConfig.getCacheConfig(config.url);
    if (enableCache !== false && cacheConfig?.enabled) {
      config.enableCache = true;
      config.cacheTime = cacheTime || cacheConfig.ttl;
      config.cacheStrategy = cacheConfig.strategy;
    }

    // 添加元数据
    config.metadata = {
      category,
      action,
      requiresAuth: apiConfig.requiresAuth(config.url),
      specialHandling: apiConfig.getSpecialHandling(config.url),
      rateLimitConfig: apiConfig.getRateLimitConfig(config.url)
    };

    return config;
  }

  /**
   * 构建GET请求配置
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} params - 查询参数
   * @param {Object} options - 其他选项
   * @returns {Object} 请求配置
   */
  buildGet(category, action, params = {}, options = {}) {
    return this.build({
      category,
      action,
      method: 'GET',
      params,
      ...options
    });
  }

  /**
   * 构建POST请求配置
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} data - 请求体数据
   * @param {Object} options - 其他选项
   * @returns {Object} 请求配置
   */
  buildPost(category, action, data = {}, options = {}) {
    return this.build({
      category,
      action,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * 构建PUT请求配置
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} data - 请求体数据
   * @param {Object} options - 其他选项
   * @returns {Object} 请求配置
   */
  buildPut(category, action, data = {}, options = {}) {
    return this.build({
      category,
      action,
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * 构建DELETE请求配置
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} params - 查询参数
   * @param {Object} options - 其他选项
   * @returns {Object} 请求配置
   */
  buildDelete(category, action, params = {}, options = {}) {
    return this.build({
      category,
      action,
      method: 'DELETE',
      params,
      ...options
    });
  }

  /**
   * 克隆配置
   * @param {Object} config - 原始配置
   * @returns {Object} 克隆的配置
   */
  clone(config) {
    return JSON.parse(JSON.stringify(config));
  }

  /**
   * 合并配置
   * @param {Object} baseConfig - 基础配置
   * @param {Object} overrideConfig - 覆盖配置
   * @returns {Object} 合并后的配置
   */
  merge(baseConfig, overrideConfig) {
    const merged = this.clone(baseConfig);
    
    // 深度合并
    for (const [key, value] of Object.entries(overrideConfig)) {
      if (key === 'header' && typeof value === 'object') {
        merged.header = { ...merged.header, ...value };
      } else if (key === 'params' && typeof value === 'object') {
        merged.params = { ...merged.params, ...value };
      } else {
        merged[key] = value;
      }
    }
    
    return merged;
  }
}

// 创建单例实例
const requestConfigBuilder = new RequestConfigBuilder();

export default requestConfigBuilder;
```

---

## 🔌 2. 插件化架构实现

### 2.1 插件管理器

**文件位置**: `hooks/plugins/PluginManager.js`

```javascript
/**
 * 插件管理器
 * 提供插件的注册、加载、卸载功能
 */
class PluginManager {
  constructor() {
    this.plugins = new Map();
    this.hooks = new Map();
    this.loadedPlugins = new Set();
  }

  /**
   * 注册插件
   * @param {string} name - 插件名称
   * @param {Object} plugin - 插件对象
   */
  register(name, plugin) {
    if (this.plugins.has(name)) {
      console.warn(`插件 ${name} 已存在，将被覆盖`);
    }

    // 验证插件结构
    if (!this.validatePlugin(plugin)) {
      throw new Error(`插件 ${name} 结构无效`);
    }

    this.plugins.set(name, plugin);
    console.log(`插件 ${name} 注册成功`);
  }

  /**
   * 加载插件
   * @param {string} name - 插件名称
   * @param {Object} options - 插件选项
   */
  async load(name, options = {}) {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      throw new Error(`插件 ${name} 不存在`);
    }

    if (this.loadedPlugins.has(name)) {
      console.warn(`插件 ${name} 已加载`);
      return;
    }

    try {
      // 调用插件的install方法
      if (typeof plugin.install === 'function') {
        await plugin.install(this, options);
      }

      // 注册插件的钩子
      if (plugin.hooks) {
        this.registerHooks(name, plugin.hooks);
      }

      this.loadedPlugins.add(name);
      console.log(`插件 ${name} 加载成功`);
    } catch (error) {
      console.error(`插件 ${name} 加载失败:`, error);
      throw error;
    }
  }

  /**
   * 卸载插件
   * @param {string} name - 插件名称
   */
  async unload(name) {
    const plugin = this.plugins.get(name);
    if (!plugin || !this.loadedPlugins.has(name)) {
      console.warn(`插件 ${name} 未加载`);
      return;
    }

    try {
      // 调用插件的uninstall方法
      if (typeof plugin.uninstall === 'function') {
        await plugin.uninstall(this);
      }

      // 移除插件的钩子
      this.unregisterHooks(name);

      this.loadedPlugins.delete(name);
      console.log(`插件 ${name} 卸载成功`);
    } catch (error) {
      console.error(`插件 ${name} 卸载失败:`, error);
      throw error;
    }
  }

  /**
   * 验证插件结构
   * @param {Object} plugin - 插件对象
   * @returns {boolean} 是否有效
   */
  validatePlugin(plugin) {
    if (!plugin || typeof plugin !== 'object') {
      return false;
    }

    // 必需的属性
    const requiredProps = ['name', 'version'];
    for (const prop of requiredProps) {
      if (!plugin[prop]) {
        console.error(`插件缺少必需属性: ${prop}`);
        return false;
      }
    }

    return true;
  }

  /**
   * 注册钩子
   * @param {string} pluginName - 插件名称
   * @param {Object} hooks - 钩子对象
   */
  registerHooks(pluginName, hooks) {
    for (const [hookName, handler] of Object.entries(hooks)) {
      if (!this.hooks.has(hookName)) {
        this.hooks.set(hookName, []);
      }

      this.hooks.get(hookName).push({
        plugin: pluginName,
        handler
      });
    }
  }

  /**
   * 移除钩子
   * @param {string} pluginName - 插件名称
   */
  unregisterHooks(pluginName) {
    for (const [hookName, handlers] of this.hooks.entries()) {
      const filtered = handlers.filter(h => h.plugin !== pluginName);
      this.hooks.set(hookName, filtered);
    }
  }

  /**
   * 执行钩子
   * @param {string} hookName - 钩子名称
   * @param {any} data - 传递给钩子的数据
   * @returns {any} 处理后的数据
   */
  async executeHook(hookName, data) {
    const handlers = this.hooks.get(hookName) || [];
    let result = data;

    for (const { plugin, handler } of handlers) {
      try {
        if (typeof handler === 'function') {
          result = await handler(result, { plugin, hookName });
        }
      } catch (error) {
        console.error(`钩子 ${hookName} 在插件 ${plugin} 中执行失败:`, error);
      }
    }

    return result;
  }

  /**
   * 获取已加载的插件列表
   * @returns {Array} 插件列表
   */
  getLoadedPlugins() {
    return Array.from(this.loadedPlugins);
  }

  /**
   * 获取插件信息
   * @param {string} name - 插件名称
   * @returns {Object|null} 插件信息
   */
  getPluginInfo(name) {
    const plugin = this.plugins.get(name);
    if (!plugin) return null;

    return {
      name: plugin.name,
      version: plugin.version,
      description: plugin.description || '',
      loaded: this.loadedPlugins.has(name)
    };
  }
}

// 创建单例实例
const pluginManager = new PluginManager();

export default pluginManager;
```

### 2.2 缓存插件实现

**文件位置**: `hooks/plugins/CachePlugin.js`

```javascript
/**
 * 缓存插件
 * 提供内存缓存和本地存储缓存功能
 */
class CachePlugin {
  constructor() {
    this.name = 'CachePlugin';
    this.version = '1.0.0';
    this.description = '请求缓存插件';

    this.memoryCache = new Map();
    this.storagePrefix = 'api_cache_';
  }

  /**
   * 插件安装
   * @param {Object} pluginManager - 插件管理器
   * @param {Object} options - 选项
   */
  async install(pluginManager, options = {}) {
    this.options = {
      enableMemoryCache: true,
      enableStorageCache: true,
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxMemorySize: 100, // 最大内存缓存条目数
      ...options
    };

    console.log('缓存插件安装成功', this.options);
  }

  /**
   * 插件卸载
   * @param {Object} pluginManager - 插件管理器
   */
  async uninstall(pluginManager) {
    this.clearMemoryCache();
    console.log('缓存插件卸载成功');
  }

  /**
   * 插件钩子
   */
  get hooks() {
    return {
      'before-request': this.beforeRequest.bind(this),
      'after-response': this.afterResponse.bind(this)
    };
  }

  /**
   * 请求前钩子 - 检查缓存
   * @param {Object} config - 请求配置
   * @returns {Object} 处理后的配置
   */
  async beforeRequest(config) {
    if (!config.enableCache) {
      return config;
    }

    const cacheKey = this.generateCacheKey(config);
    const cached = await this.getCache(cacheKey, config.cacheStrategy);

    if (cached) {
      console.log('使用缓存数据:', config.url);
      // 返回缓存的响应数据
      config._cached = true;
      config._cachedResponse = cached;
    }

    return config;
  }

  /**
   * 响应后钩子 - 存储缓存
   * @param {Object} response - 响应对象
   * @returns {Object} 处理后的响应
   */
  async afterResponse(response) {
    const config = response.config;

    if (config.enableCache && !config._cached && response.data?.success) {
      const cacheKey = this.generateCacheKey(config);
      const ttl = config.cacheTime || this.options.defaultTTL;

      await this.setCache(cacheKey, response.data, ttl, config.cacheStrategy);
      console.log('缓存响应数据:', config.url);
    }

    return response;
  }

  /**
   * 生成缓存键
   * @param {Object} config - 请求配置
   * @returns {string} 缓存键
   */
  generateCacheKey(config) {
    const url = config.url;
    const params = JSON.stringify(config.params || {});
    const data = JSON.stringify(config.data || {});

    return `${url}_${params}_${data}`;
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @param {string} strategy - 缓存策略
   * @returns {any} 缓存数据
   */
  async getCache(key, strategy = 'memory') {
    if (strategy === 'memory' && this.options.enableMemoryCache) {
      return this.getMemoryCache(key);
    } else if (strategy === 'storage' && this.options.enableStorageCache) {
      return this.getStorageCache(key);
    }

    return null;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} ttl - 过期时间
   * @param {string} strategy - 缓存策略
   */
  async setCache(key, data, ttl, strategy = 'memory') {
    if (strategy === 'memory' && this.options.enableMemoryCache) {
      this.setMemoryCache(key, data, ttl);
    } else if (strategy === 'storage' && this.options.enableStorageCache) {
      this.setStorageCache(key, data, ttl);
    }
  }

  /**
   * 获取内存缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据
   */
  getMemoryCache(key) {
    const cached = this.memoryCache.get(key);
    if (!cached) return null;

    if (Date.now() > cached.expireTime) {
      this.memoryCache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * 设置内存缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} ttl - 过期时间
   */
  setMemoryCache(key, data, ttl) {
    // 检查缓存大小限制
    if (this.memoryCache.size >= this.options.maxMemorySize) {
      // 删除最旧的缓存项
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }

    this.memoryCache.set(key, {
      data,
      expireTime: Date.now() + ttl,
      createTime: Date.now()
    });
  }

  /**
   * 获取本地存储缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据
   */
  getStorageCache(key) {
    try {
      const storageKey = this.storagePrefix + key;
      const cached = uni.getStorageSync(storageKey);

      if (!cached) return null;

      const parsedCache = JSON.parse(cached);
      if (Date.now() > parsedCache.expireTime) {
        uni.removeStorageSync(storageKey);
        return null;
      }

      return parsedCache.data;
    } catch (error) {
      console.error('获取本地存储缓存失败:', error);
      return null;
    }
  }

  /**
   * 设置本地存储缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} ttl - 过期时间
   */
  setStorageCache(key, data, ttl) {
    try {
      const storageKey = this.storagePrefix + key;
      const cacheData = {
        data,
        expireTime: Date.now() + ttl,
        createTime: Date.now()
      };

      uni.setStorageSync(storageKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error('设置本地存储缓存失败:', error);
    }
  }

  /**
   * 清除内存缓存
   */
  clearMemoryCache() {
    this.memoryCache.clear();
    console.log('内存缓存已清除');
  }

  /**
   * 清除本地存储缓存
   */
  clearStorageCache() {
    try {
      const keys = uni.getStorageInfoSync().keys;
      const cacheKeys = keys.filter(key => key.startsWith(this.storagePrefix));

      cacheKeys.forEach(key => {
        uni.removeStorageSync(key);
      });

      console.log('本地存储缓存已清除');
    } catch (error) {
      console.error('清除本地存储缓存失败:', error);
    }
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.clearMemoryCache();
    this.clearStorageCache();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      memorySize: this.memoryCache.size,
      maxMemorySize: this.options.maxMemorySize,
      storageKeys: this.getStorageCacheKeys().length
    };
  }

  /**
   * 获取本地存储缓存键列表
   * @returns {Array} 缓存键列表
   */
  getStorageCacheKeys() {
    try {
      const keys = uni.getStorageInfoSync().keys;
      return keys.filter(key => key.startsWith(this.storagePrefix));
    } catch (error) {
      console.error('获取本地存储缓存键失败:', error);
      return [];
    }
  }
}

export default CachePlugin;
```

---

## 🚀 3. 统一API客户端实现

### 3.1 API客户端核心类

**文件位置**: `hooks/client/ApiClient.js`

```javascript
import Request from '@/js_sdk/luch-request/luch-request/index.js';
import envConfig from '../config/EnvConfig.js';
import apiConfig from '../config/ApiConfig.js';
import requestConfigBuilder from '../config/RequestConfigBuilder.js';
import pluginManager from '../plugins/PluginManager.js';
import securityInterceptor from '../security/SecurityInterceptor.js';

/**
 * 统一API客户端
 * 整合所有网络请求功能的核心类
 */
class ApiClient {
  constructor() {
    this.http = new Request();
    this.initialized = false;
    this.requestQueue = [];
    this.isInitializing = false;
  }

  /**
   * 初始化API客户端
   * @param {Object} options - 初始化选项
   */
  async initialize(options = {}) {
    if (this.initialized) {
      console.warn('API客户端已初始化');
      return;
    }

    if (this.isInitializing) {
      console.log('API客户端正在初始化中...');
      return;
    }

    this.isInitializing = true;

    try {
      // 1. 验证配置
      const configValidation = apiConfig.validateConfig();
      if (!configValidation.valid) {
        throw new Error(`配置验证失败: ${configValidation.errors.join(', ')}`);
      }

      // 2. 设置基础配置
      this.setupBaseConfig();

      // 3. 设置拦截器
      this.setupInterceptors();

      // 4. 加载插件
      await this.loadPlugins(options.plugins || []);

      // 5. 全局挂载
      this.mountGlobally();

      this.initialized = true;
      console.log('API客户端初始化成功');

      // 处理等待队列中的请求
      this.processRequestQueue();
    } catch (error) {
      console.error('API客户端初始化失败:', error);
      throw error;
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * 设置基础配置
   */
  setupBaseConfig() {
    const config = requestConfigBuilder.buildDefaultConfig();

    this.http.setConfig(httpConfig => {
      Object.assign(httpConfig, config);
      return httpConfig;
    });
  }

  /**
   * 设置拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.http.interceptors.request.use(async config => {
      try {
        // 安全检查
        config = await securityInterceptor.beforeRequest(config);

        // 执行插件钩子
        config = await pluginManager.executeHook('before-request', config);

        // 如果有缓存响应，直接返回
        if (config._cached && config._cachedResponse) {
          return Promise.resolve({
            data: config._cachedResponse,
            statusCode: 200,
            config
          });
        }

        return config;
      } catch (error) {
        return securityInterceptor.handleError(error);
      }
    }, error => {
      return Promise.reject(error);
    });

    // 响应拦截器
    this.http.interceptors.response.use(async response => {
      try {
        // 安全处理
        response = securityInterceptor.afterResponse(response);

        // 执行插件钩子
        response = await pluginManager.executeHook('after-response', response);

        // 业务错误处理
        return this.handleBusinessResponse(response);
      } catch (error) {
        return Promise.reject(error);
      }
    }, error => {
      return this.handleResponseError(error);
    });
  }

  /**
   * 处理业务响应
   * @param {Object} response - 响应对象
   * @returns {Object} 处理后的响应
   */
  handleBusinessResponse(response) {
    const data = response.data;

    // Token失效处理
    if (data?.code === 666 || data?.code === 401 || data?.code === 403) {
      return this.handleTokenError(response);
    }

    // 业务错误处理
    if (data?.code === 500) {
      return this.handleBusinessError(response);
    }

    // 其他错误码处理
    if (data?.code && data.code !== 200 && !data.success) {
      return this.handleOtherError(response);
    }

    return response;
  }

  /**
   * 处理Token错误
   * @param {Object} response - 响应对象
   * @returns {Promise} 拒绝的Promise
   */
  handleTokenError(response) {
    // 停止UI操作
    try {
      uni.stopPullDownRefresh();
      uni.hideLoading();
    } catch (e) {
      console.error('停止UI操作失败', e);
    }

    // 清除用户信息
    const { userStore } = require('../../store/index.js');
    const store = userStore();
    store.clearUserInfo();
    store.clearToken();

    // 显示提示
    uni.showToast({
      icon: 'none',
      title: '登录已过期，请重新登录',
      duration: 1500
    });

    // 跳转登录页
    const { toLoginAndBackPage } = require('../index.js');
    toLoginAndBackPage();

    return Promise.reject(response);
  }

  /**
   * 处理业务错误
   * @param {Object} response - 响应对象
   * @returns {Object|Promise} 响应或拒绝的Promise
   */
  handleBusinessError(response) {
    const specialHandling = apiConfig.getSpecialHandling(response.config?.url || '');

    if (specialHandling?.allowError500) {
      console.log('特殊接口允许500错误:', response.config?.url);
      return response;
    }

    uni.showToast({
      icon: 'none',
      title: response.data?.message || '系统异常，请稍后再试'
    });

    return Promise.reject(response);
  }

  /**
   * 处理其他错误
   * @param {Object} response - 响应对象
   * @returns {Promise} 拒绝的Promise
   */
  handleOtherError(response) {
    uni.showToast({
      icon: 'none',
      title: response.data?.message || '操作失败'
    });

    return Promise.reject(response);
  }

  /**
   * 处理响应错误
   * @param {Object} error - 错误对象
   * @returns {Promise} 拒绝的Promise
   */
  handleResponseError(error) {
    let errorMessage = '网络异常，请稍后再试';

    if (error.statusCode) {
      switch (error.statusCode) {
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        case 502:
          errorMessage = '网关错误';
          break;
        case 503:
          errorMessage = '服务暂不可用';
          break;
        case 504:
          errorMessage = '网关超时';
          break;
        default:
          errorMessage = error.data?.message || '网络异常，请稍后再试';
      }
    }

    uni.showToast({
      icon: 'none',
      title: errorMessage
    });

    return Promise.reject(error);
  }

  /**
   * 加载插件
   * @param {Array} plugins - 插件列表
   */
  async loadPlugins(plugins) {
    for (const plugin of plugins) {
      try {
        if (typeof plugin === 'string') {
          // 动态导入插件
          const PluginClass = await import(`../plugins/${plugin}.js`);
          const pluginInstance = new PluginClass.default();
          pluginManager.register(plugin, pluginInstance);
          await pluginManager.load(plugin);
        } else if (typeof plugin === 'object') {
          pluginManager.register(plugin.name, plugin);
          await pluginManager.load(plugin.name, plugin.options);
        }
      } catch (error) {
        console.error(`加载插件失败: ${plugin}`, error);
      }
    }
  }

  /**
   * 全局挂载
   */
  mountGlobally() {
    // 挂载HTTP实例
    uni.http = this.http;

    // 挂载API配置
    uni.api = this.createApiProxy();

    // 挂载客户端实例
    uni.apiClient = this;
  }

  /**
   * 创建API代理
   * @returns {Proxy} API代理对象
   */
  createApiProxy() {
    const routes = apiConfig.getAllRoutes();
    const flatRoutes = {};

    // 扁平化API路由
    for (const [category, actions] of Object.entries(routes)) {
      for (const [action, path] of Object.entries(actions)) {
        flatRoutes[`${category}_${action}`] = path;
        flatRoutes[action] = path; // 向后兼容
      }
    }

    return new Proxy(flatRoutes, {
      get(target, prop) {
        if (prop in target) {
          return target[prop];
        }

        console.warn(`API路径不存在: ${prop}`);
        return null;
      }
    });
  }

  /**
   * 处理请求队列
   */
  processRequestQueue() {
    while (this.requestQueue.length > 0) {
      const { resolve, config } = this.requestQueue.shift();
      resolve(this.request(config));
    }
  }

  /**
   * 通用请求方法
   * @param {Object} config - 请求配置
   * @returns {Promise} 请求Promise
   */
  async request(config) {
    if (!this.initialized) {
      if (this.isInitializing) {
        // 如果正在初始化，将请求加入队列
        return new Promise((resolve) => {
          this.requestQueue.push({ resolve, config });
        });
      } else {
        throw new Error('API客户端未初始化');
      }
    }

    return this.http.request(config);
  }

  /**
   * GET请求
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} params - 查询参数
   * @param {Object} options - 其他选项
   * @returns {Promise} 请求Promise
   */
  get(category, action, params = {}, options = {}) {
    const config = requestConfigBuilder.buildGet(category, action, params, options);
    return this.request(config);
  }

  /**
   * POST请求
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} data - 请求体数据
   * @param {Object} options - 其他选项
   * @returns {Promise} 请求Promise
   */
  post(category, action, data = {}, options = {}) {
    const config = requestConfigBuilder.buildPost(category, action, data, options);
    return this.request(config);
  }

  /**
   * PUT请求
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} data - 请求体数据
   * @param {Object} options - 其他选项
   * @returns {Promise} 请求Promise
   */
  put(category, action, data = {}, options = {}) {
    const config = requestConfigBuilder.buildPut(category, action, data, options);
    return this.request(config);
  }

  /**
   * DELETE请求
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} params - 查询参数
   * @param {Object} options - 其他选项
   * @returns {Promise} 请求Promise
   */
  delete(category, action, params = {}, options = {}) {
    const config = requestConfigBuilder.buildDelete(category, action, params, options);
    return this.request(config);
  }

  /**
   * 获取客户端状态
   * @returns {Object} 客户端状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      isInitializing: this.isInitializing,
      queueLength: this.requestQueue.length,
      loadedPlugins: pluginManager.getLoadedPlugins(),
      environment: envConfig.getEnvironment()
    };
  }
}

// 创建单例实例
const apiClient = new ApiClient();

export default apiClient;
```

---

**注意**: 本架构重构方案基于heartful-mall项目的实际需求设计，采用渐进式重构策略，确保向后兼容性和系统稳定性。
