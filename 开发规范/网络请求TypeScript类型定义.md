# 🔷 网络请求TypeScript类型定义

## 📋 概述

本文档提供了heartful-mall-app C端用户小程序网络请求的TypeScript类型定义，提升代码质量和开发效率。

---

## 🏗️ 核心类型定义

### 1. 基础响应类型

**文件位置**: `types/api.d.ts`

```typescript
/**
 * jeecg-boot标准响应格式
 */
interface ApiResponse<T = any> {
  /** 请求是否成功 */
  success: boolean;
  /** 响应消息 */
  message: string;
  /** 响应代码 */
  code: number;
  /** 业务数据 */
  result: T;
  /** 响应时间戳 */
  timestamp: number;
}

/**
 * 分页响应数据
 */
interface PageResult<T = any> {
  /** 当前页码 */
  current: number;
  /** 每页大小 */
  size: number;
  /** 总记录数 */
  total: number;
  /** 总页数 */
  pages: number;
  /** 数据列表 */
  records: T[];
}

/**
 * 分页查询参数
 */
interface PageParams {
  /** 页码 */
  pageNo?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段 */
  column?: string;
  /** 排序方向 */
  order?: 'asc' | 'desc';
}

/**
 * 请求配置
 */
interface RequestConfig {
  /** 请求URL */
  url: string;
  /** 请求方法 */
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  /** 请求参数 */
  params?: Record<string, any>;
  /** 请求体数据 */
  data?: Record<string, any>;
  /** 请求头 */
  header?: Record<string, string>;
  /** 超时时间 */
  timeout?: number;
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 缓存时间（毫秒） */
  cacheTime?: number;
}

/**
 * 错误响应
 */
interface ApiError {
  /** 错误代码 */
  code: number;
  /** 错误消息 */
  message: string;
  /** 详细错误信息 */
  details?: any;
  /** 错误堆栈 */
  stack?: string;
}
```

### 2. 业务数据类型

```typescript
/**
 * 用户信息
 */
interface UserInfo {
  /** 用户ID */
  id: string;
  /** 会员ID */
  memberId?: string;
  /** 用户昵称 */
  nickname: string;
  /** 手机号 */
  phone: string;
  /** 头像URL */
  avatar?: string;
  /** 性别 */
  gender?: 0 | 1 | 2; // 0:未知 1:男 2:女
  /** 生日 */
  birthday?: string;
  /** 注册时间 */
  createTime: string;
  /** 用户状态 */
  status: 0 | 1; // 0:禁用 1:启用
  /** 会员等级 */
  memberLevel?: number;
  /** 余额 */
  balance?: number;
  /** 积分 */
  points?: number;
}

/**
 * 登录响应
 */
interface LoginResult {
  /** 访问Token */
  token: string;
  /** 刷新Token */
  refreshToken?: string;
  /** Token过期时间（秒） */
  expiresIn: number;
  /** 用户信息 */
  userInfo: UserInfo;
}

/**
 * 商品信息
 */
interface GoodInfo {
  /** 商品ID */
  id: string;
  /** 商品名称 */
  goodName: string;
  /** 商品价格 */
  price: number;
  /** 原价 */
  originalPrice?: number;
  /** 商品图片 */
  images: string[];
  /** 商品描述 */
  description?: string;
  /** 库存数量 */
  stock: number;
  /** 销量 */
  sales: number;
  /** 商品状态 */
  status: 0 | 1; // 0:下架 1:上架
  /** 店铺ID */
  storeId: string;
  /** 店铺名称 */
  storeName: string;
}

/**
 * 订单信息
 */
interface OrderInfo {
  /** 订单ID */
  id: string;
  /** 订单号 */
  orderNo: string;
  /** 订单状态 */
  status: number;
  /** 订单金额 */
  totalAmount: number;
  /** 实付金额 */
  payAmount: number;
  /** 创建时间 */
  createTime: string;
  /** 支付时间 */
  payTime?: string;
  /** 收货地址 */
  address: AddressInfo;
  /** 订单商品 */
  items: OrderItem[];
}

/**
 * 订单商品项
 */
interface OrderItem {
  /** 商品ID */
  goodId: string;
  /** 商品名称 */
  goodName: string;
  /** 商品图片 */
  image: string;
  /** 商品价格 */
  price: number;
  /** 购买数量 */
  quantity: number;
  /** 小计金额 */
  subtotal: number;
}

/**
 * 收货地址
 */
interface AddressInfo {
  /** 地址ID */
  id?: string;
  /** 收货人姓名 */
  receiverName: string;
  /** 收货人电话 */
  receiverPhone: string;
  /** 省份 */
  province: string;
  /** 城市 */
  city: string;
  /** 区县 */
  district: string;
  /** 详细地址 */
  detail: string;
  /** 是否默认地址 */
  isDefault?: boolean;
}

/**
 * 任务信息
 */
interface TaskInfo {
  /** 任务ID */
  id: string;
  /** 任务标题 */
  title: string;
  /** 任务描述 */
  description: string;
  /** 任务类型 */
  type: 'short' | 'long'; // short:短期任务 long:长期任务
  /** 任务状态 */
  status: 'pending' | 'accepted' | 'completed' | 'cancelled';
  /** 任务奖励 */
  reward: number;
  /** 截止时间 */
  deadline: string;
  /** 完成天数（长期任务） */
  daysToComplete?: number;
  /** 发布者ID */
  publisherId: string;
  /** 发布者昵称 */
  publisherName: string;
  /** 创建时间 */
  createTime: string;
}
```

### 3. API接口类型

```typescript
/**
 * 用户认证相关API
 */
interface AuthApi {
  /** 微信登录 */
  loginByCode(params: {
    code: string;
    encryptedData?: string;
    iv?: string;
  }): Promise<ApiResponse<LoginResult>>;

  /** 刷新Token */
  refreshToken(params: {
    refreshToken: string;
  }): Promise<ApiResponse<LoginResult>>;

  /** 获取用户信息 */
  getMemberInfo(): Promise<ApiResponse<UserInfo>>;

  /** 用户登出 */
  logout(): Promise<ApiResponse<null>>;
}

/**
 * 商品相关API
 */
interface GoodApi {
  /** 搜索商品列表 */
  searchHostStoreGoodList(params: {
    keyword?: string;
    categoryId?: string;
    storeId?: string;
  } & PageParams): Promise<ApiResponse<PageResult<GoodInfo>>>;

  /** 根据商品ID获取详情 */
  findGoodListByGoodId(params: {
    goodId: string;
  }): Promise<ApiResponse<GoodInfo>>;
}

/**
 * 订单相关API
 */
interface OrderApi {
  /** 立即下单 */
  promptlyAffirmOrder(params: {
    goodId: string;
    skuId?: string;
    quantity: number;
    addressId: string;
  }): Promise<ApiResponse<OrderInfo>>;

  /** 提交订单 */
  submitOrder(params: {
    orderData: any;
  }): Promise<ApiResponse<{ orderId: string }>>;

  /** 订单列表 */
  orderList(params: {
    status?: number;
  } & PageParams): Promise<ApiResponse<PageResult<OrderInfo>>>;
}

/**
 * 任务相关API
 */
interface TaskApi {
  /** 我发布的任务 */
  getMyPublishedTasks(params?: PageParams): Promise<ApiResponse<PageResult<TaskInfo>>>;

  /** 我接受的任务 */
  getMyAcceptedTasks(params?: PageParams): Promise<ApiResponse<PageResult<TaskInfo>>>;

  /** 待审核任务 */
  getPendingAuditTasks(params?: PageParams): Promise<ApiResponse<PageResult<TaskInfo>>>;

  /** 取消任务接受记录 */
  cancelAcceptanceRecord(params: {
    recordId: string;
  }): Promise<ApiResponse<null>>;
}
```

### 4. 安全相关类型

```typescript
/**
 * Token管理器类型
 */
interface ITokenManager {
  /** 设置Token */
  setToken(token: string, refreshToken?: string, expiresIn?: number): void;
  
  /** 获取Token */
  getToken(): string | null;
  
  /** 获取刷新Token */
  getRefreshToken(): string | null;
  
  /** 检查Token是否即将过期 */
  isTokenExpiringSoon(): boolean;
  
  /** 刷新Token */
  refreshToken(): Promise<string>;
  
  /** 清除Token */
  clearToken(): void;
  
  /** 验证Token有效性 */
  isTokenValid(): boolean;
}

/**
 * 请求签名器类型
 */
interface IRequestSigner {
  /** 生成签名 */
  generateSign(url: string, params?: Record<string, any>, timestamp?: number): string;
  
  /** 为请求添加签名头 */
  addSignHeaders(config: RequestConfig): RequestConfig;
  
  /** 验证签名 */
  verifySign(url: string, params: Record<string, any>, sign: string, timestamp: number): boolean;
}

/**
 * 频率限制器类型
 */
interface IRateLimiter {
  /** 检查请求频率 */
  checkRate(api: string, userId?: string): boolean;
  
  /** 获取剩余请求次数 */
  getRemainingRequests(api: string, userId?: string): number;
  
  /** 重置用户频率限制 */
  resetUserLimits(userId: string): void;
  
  /** 更新频率限制配置 */
  updateRateLimit(apiPrefix: string, config: { limit: number; window: number }): void;
}

/**
 * 安全拦截器类型
 */
interface ISecurityInterceptor {
  /** 请求前安全检查 */
  beforeRequest(config: RequestConfig): Promise<RequestConfig>;
  
  /** 响应后安全处理 */
  afterResponse(response: any): any;
  
  /** 错误处理 */
  handleError(error: ApiError): Promise<never>;
}
```

### 5. 配置类型

```typescript
/**
 * API配置
 */
interface ApiConfig {
  /** 基础URL */
  baseURL: string;
  /** 超时时间 */
  timeout: number;
  /** 默认请求头 */
  headers: Record<string, string>;
  /** 是否启用缓存 */
  enableCache: boolean;
  /** 是否启用签名 */
  enableSigning: boolean;
  /** 是否启用频率限制 */
  enableRateLimit: boolean;
}

/**
 * 环境配置
 */
interface EnvConfig {
  /** 开发环境API地址 */
  dev: string;
  /** 测试环境API地址 */
  test: string;
  /** 生产环境API地址 */
  prod: string;
}

/**
 * 缓存配置
 */
interface CacheConfig {
  /** API路径 */
  [apiPath: string]: {
    /** 缓存时间（毫秒） */
    ttl: number;
    /** 是否启用 */
    enabled?: boolean;
  };
}

/**
 * 频率限制配置
 */
interface RateLimitConfig {
  /** API前缀 */
  [apiPrefix: string]: {
    /** 限制次数 */
    limit: number;
    /** 时间窗口（毫秒） */
    window: number;
  };
}
```

### 6. 工具类型

```typescript
/**
 * 请求方法类型
 */
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * 响应状态类型
 */
type ResponseStatus = 'success' | 'error' | 'loading';

/**
 * 平台类型
 */
type PlatformType = 'mp-weixin' | 'h5' | 'app-plus' | 'mp-alipay';

/**
 * 软件模型类型
 */
type SoftModel = 0 | 1 | 2 | 3; // 0:小程序 1:Android 2:iOS 3:H5

/**
 * 任务状态类型
 */
type TaskStatus = 'pending' | 'accepted' | 'completed' | 'cancelled' | 'expired';

/**
 * 订单状态类型
 */
type OrderStatus = 0 | 1 | 2 | 3 | 4 | 5; // 0:待付款 1:待发货 2:待收货 3:已完成 4:已取消 5:已退款

/**
 * 性别类型
 */
type Gender = 0 | 1 | 2; // 0:未知 1:男 2:女

/**
 * 用户状态类型
 */
type UserStatus = 0 | 1; // 0:禁用 1:启用

/**
 * 商品状态类型
 */
type GoodStatus = 0 | 1; // 0:下架 1:上架
```

---

## 📝 使用示例

### 在Vue组件中使用类型

```typescript
// pages/goods/detail.vue
<script setup lang="ts">
import { ref, onMounted } from 'vue';

const goodInfo = ref<GoodInfo | null>(null);
const loading = ref<boolean>(false);

const loadGoodDetail = async (goodId: string): Promise<void> => {
  try {
    loading.value = true;
    const response: ApiResponse<GoodInfo> = await uni.http.get(
      uni.api.findGoodListByGoodId,
      { params: { goodId } }
    );
    
    if (response.success) {
      goodInfo.value = response.result;
    } else {
      uni.showToast({
        icon: 'none',
        title: response.message
      });
    }
  } catch (error) {
    console.error('加载商品详情失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  const goodId = getCurrentPages().pop()?.options?.goodId;
  if (goodId) {
    loadGoodDetail(goodId);
  }
});
</script>
```

### API调用类型安全

```typescript
// utils/api.ts
class ApiClient {
  async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await uni.http.get(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    const response = await uni.http.post(url, data, config);
    return response.data;
  }
}

const apiClient = new ApiClient();

// 类型安全的API调用
const userInfo: ApiResponse<UserInfo> = await apiClient.get('/after/member/getMemberInfo');
const orderList: ApiResponse<PageResult<OrderInfo>> = await apiClient.get('/after/order/orderList');
```

---

**注意**: 这些类型定义基于heartful-mall项目的实际API结构设计，可以根据后端接口的具体实现进行调整和完善。
