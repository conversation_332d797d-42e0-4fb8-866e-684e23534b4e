# 🔒 网络请求安全增强实现方案

## 📋 概述

本文档提供了heartful-mall-app C端用户小程序网络请求安全增强的具体实现方案，包括Token管理、请求签名、频率限制等安全机制的代码实现。

---

## 🛡️ 1. Token安全管理实现

### 1.1 Token管理器实现

**文件位置**: `hooks/security/TokenManager.js`

```javascript
/**
 * Token安全管理器
 * 提供Token的安全存储、获取、刷新和清除功能
 */
class TokenManager {
  constructor() {
    this.tokenKey = 'auth_token';
    this.refreshTokenKey = 'refresh_token';
    this.expireKey = 'token_expire';
    this.isRefreshing = false;
    this.failedQueue = [];
  }

  /**
   * 存储Token
   * @param {string} token - 访问Token
   * @param {string} refreshToken - 刷新Token
   * @param {number} expiresIn - 过期时间（秒）
   */
  setToken(token, refreshToken = null, expiresIn = 7 * 24 * 60 * 60) {
    try {
      uni.setStorageSync(this.tokenKey, token);
      
      if (refreshToken) {
        uni.setStorageSync(this.refreshTokenKey, refreshToken);
      }
      
      // 设置过期时间（当前时间 + 过期秒数）
      const expireTime = Date.now() + (expiresIn * 1000);
      uni.setStorageSync(this.expireKey, expireTime);
      
      console.log('Token存储成功');
    } catch (error) {
      console.error('Token存储失败:', error);
      throw new Error('Token存储失败');
    }
  }

  /**
   * 获取Token
   * @returns {string|null} Token或null
   */
  getToken() {
    try {
      const token = uni.getStorageSync(this.tokenKey);
      const expireTime = uni.getStorageSync(this.expireKey);
      
      // 检查Token是否存在
      if (!token) {
        return null;
      }
      
      // 检查Token是否过期
      if (expireTime && Date.now() > expireTime) {
        console.log('Token已过期，自动清除');
        this.clearToken();
        return null;
      }
      
      return token;
    } catch (error) {
      console.error('Token获取失败:', error);
      return null;
    }
  }

  /**
   * 获取刷新Token
   * @returns {string|null} 刷新Token或null
   */
  getRefreshToken() {
    try {
      return uni.getStorageSync(this.refreshTokenKey);
    } catch (error) {
      console.error('刷新Token获取失败:', error);
      return null;
    }
  }

  /**
   * 检查Token是否即将过期（30分钟内）
   * @returns {boolean} 是否即将过期
   */
  isTokenExpiringSoon() {
    try {
      const expireTime = uni.getStorageSync(this.expireKey);
      if (!expireTime) return false;
      
      const thirtyMinutes = 30 * 60 * 1000;
      return (expireTime - Date.now()) < thirtyMinutes;
    } catch (error) {
      console.error('Token过期检查失败:', error);
      return false;
    }
  }

  /**
   * 刷新Token
   * @returns {Promise<string>} 新的Token
   */
  async refreshToken() {
    // 防止并发刷新
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      });
    }

    this.isRefreshing = true;

    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('刷新Token不存在');
      }

      // 调用刷新Token接口
      const response = await uni.http.post('/front/weixin/refreshToken', {
        refreshToken
      });

      if (response.data?.success) {
        const { token, refreshToken: newRefreshToken, expiresIn } = response.data.result;
        
        // 存储新Token
        this.setToken(token, newRefreshToken, expiresIn);
        
        // 处理等待队列
        this.processQueue(null, token);
        
        console.log('Token刷新成功');
        return token;
      } else {
        throw new Error(response.data?.message || 'Token刷新失败');
      }
    } catch (error) {
      console.error('Token刷新失败:', error);
      
      // 刷新失败，清除所有Token信息
      this.clearToken();
      this.processQueue(error, null);
      
      // 跳转到登录页
      const { toLoginAndBackPage } = require('../index.js');
      toLoginAndBackPage();
      
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * 处理等待队列
   * @param {Error|null} error - 错误信息
   * @param {string|null} token - 新Token
   */
  processQueue(error, token = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }

  /**
   * 清除Token
   */
  clearToken() {
    try {
      uni.removeStorageSync(this.tokenKey);
      uni.removeStorageSync(this.refreshTokenKey);
      uni.removeStorageSync(this.expireKey);
      console.log('Token清除成功');
    } catch (error) {
      console.error('Token清除失败:', error);
    }
  }

  /**
   * 验证Token有效性
   * @returns {boolean} Token是否有效
   */
  isTokenValid() {
    const token = this.getToken();
    return !!token;
  }
}

// 创建单例实例
const tokenManager = new TokenManager();

export default tokenManager;
```

### 1.2 请求签名验证实现

**文件位置**: `hooks/security/RequestSigner.js`

```javascript
import CryptoJS from 'crypto-js';

/**
 * 请求签名验证器
 * 提供请求签名生成和验证功能
 */
class RequestSigner {
  constructor() {
    this.secretKey = 'heartful_mall_secret_2025'; // 应该从配置文件获取
    this.signHeader = 'X-Sign';
    this.timestampHeader = 'X-Timestamp';
  }

  /**
   * 生成请求签名
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {number} timestamp - 时间戳
   * @returns {string} 签名字符串
   */
  generateSign(url, params = {}, timestamp = null) {
    try {
      const ts = timestamp || Date.now();
      
      // 1. 参数排序
      const sortedParams = this.sortParams(params);
      
      // 2. 构建签名字符串
      const signString = this.buildSignString(url, sortedParams, ts);
      
      // 3. 生成MD5签名
      const sign = CryptoJS.MD5(signString).toString();
      
      console.log('签名生成:', { url, params, timestamp: ts, sign });
      return sign;
    } catch (error) {
      console.error('签名生成失败:', error);
      throw new Error('签名生成失败');
    }
  }

  /**
   * 参数排序
   * @param {Object} params - 参数对象
   * @returns {string} 排序后的参数字符串
   */
  sortParams(params) {
    if (!params || typeof params !== 'object') {
      return '';
    }

    return Object.keys(params)
      .filter(key => params[key] !== null && params[key] !== undefined)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
  }

  /**
   * 构建签名字符串
   * @param {string} url - 请求URL
   * @param {string} sortedParams - 排序后的参数
   * @param {number} timestamp - 时间戳
   * @returns {string} 签名字符串
   */
  buildSignString(url, sortedParams, timestamp) {
    const parts = [url];
    
    if (sortedParams) {
      parts.push(sortedParams);
    }
    
    parts.push(`timestamp=${timestamp}`);
    parts.push(`secret=${this.secretKey}`);
    
    return parts.join('&');
  }

  /**
   * 为请求添加签名头
   * @param {Object} config - 请求配置
   * @returns {Object} 添加签名后的配置
   */
  addSignHeaders(config) {
    try {
      const timestamp = Date.now();
      const url = config.url;
      const params = config.method === 'get' ? config.params : config.data;
      
      const sign = this.generateSign(url, params, timestamp);
      
      config.header = {
        ...config.header,
        [this.signHeader]: sign,
        [this.timestampHeader]: timestamp
      };
      
      return config;
    } catch (error) {
      console.error('添加签名头失败:', error);
      return config;
    }
  }

  /**
   * 验证签名是否有效
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {string} sign - 签名
   * @param {number} timestamp - 时间戳
   * @returns {boolean} 签名是否有效
   */
  verifySign(url, params, sign, timestamp) {
    try {
      // 检查时间戳是否在有效期内（5分钟）
      const now = Date.now();
      const maxAge = 5 * 60 * 1000; // 5分钟
      
      if (Math.abs(now - timestamp) > maxAge) {
        console.warn('签名时间戳过期');
        return false;
      }
      
      // 重新生成签名进行比较
      const expectedSign = this.generateSign(url, params, timestamp);
      return sign === expectedSign;
    } catch (error) {
      console.error('签名验证失败:', error);
      return false;
    }
  }
}

// 创建单例实例
const requestSigner = new RequestSigner();

export default requestSigner;
```

---

## 📊 2. API调用频率限制实现

### 2.1 频率限制器实现

**文件位置**: `hooks/security/RateLimiter.js`

```javascript
/**
 * API调用频率限制器
 * 基于滑动窗口算法实现请求频率控制
 */
class RateLimiter {
  constructor() {
    this.requests = new Map(); // 存储请求记录
    this.defaultLimits = {
      // 默认频率限制配置
      '/after/': { limit: 100, window: 60000 }, // 登录接口：100次/分钟
      '/front/': { limit: 200, window: 60000 }, // 公开接口：200次/分钟
      '/after/order/': { limit: 10, window: 60000 }, // 订单接口：10次/分钟
      '/after/payment/': { limit: 5, window: 60000 }  // 支付接口：5次/分钟
    };
  }

  /**
   * 检查请求频率
   * @param {string} api - API路径
   * @param {string} userId - 用户ID（可选）
   * @returns {boolean} 是否允许请求
   */
  checkRate(api, userId = 'anonymous') {
    try {
      const config = this.getRateLimitConfig(api);
      const key = this.generateKey(api, userId);
      const now = Date.now();
      
      // 获取当前窗口的请求记录
      const windowStart = Math.floor(now / config.window) * config.window;
      const windowKey = `${key}_${windowStart}`;
      
      const currentCount = this.requests.get(windowKey) || 0;
      
      // 检查是否超过限制
      if (currentCount >= config.limit) {
        console.warn(`API调用频率超限: ${api}, 当前: ${currentCount}/${config.limit}`);
        return false;
      }
      
      // 记录本次请求
      this.requests.set(windowKey, currentCount + 1);
      
      // 清理过期记录
      this.cleanup(now, config.window);
      
      return true;
    } catch (error) {
      console.error('频率检查失败:', error);
      return true; // 出错时允许请求
    }
  }

  /**
   * 获取API的频率限制配置
   * @param {string} api - API路径
   * @returns {Object} 频率限制配置
   */
  getRateLimitConfig(api) {
    // 按最具体的匹配规则查找配置
    const sortedKeys = Object.keys(this.defaultLimits)
      .sort((a, b) => b.length - a.length);
    
    for (const prefix of sortedKeys) {
      if (api.startsWith(prefix)) {
        return this.defaultLimits[prefix];
      }
    }
    
    // 默认配置
    return { limit: 60, window: 60000 };
  }

  /**
   * 生成缓存键
   * @param {string} api - API路径
   * @param {string} userId - 用户ID
   * @returns {string} 缓存键
   */
  generateKey(api, userId) {
    return `rate_limit_${userId}_${api}`;
  }

  /**
   * 清理过期记录
   * @param {number} now - 当前时间戳
   * @param {number} window - 窗口大小
   */
  cleanup(now, window) {
    const expireTime = now - window * 2; // 保留两个窗口的数据
    
    for (const [key, value] of this.requests.entries()) {
      const parts = key.split('_');
      const timestamp = parseInt(parts[parts.length - 1]);
      
      if (timestamp < expireTime) {
        this.requests.delete(key);
      }
    }
  }

  /**
   * 获取剩余请求次数
   * @param {string} api - API路径
   * @param {string} userId - 用户ID
   * @returns {number} 剩余请求次数
   */
  getRemainingRequests(api, userId = 'anonymous') {
    try {
      const config = this.getRateLimitConfig(api);
      const key = this.generateKey(api, userId);
      const now = Date.now();
      const windowStart = Math.floor(now / config.window) * config.window;
      const windowKey = `${key}_${windowStart}`;
      
      const currentCount = this.requests.get(windowKey) || 0;
      return Math.max(0, config.limit - currentCount);
    } catch (error) {
      console.error('获取剩余请求次数失败:', error);
      return 0;
    }
  }

  /**
   * 重置用户的频率限制
   * @param {string} userId - 用户ID
   */
  resetUserLimits(userId) {
    try {
      const keysToDelete = [];
      
      for (const key of this.requests.keys()) {
        if (key.includes(`_${userId}_`)) {
          keysToDelete.push(key);
        }
      }
      
      keysToDelete.forEach(key => this.requests.delete(key));
      console.log(`用户 ${userId} 的频率限制已重置`);
    } catch (error) {
      console.error('重置用户频率限制失败:', error);
    }
  }

  /**
   * 动态更新频率限制配置
   * @param {string} apiPrefix - API前缀
   * @param {Object} config - 新的配置
   */
  updateRateLimit(apiPrefix, config) {
    try {
      this.defaultLimits[apiPrefix] = {
        limit: config.limit || 60,
        window: config.window || 60000
      };
      console.log(`频率限制配置已更新: ${apiPrefix}`, config);
    } catch (error) {
      console.error('更新频率限制配置失败:', error);
    }
  }
}

// 创建单例实例
const rateLimiter = new RateLimiter();

export default rateLimiter;
```

---

## 🔧 3. 安全增强集成方案

### 3.1 请求拦截器安全增强

**文件位置**: `hooks/security/SecurityInterceptor.js`

```javascript
import tokenManager from './TokenManager.js';
import requestSigner from './RequestSigner.js';
import rateLimiter from './RateLimiter.js';

/**
 * 安全拦截器
 * 集成Token管理、请求签名、频率限制等安全功能
 */
class SecurityInterceptor {
  constructor() {
    this.enableSigning = true; // 是否启用请求签名
    this.enableRateLimit = true; // 是否启用频率限制
    this.enableTokenRefresh = true; // 是否启用Token自动刷新
  }

  /**
   * 请求前安全检查
   * @param {Object} config - 请求配置
   * @returns {Promise<Object>} 处理后的配置
   */
  async beforeRequest(config) {
    try {
      // 1. 频率限制检查
      if (this.enableRateLimit) {
        const userId = this.getCurrentUserId();
        if (!rateLimiter.checkRate(config.url, userId)) {
          throw new Error('请求过于频繁，请稍后再试');
        }
      }

      // 2. Token处理
      await this.handleToken(config);

      // 3. 请求签名
      if (this.enableSigning) {
        requestSigner.addSignHeaders(config);
      }

      // 4. 添加安全头
      this.addSecurityHeaders(config);

      return config;
    } catch (error) {
      console.error('请求前安全检查失败:', error);
      throw error;
    }
  }

  /**
   * 处理Token相关逻辑
   * @param {Object} config - 请求配置
   */
  async handleToken(config) {
    // 检查是否需要Token
    if (!this.requiresAuth(config.url)) {
      return;
    }

    // 获取当前Token
    let token = tokenManager.getToken();

    // 如果Token即将过期，尝试刷新
    if (this.enableTokenRefresh && token && tokenManager.isTokenExpiringSoon()) {
      try {
        console.log('Token即将过期，自动刷新');
        token = await tokenManager.refreshToken();
      } catch (error) {
        console.warn('Token自动刷新失败:', error);
        // 刷新失败时清除Token，让用户重新登录
        tokenManager.clearToken();
        token = null;
      }
    }

    // 添加Token到请求头
    if (token) {
      config.header = {
        ...config.header,
        'X-AUTH-TOKEN': token
      };
    } else if (this.requiresAuth(config.url)) {
      throw new Error('用户未登录或登录已过期');
    }
  }

  /**
   * 添加安全请求头
   * @param {Object} config - 请求配置
   */
  addSecurityHeaders(config) {
    config.header = {
      ...config.header,
      'X-Requested-With': 'XMLHttpRequest',
      'X-Client-Version': uni.getSystemInfoSync().version || '1.0.0',
      'X-Request-ID': this.generateRequestId()
    };
  }

  /**
   * 检查API是否需要认证
   * @param {string} url - API路径
   * @returns {boolean} 是否需要认证
   */
  requiresAuth(url) {
    const authRequiredPrefixes = ['/after/', '/back/'];
    return authRequiredPrefixes.some(prefix => url.startsWith(prefix));
  }

  /**
   * 获取当前用户ID
   * @returns {string} 用户ID
   */
  getCurrentUserId() {
    try {
      const { userStore } = require('../../store/index.js');
      const store = userStore();
      return store.userInfo?.id || 'anonymous';
    } catch (error) {
      return 'anonymous';
    }
  }

  /**
   * 生成请求ID
   * @returns {string} 请求ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 响应后安全处理
   * @param {Object} response - 响应对象
   * @returns {Object} 处理后的响应
   */
  afterResponse(response) {
    try {
      // 记录响应日志（生产环境可关闭）
      if (process.env.NODE_ENV === 'development') {
        console.log('API响应:', {
          url: response.config?.url,
          status: response.statusCode,
          data: response.data
        });
      }

      return response;
    } catch (error) {
      console.error('响应后处理失败:', error);
      return response;
    }
  }

  /**
   * 错误处理
   * @param {Error} error - 错误对象
   * @returns {Promise<never>} 拒绝的Promise
   */
  handleError(error) {
    console.error('请求安全错误:', error);

    // 根据错误类型进行不同处理
    if (error.message?.includes('频繁')) {
      uni.showToast({
        icon: 'none',
        title: '操作过于频繁，请稍后再试'
      });
    } else if (error.message?.includes('登录')) {
      uni.showToast({
        icon: 'none',
        title: '请先登录'
      });
      
      // 跳转到登录页
      const { toLoginAndBackPage } = require('../index.js');
      toLoginAndBackPage();
    }

    return Promise.reject(error);
  }
}

// 创建单例实例
const securityInterceptor = new SecurityInterceptor();

export default securityInterceptor;
```

---

## 📝 使用说明

### 集成到现有请求拦截器

在 `hooks/request.js` 中集成安全增强功能：

```javascript
import securityInterceptor from './security/SecurityInterceptor.js';

// 请求前拦截器
http.interceptors.request.use(async config => {
  try {
    // 应用安全增强
    config = await securityInterceptor.beforeRequest(config);
    
    // 原有的业务逻辑...
    
    return config;
  } catch (error) {
    return securityInterceptor.handleError(error);
  }
});

// 响应拦截器
http.interceptors.response.use(response => {
  // 安全后处理
  response = securityInterceptor.afterResponse(response);
  
  // 原有的响应处理逻辑...
  
  return response;
});
```

---

**注意**: 本实现方案严格遵循heartful-mall项目的技术栈和安全要求，所有代码均考虑了向后兼容性和实际部署环境的需求。
