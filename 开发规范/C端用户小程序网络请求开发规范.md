# 🌐 C端用户小程序网络请求开发规范

## 📋 概述

本文档定义了heartful-mall-app C端用户小程序的网络请求开发规范，确保API调用的一致性、可靠性和性能优化。所有网络请求相关开发均应严格遵循此规范。

---

## 🏗️ 技术架构

### 核心请求库
项目统一使用 **`luch-request`** 作为基础HTTP请求库，这是一个专为uni-app设计的、类似axios的请求库。

### 文件结构
```
heartful-mall-app/
├── hooks/
│   ├── request.js         # 主要请求封装、拦截器配置
│   ├── api.js             # API接口路径统一定义
│   └── index.js           # 工具函数（登录跳转等）
├── js_sdk/
│   └── luch-request/      # luch-request库源码
└── store/                 # Pinia状态管理
    ├── user.js           # 用户状态
    ├── location.js       # 位置信息
    └── index.js          # 状态导出
```

---

## ⚙️ 基础配置

### 1. 请求实例配置

**文件位置**: `hooks/request.js`

```javascript
import Request from '@/js_sdk/luch-request/luch-request/index.js'

const http = new Request();

// 基础配置
http.setConfig(config => {
  config.baseURL = uni.env.REQUEST_URL  // 从环境配置获取
  config.header = {
    'content-type': 'application/x-www-form-urlencoded'  // 符合jeecg-boot框架要求
  }
  config.timeout = 30000  // 30秒超时，优化用户体验
  config.dataType = 'json'
  config.responseType = 'text'
  return config
})

// 全局挂载
uni.http = http
```

**配置说明：**
- **Content-Type**: 使用`application/x-www-form-urlencoded`符合jeecg-boot框架要求
- **超时时间**: 调整为30秒，平衡用户体验和网络稳定性
- **baseURL**: 通过环境变量动态配置，支持多环境部署

### 2. 环境配置

API基础URL通过 `uni.env.REQUEST_URL` 动态获取：
- **开发环境**: `https://dev-api.heartful-mall.com`
- **生产环境**: `https://www.zehuaiit.top/heartful-mall-api/`

---

## 🔧 请求拦截器配置

### 请求前拦截器

```javascript
http.interceptors.request.use(config => {
  config.header = {
    // 平台标识（条件编译）
    // #ifdef MP-WEIXIN
    'softModel': 0,  // 微信小程序
    // #endif
    // #ifdef H5
    'softModel': 3,  // H5
    // #endif
    
    // 业务参数（从Pinia状态获取）
    sysUserId: getAllBeSharedStore.info?.sysUserId || setSaleChannelStoreInfoStore.sysUserId,
    tMemberId: getAllBeSharedStore.info?.tMemberId || '',
    longitude: locationStore.info.longitude,
    latitude: locationStore.info.latitude,
    shareDiscountRatio: getAllBeSharedStore.shareDiscountRatio || '',
    
    // 认证Token
    "X-AUTH-TOKEN": uni.getStorageSync('token') || '',
    
    // 合并原有header
    ...config.header,
  }
  return config
}, config => {
  return Promise.reject(config)
})
```

### 响应拦截器

```javascript
http.interceptors.response.use(response => {
  // Token失效处理 - 支持多种失效码
  if (response.data?.code == 666 || response.data?.code == 401 || response.data?.code == 403) {
    // 先停止所有可能正在进行的UI操作
    try {
      uni.stopPullDownRefresh();
      uni.hideLoading();
    } catch (e) {
      console.error('停止UI操作失败', e);
    }

    store.clearUserInfo();
    store.clearToken();

    uni.showToast({
      icon: 'none',
      title: '登录已过期，请重新登录',
      duration: 1500
    });

    toLoginAndBackPage();
    return Promise.reject(response);
  }

  // 业务错误处理
  else if (response.data?.code == 500) {
    // 特殊接口白名单处理
    const specialApiList = [
      'orderStorePickUp/verification',  // 扫码核销
      'payment/callback'                // 支付回调
    ];

    const isSpecialApi = specialApiList.some(api =>
      response.config?.url?.includes(api)
    );

    if (isSpecialApi) {
      console.log('特殊接口返回code=500，不拦截为错误:', response.data);
      return response;
    }

    uni.showToast({
      icon: 'none',
      title: response.data?.message || '系统异常，请稍后再试'
    });
    return Promise.reject(response);
  }

  // 其他业务错误码处理
  else if (response.data?.code && response.data.code !== 200 && !response.data.success) {
    uni.showToast({
      icon: 'none',
      title: response.data?.message || '操作失败'
    });
    return Promise.reject(response);
  }

  // 成功响应
  else {
    return response;
  }
}, response => {
  // 网络错误处理
  let errorMessage = '网络异常，请稍后再试';

  if (response.statusCode) {
    switch (response.statusCode) {
      case 404:
        errorMessage = '请求的资源不存在';
        break;
      case 500:
        errorMessage = '服务器内部错误';
        break;
      case 502:
        errorMessage = '网关错误';
        break;
      case 503:
        errorMessage = '服务暂不可用';
        break;
      case 504:
        errorMessage = '网关超时';
        break;
      default:
        errorMessage = response.data?.message || '网络异常，请稍后再试';
    }
  }

  uni.showToast({
    icon: 'none',
    title: errorMessage
  });

  return Promise.reject(response);
})
```

**响应拦截器优化说明：**
- **完善错误码处理**: 支持666、401、403等多种Token失效码
- **特殊接口白名单**: 可配置的特殊接口处理机制
- **详细错误分类**: 根据HTTP状态码提供具体错误提示
- **UI操作保护**: 自动停止加载动画，避免界面卡死

---

## 📡 API接口管理

### 1. 接口定义规范

**文件位置**: `hooks/api.js`

```javascript
// API接口配置对象
const api = {
  // ==================== 用户认证相关 ====================
  loginByCode: '/front/weixin/loginByCode',        // 微信登录
  refreshToken: '/front/weixin/refreshToken',      // 刷新Token
  getMemberInfo: '/after/member/getMemberInfo',    // 获取用户信息
  logout: '/after/member/logout',                  // 用户登出

  // ==================== 商城首页相关 ====================
  indexNew: '/front/index/indexNew',               // 首页数据
  findarketingAdvertisingList: '/front/MarketingAdvertising/findarketingAdvertisingList',

  // ==================== 商品相关 ====================
  searchHostStoreGoodList: '/front/goodList/searchHostStoreGoodList',
  findGoodListByGoodId: '/front/goodList/findGoodListByGoodId',

  // ==================== 订单相关 ====================
  promptlyAffirmOrder: '/after/order/promptlyAffirmOrder',
  submitOrder: '/after/order/submitOrder',
  orderList: '/after/order/orderList',

  // ==================== 班级相关 ====================
  getMyClassInfo: '/after/eduClassMember/myClassInfo',
  getClassRanking: '/after/eduClassMember/ranking',

  // ==================== 购物车相关 ====================
  addGoodToShoppingCart: '/after/memberShoppingCart/addGoodToShoppingCart',
  findCarGoods: '/after/memberShoppingCart/findCarGoods',

  // ==================== 任务相关 ====================
  getMyPublishedTasks: '/after/task/my-published',    // 我发布的任务列表
  getMyAcceptedTasks: '/after/task/my-accepted',      // 我接受的任务列表
  getPendingAuditTasks: '/after/task/pending-audit',  // 待审核任务列表
  cancelAcceptanceRecord: '/after/taskAcceptanceRecord/cancel', // 取消任务接受记录
};

// API接口元数据配置
const apiMetadata = {
  // 需要登录的接口前缀
  authRequiredPrefixes: ['/after/', '/back/'],

  // 公开接口前缀
  publicPrefixes: ['/front/', '/before/'],

  // 特殊处理接口列表
  specialHandling: {
    'orderStorePickUp/verification': { allowError500: true },
    'payment/callback': { allowError500: true }
  },

  // 接口缓存配置
  cacheConfig: {
    '/front/index/indexNew': { ttl: 5 * 60 * 1000 }, // 5分钟缓存
    '/after/member/getMemberInfo': { ttl: 10 * 60 * 1000 } // 10分钟缓存
  }
};

// 全局挂载
uni.api = api;
uni.apiMetadata = apiMetadata;
```

### 2. API接口版本管理

```javascript
// API版本管理
const ApiVersionManager = {
  currentVersion: 'v1',

  // 获取带版本的API路径
  getVersionedApi(apiPath, version = null) {
    const targetVersion = version || this.currentVersion;

    // 如果已经包含版本信息，直接返回
    if (apiPath.includes('/v')) {
      return apiPath;
    }

    // 添加版本前缀
    const segments = apiPath.split('/');
    segments.splice(2, 0, targetVersion);
    return segments.join('/');
  },

  // 检查API兼容性
  checkCompatibility(apiPath, clientVersion) {
    // 实现版本兼容性检查逻辑
    return true;
  }
};
```

### 2. 接口路径规范

#### 路径前缀规范
- **`/front/`**: 公开接口，无需登录
- **`/after/`**: 需要登录的接口
- **`/before/`**: 商家端登录前接口
- **`/back/`**: 商家端登录后接口

#### 命名规范
- 使用驼峰命名法
- 接口名称应清晰表达功能
- 按功能模块分组管理

---

## 🚀 请求调用规范

### 1. 基本调用方式

**全局实例**: 所有HTTP请求必须通过 `uni.http` 实例发起
**推荐语法**: 优先使用 `async/await` 语法

### 2. GET请求示例

```javascript
// 无参数GET请求
async function getUserInfo() {
  try {
    const { data } = await uni.http.get(uni.api.getMemberInfo);
    return data.result;
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
}

// 带参数GET请求（正确方式）
async function getClassRanking(classId, sortField) {
  try {
    const params = {
      classId,
      sortField,
      pageNo: 1,
      pageSize: 100
    };
    
    // 注意：参数通过 { params } 对象传递
    const { data } = await uni.http.get(uni.api.getClassRanking, { params });
    return data.result;
  } catch (error) {
    console.error('获取排行榜失败:', error);
  }
}
```

### 3. POST请求示例

```javascript
// 登录请求
async function wxLogin(loginData) {
  try {
    uni.showLoading({ title: '登录中...', mask: true });
    
    const { data } = await uni.http.post(uni.api.loginByCode, {
      ...loginData,
      code: code.value
    });
    
    uni.hideLoading();
    return data;
  } catch (error) {
    uni.hideLoading();
    console.error('登录失败:', error);
  }
}

// 添加商品到购物车
async function addToCart(goodId, skuId, buyNum = 1) {
  try {
    const { data } = await uni.http.post(uni.api.addGoodToShoppingCart, {
      goodId,
      skuId,
      buyNum
    });
    
    uni.showToast({
      title: '添加成功',
      icon: 'none'
    });
    
    return data.result;
  } catch (error) {
    console.error('添加购物车失败:', error);
  }
}
```

---

## ⚠️ 错误处理规范

### 1. 统一错误提示

```javascript
// 推荐的错误提示方式
uni.showToast({
  icon: 'none',
  title: '错误信息',
  duration: 2000
});
```

### 2. 错误处理最佳实践

```javascript
async function apiCall() {
  try {
    const { data } = await uni.http.get(uni.api.someApi);
    
    // 业务逻辑处理
    if (data.success) {
      return data.result;
    } else {
      // 业务错误处理
      uni.showToast({
        icon: 'none',
        title: data.message || '操作失败'
      });
    }
  } catch (error) {
    // 网络错误已在拦截器中处理
    console.error('API调用失败:', error);
  }
}
```

---

## 🔒 安全规范

### 1. HTTPS协议
- **生产环境强制**: 必须使用HTTPS协议，确保数据传输安全
- **证书验证**: 验证服务器证书有效性
- **混合内容检查**: 避免HTTPS页面加载HTTP资源

### 2. Token管理

#### 2.1 Token存储安全
```javascript
// 推荐的Token存储方式
const TokenManager = {
  // 存储Token
  setToken(token) {
    try {
      uni.setStorageSync('token', token);
      // 可选：设置Token过期时间
      uni.setStorageSync('token_expire', Date.now() + 7 * 24 * 60 * 60 * 1000);
    } catch (error) {
      console.error('Token存储失败:', error);
    }
  },

  // 获取Token
  getToken() {
    try {
      const token = uni.getStorageSync('token');
      const expire = uni.getStorageSync('token_expire');

      // 检查Token是否过期
      if (expire && Date.now() > expire) {
        this.clearToken();
        return null;
      }

      return token;
    } catch (error) {
      console.error('Token获取失败:', error);
      return null;
    }
  },

  // 清除Token
  clearToken() {
    try {
      uni.removeStorageSync('token');
      uni.removeStorageSync('token_expire');
    } catch (error) {
      console.error('Token清除失败:', error);
    }
  }
};
```

#### 2.2 Token刷新机制
```javascript
// Token自动刷新实现
const TokenRefreshManager = {
  isRefreshing: false,
  failedQueue: [],

  async refreshToken() {
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      });
    }

    this.isRefreshing = true;

    try {
      const { data } = await uni.http.post(uni.api.refreshToken);

      if (data.success) {
        TokenManager.setToken(data.result.token);
        this.processQueue(null, data.result.token);
        return data.result.token;
      } else {
        throw new Error('Token刷新失败');
      }
    } catch (error) {
      this.processQueue(error, null);
      TokenManager.clearToken();
      toLoginAndBackPage();
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  },

  processQueue(error, token = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }
};
```

### 3. 请求安全增强

#### 3.1 请求签名验证
```javascript
// 请求签名工具
const RequestSigner = {
  // 生成请求签名
  generateSign(url, params, timestamp) {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');

    const signString = `${url}?${sortedParams}&timestamp=${timestamp}`;
    return this.md5(signString + 'your_secret_key');
  },

  // MD5加密（需要引入crypto-js或其他加密库）
  md5(str) {
    // 实现MD5加密
    return CryptoJS.MD5(str).toString();
  }
};
```

#### 3.2 敏感信息保护
- **数据加密**: 敏感数据传输前进行AES加密
- **参数过滤**: 避免在URL中传递敏感信息
- **日志脱敏**: 请求日志中过滤敏感字段

```javascript
// 敏感数据加密示例
const DataEncryption = {
  // 加密敏感数据
  encryptSensitiveData(data) {
    const sensitiveFields = ['password', 'phone', 'idCard'];
    const encrypted = { ...data };

    sensitiveFields.forEach(field => {
      if (encrypted[field]) {
        encrypted[field] = this.aesEncrypt(encrypted[field]);
      }
    });

    return encrypted;
  },

  // AES加密
  aesEncrypt(text) {
    // 实现AES加密
    return CryptoJS.AES.encrypt(text, 'your_secret_key').toString();
  }
};
```

### 4. API调用频率限制

```javascript
// 请求频率限制器
const RateLimiter = {
  requests: new Map(),

  // 检查请求频率
  checkRate(api, limit = 10, window = 60000) {
    const now = Date.now();
    const key = `${api}_${Math.floor(now / window)}`;

    const count = this.requests.get(key) || 0;
    if (count >= limit) {
      throw new Error('请求过于频繁，请稍后再试');
    }

    this.requests.set(key, count + 1);

    // 清理过期记录
    this.cleanup(now, window);
  },

  cleanup(now, window) {
    for (const [key, value] of this.requests.entries()) {
      const timestamp = parseInt(key.split('_').pop()) * window;
      if (now - timestamp > window) {
        this.requests.delete(key);
      }
    }
  }
};
```

---

## 📱 平台差异化处理

### 条件编译示例

```javascript
// 平台标识
// #ifdef MP-WEIXIN
'softModel': 0,  // 微信小程序
// #endif
// #ifdef H5
'softModel': 3,  // H5
// #endif
// #ifdef APP-PLUS
'softModel': 1,  // APP
// #endif
```

---

## 🎯 开发建议

### 1. 代码组织
- 将复杂的API调用封装为hooks
- 按功能模块组织API接口
- 保持代码的可读性和可维护性

### 2. 性能优化
- 合理使用请求缓存
- 避免重复请求
- 实现请求防抖和节流

### 3. 调试支持
- 添加详细的请求日志
- 使用开发环境的调试工具
- 保留错误信息用于问题排查

---

## ✅ 检查清单

开发时请确保：

- [ ] 使用 `uni.http` 实例发起请求
- [ ] API路径在 `hooks/api.js` 中定义
- [ ] GET请求参数通过 `{ params }` 传递
- [ ] 使用 `async/await` 语法
- [ ] 添加适当的错误处理
- [ ] 遵循接口路径前缀规范
- [ ] 使用条件编译处理平台差异
- [ ] 添加必要的加载提示和错误提示

---

## 🛠️ 常见问题与解决方案

### 1. GET请求参数传递问题

**❌ 错误方式**:
```javascript
// 这种方式会导致参数无法正确传递
const res = await uni.http.get(uni.api.getClassRanking, params)
```

**✅ 正确方式**:
```javascript
// 参数必须通过 { params } 对象传递
const res = await uni.http.get(uni.api.getClassRanking, { params })
```

### 2. 响应数据格式兼容处理

```javascript
// 标准化响应数据处理
function parseResponse(res) {
  let responseData = null;

  // 优先使用标准的uni.http响应格式
  if (res.data && typeof res.data === 'object') {
    responseData = res.data;
  } else if (res.success && res.result) {
    // 兼容直接返回的格式
    responseData = res;
  } else {
    console.error('未知的响应格式:', res);
    return null;
  }

  return responseData;
}
```

### 3. Token失效自动处理

```javascript
// 在hooks/index.js中实现
export function toLoginAndBackPage() {
  const currentPages = getCurrentPages();
  const currentPage = currentPages[currentPages.length - 1];
  const currentRoute = currentPage.route;
  const currentOptions = currentPage.options;

  // 构建返回URL
  let backUrl = '/' + currentRoute;
  if (Object.keys(currentOptions).length > 0) {
    const query = Object.keys(currentOptions)
      .map(key => `${key}=${currentOptions[key]}`)
      .join('&');
    backUrl += '?' + query;
  }

  // 跳转到登录页，并传递返回地址
  uni.navigateTo({
    url: `/pages/login/login?backUrl=${encodeURIComponent(backUrl)}`
  });
}
```

### 4. 请求重试机制

```javascript
// 实现简单的请求重试
async function requestWithRetry(requestFn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error;
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}

// 使用示例
const result = await requestWithRetry(async () => {
  return await uni.http.get(uni.api.getMemberInfo);
});
```

---

## 🔍 调试技巧

### 1. 请求日志记录

```javascript
// 在请求拦截器中添加日志
http.interceptors.request.use(config => {
  console.log('🚀 发起请求:', {
    url: config.baseURL + config.url,
    method: config.method,
    params: config.params,
    data: config.data,
    headers: config.header
  });

  return config;
});

// 在响应拦截器中添加日志
http.interceptors.response.use(response => {
  console.log('📥 收到响应:', {
    url: response.config.url,
    status: response.statusCode,
    data: response.data
  });

  return response;
});
```

### 2. 开发环境Mock数据

```javascript
// 在hooks/request.js中添加Mock支持
if (process.env.NODE_ENV === 'development' && uni.env.USE_MOCK) {
  http.interceptors.request.use(config => {
    // 检查是否有对应的Mock数据
    const mockData = getMockData(config.url);
    if (mockData) {
      return Promise.resolve({
        data: mockData,
        statusCode: 200,
        config
      });
    }
    return config;
  });
}
```

---

## 📊 性能优化建议

### 1. 请求缓存策略

```javascript
// 简单的内存缓存实现
const requestCache = new Map();

async function cachedRequest(url, options = {}, cacheTime = 5 * 60 * 1000) {
  const cacheKey = `${url}_${JSON.stringify(options)}`;
  const cached = requestCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < cacheTime) {
    return cached.data;
  }

  const response = await uni.http.get(url, options);
  requestCache.set(cacheKey, {
    data: response,
    timestamp: Date.now()
  });

  return response;
}
```

### 2. 请求防抖

```javascript
// 防抖工具函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 使用示例
const debouncedSearch = debounce(async (keyword) => {
  const result = await uni.http.get(uni.api.searchGoods, {
    params: { keyword }
  });
  // 处理搜索结果
}, 300);
```

### 3. 并发请求控制

```javascript
// 限制并发请求数量
class RequestQueue {
  constructor(maxConcurrent = 5) {
    this.maxConcurrent = maxConcurrent;
    this.running = 0;
    this.queue = [];
  }

  async add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ requestFn, resolve, reject });
      this.process();
    });
  }

  async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    this.running++;
    const { requestFn, resolve, reject } = this.queue.shift();

    try {
      const result = await requestFn();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process();
    }
  }
}

// 全局请求队列
const requestQueue = new RequestQueue(5);

// 使用示例
const result = await requestQueue.add(() =>
  uni.http.get(uni.api.getMemberInfo)
);
```

---

## � 与heartful-mall项目集成

### 1. jeecg-boot框架集成

#### 1.1 统一响应格式处理
```javascript
// 响应数据标准化处理
const ResponseHandler = {
  // 处理jeecg-boot标准响应格式
  parseJeecgResponse(response) {
    const data = response.data;

    // 标准Result<T>格式
    if (data && typeof data === 'object') {
      return {
        success: data.success || false,
        result: data.result || null,
        message: data.message || '',
        code: data.code || 0,
        timestamp: data.timestamp || Date.now()
      };
    }

    return null;
  },

  // 检查响应是否成功
  isSuccess(response) {
    const parsed = this.parseJeecgResponse(response);
    return parsed && parsed.success === true;
  },

  // 获取业务数据
  getResult(response) {
    const parsed = this.parseJeecgResponse(response);
    return parsed ? parsed.result : null;
  }
};
```

#### 1.2 权限体系对接
```javascript
// 权限验证工具
const PermissionManager = {
  // 检查接口权限
  checkApiPermission(apiPath) {
    const authRequiredPrefixes = uni.apiMetadata.authRequiredPrefixes;
    return authRequiredPrefixes.some(prefix => apiPath.startsWith(prefix));
  },

  // 获取当前用户权限
  getCurrentUserPermissions() {
    const userInfo = userStore().userInfo;
    return userInfo?.permissions || [];
  },

  // 验证用户是否有接口访问权限
  hasApiAccess(apiPath) {
    if (!this.checkApiPermission(apiPath)) {
      return true; // 公开接口
    }

    const token = TokenManager.getToken();
    return !!token; // 需要登录的接口检查Token
  }
};
```

### 2. 与后端LoginMemberUtil对接

```javascript
// 会员信息管理
const MemberManager = {
  // 获取当前登录会员ID（对应后端LoginMemberUtil.getLoginMemberId()）
  getLoginMemberId() {
    const userInfo = userStore().userInfo;
    return userInfo?.id || userInfo?.memberId || null;
  },

  // 获取会员完整信息
  async getMemberInfo() {
    try {
      const { data } = await uni.http.get(uni.api.getMemberInfo);
      const result = ResponseHandler.parseJeecgResponse({ data });

      if (result.success) {
        // 更新本地用户信息
        userStore().setUserInfo(result.result);
        return result.result;
      }

      throw new Error(result.message || '获取用户信息失败');
    } catch (error) {
      console.error('获取会员信息失败:', error);
      throw error;
    }
  },

  // 验证会员身份
  validateMemberIdentity() {
    const memberId = this.getLoginMemberId();
    const token = TokenManager.getToken();

    return !!(memberId && token);
  }
};
```

### 3. MVP开发流程对接

```javascript
// MVP开发模式适配器
const MVPAdapter = {
  // 基础功能优先的API调用
  async callBasicApi(apiPath, params = {}, options = {}) {
    const {
      enableCache = false,
      fallbackData = null,
      timeout = 10000
    } = options;

    try {
      // 基础功能调用，快速响应
      const config = {
        timeout,
        ...options
      };

      const response = await uni.http.get(apiPath, { params }, config);
      return ResponseHandler.parseJeecgResponse(response);
    } catch (error) {
      console.warn(`API调用失败，使用降级方案: ${apiPath}`, error);

      // MVP模式：提供降级方案
      if (fallbackData) {
        return {
          success: true,
          result: fallbackData,
          message: '使用缓存数据',
          code: 200
        };
      }

      throw error;
    }
  },

  // 渐进式功能增强
  async enhanceWithAdvancedFeatures(basicResult, enhancementApis = []) {
    if (!basicResult.success) {
      return basicResult;
    }

    // 并行调用增强功能API
    const enhancements = await Promise.allSettled(
      enhancementApis.map(api => uni.http.get(api))
    );

    // 合并增强数据
    enhancements.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const parsed = ResponseHandler.parseJeecgResponse(result.value);
        if (parsed.success) {
          basicResult.result[`enhancement_${index}`] = parsed.result;
        }
      }
    });

    return basicResult;
  }
};
```

### 4. 任务管理器集成

```javascript
// 网络请求任务管理
const NetworkTaskManager = {
  activeTasks: new Map(),

  // 创建网络请求任务
  createTask(taskId, apiPath, params) {
    const task = {
      id: taskId,
      api: apiPath,
      params,
      status: 'pending',
      startTime: Date.now(),
      retryCount: 0,
      maxRetries: 3
    };

    this.activeTasks.set(taskId, task);
    return task;
  },

  // 执行任务
  async executeTask(taskId) {
    const task = this.activeTasks.get(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }

    task.status = 'running';

    try {
      const response = await uni.http.get(task.api, { params: task.params });
      task.status = 'completed';
      task.result = ResponseHandler.parseJeecgResponse(response);
      task.endTime = Date.now();

      return task.result;
    } catch (error) {
      task.status = 'failed';
      task.error = error;
      task.retryCount++;

      // 自动重试机制
      if (task.retryCount < task.maxRetries) {
        console.log(`任务重试 ${task.retryCount}/${task.maxRetries}: ${taskId}`);
        await new Promise(resolve => setTimeout(resolve, 1000 * task.retryCount));
        return this.executeTask(taskId);
      }

      throw error;
    }
  },

  // 获取任务状态
  getTaskStatus(taskId) {
    const task = this.activeTasks.get(taskId);
    return task ? task.status : 'not_found';
  }
};
```

---

## �📚 相关文档

- [luch-request官方文档](https://www.quanzhan.co/luch-request/)
- [uni-app网络请求文档](https://uniapp.dcloud.net.cn/api/request/request.html)
- [jeecg-boot框架文档](https://jeecg.com/)
- [heartful-mall后端API规范](../后端开发规范/API接口规范.md)
- [Pinia状态管理规范](./状态管理规范.md)
- [C端小程序开发规范](./C端小程序开发规范.md)
- [heartful-mall项目架构文档](../技术架构/项目架构设计.md)

---

## 📝 更新日志

| 版本 | 日期 | 更新内容 | 作者 |
|------|------|----------|------|
| v1.0.0 | 2025-01-02 | 初始版本，基于项目实际代码整理 | AI助手 |
| v2.0.0 | 2025-01-07 | 全面优化规范，增强安全性和项目集成度 | 自省姐 |

**v2.0.0 主要更新内容：**
- ✅ **优化基础配置**: 调整超时时间为30秒，保持Content-Type符合jeecg-boot要求
- ✅ **完善错误处理**: 支持多种错误码，增加特殊接口白名单机制
- ✅ **增强安全规范**: 添加Token刷新、请求签名、频率限制等安全机制
- ✅ **API管理优化**: 增加版本管理、元数据配置、缓存策略
- ✅ **项目深度集成**: 与jeecg-boot框架、LoginMemberUtil、MVP开发流程深度对接
- ✅ **任务管理集成**: 支持网络请求任务管理和自动重试机制

---

**注意**: 本规范基于heartful-mall-app项目的实际代码分析整理，严格遵循项目现有技术栈和开发规范。所有优化建议均考虑了向后兼容性和实际可操作性。
