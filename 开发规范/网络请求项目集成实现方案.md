# 🔗 网络请求项目集成实现方案

## 📋 概述

本文档提供了heartful-mall-app C端用户小程序网络请求与项目整体架构深度集成的具体实现方案，包括与jeecg-boot框架集成、MVP开发流程对接、任务管理器集成等。

---

## 🏗️ 1. jeecg-boot框架深度集成

### 1.1 统一响应格式处理器

**文件位置**: `hooks/integration/JeecgResponseHandler.js`

```javascript
/**
 * jeecg-boot响应格式处理器
 * 统一处理后端返回的Result<T>格式数据
 */
class JeecgResponseHandler {
  constructor() {
    this.name = 'JeecgResponseHandler';
    this.version = '1.0.0';
  }

  /**
   * 标准化响应数据
   * @param {Object} response - 原始响应
   * @returns {Object} 标准化后的响应
   */
  standardizeResponse(response) {
    const data = response.data;
    
    // 检查是否为jeecg-boot标准格式
    if (this.isJeecgFormat(data)) {
      return {
        success: data.success || false,
        result: data.result || null,
        message: data.message || '',
        code: data.code || 0,
        timestamp: data.timestamp || Date.now(),
        original: data
      };
    }
    
    // 兼容其他格式
    return {
      success: true,
      result: data,
      message: 'success',
      code: 200,
      timestamp: Date.now(),
      original: data
    };
  }

  /**
   * 检查是否为jeecg-boot标准格式
   * @param {Object} data - 响应数据
   * @returns {boolean} 是否为标准格式
   */
  isJeecgFormat(data) {
    return data && 
           typeof data === 'object' && 
           'success' in data && 
           'message' in data;
  }

  /**
   * 提取业务数据
   * @param {Object} response - 响应对象
   * @returns {any} 业务数据
   */
  extractResult(response) {
    const standardized = this.standardizeResponse(response);
    return standardized.success ? standardized.result : null;
  }

  /**
   * 检查响应是否成功
   * @param {Object} response - 响应对象
   * @returns {boolean} 是否成功
   */
  isSuccess(response) {
    const standardized = this.standardizeResponse(response);
    return standardized.success === true;
  }

  /**
   * 获取错误信息
   * @param {Object} response - 响应对象
   * @returns {string} 错误信息
   */
  getErrorMessage(response) {
    const standardized = this.standardizeResponse(response);
    return standardized.message || '操作失败';
  }

  /**
   * 处理分页数据
   * @param {Object} response - 响应对象
   * @returns {Object} 分页数据
   */
  handlePageResult(response) {
    const result = this.extractResult(response);
    
    if (result && typeof result === 'object') {
      return {
        current: result.current || 1,
        size: result.size || 10,
        total: result.total || 0,
        pages: result.pages || 0,
        records: result.records || []
      };
    }
    
    return {
      current: 1,
      size: 10,
      total: 0,
      pages: 0,
      records: []
    };
  }

  /**
   * 创建标准响应
   * @param {boolean} success - 是否成功
   * @param {any} result - 结果数据
   * @param {string} message - 消息
   * @param {number} code - 状态码
   * @returns {Object} 标准响应
   */
  createResponse(success, result = null, message = '', code = 200) {
    return {
      success,
      result,
      message,
      code,
      timestamp: Date.now()
    };
  }
}

// 创建单例实例
const jeecgResponseHandler = new JeecgResponseHandler();

export default jeecgResponseHandler;
```

### 1.2 权限体系集成

**文件位置**: `hooks/integration/PermissionIntegration.js`

```javascript
import { userStore } from '@/store/index.js';

/**
 * 权限体系集成
 * 与jeecg-boot权限体系深度集成
 */
class PermissionIntegration {
  constructor() {
    this.permissionCache = new Map();
    this.roleCache = new Map();
  }

  /**
   * 检查API访问权限
   * @param {string} apiPath - API路径
   * @param {string} method - 请求方法
   * @returns {boolean} 是否有权限
   */
  checkApiPermission(apiPath, method = 'GET') {
    // 公开接口无需权限检查
    if (this.isPublicApi(apiPath)) {
      return true;
    }

    // 检查用户是否登录
    const store = userStore();
    if (!store.token || !store.userInfo) {
      return false;
    }

    // 检查具体权限
    return this.hasPermission(apiPath, method);
  }

  /**
   * 检查是否为公开接口
   * @param {string} apiPath - API路径
   * @returns {boolean} 是否为公开接口
   */
  isPublicApi(apiPath) {
    const publicPrefixes = ['/front/', '/before/'];
    return publicPrefixes.some(prefix => apiPath.startsWith(prefix));
  }

  /**
   * 检查用户权限
   * @param {string} resource - 资源标识
   * @param {string} action - 操作类型
   * @returns {boolean} 是否有权限
   */
  hasPermission(resource, action = 'read') {
    const store = userStore();
    const userPermissions = store.userInfo?.permissions || [];
    
    // 超级管理员拥有所有权限
    if (this.isSuperAdmin()) {
      return true;
    }

    // 检查缓存
    const cacheKey = `${resource}_${action}`;
    if (this.permissionCache.has(cacheKey)) {
      return this.permissionCache.get(cacheKey);
    }

    // 权限检查逻辑
    const hasPermission = this.checkUserPermission(userPermissions, resource, action);
    
    // 缓存结果
    this.permissionCache.set(cacheKey, hasPermission);
    
    return hasPermission;
  }

  /**
   * 检查用户具体权限
   * @param {Array} permissions - 用户权限列表
   * @param {string} resource - 资源标识
   * @param {string} action - 操作类型
   * @returns {boolean} 是否有权限
   */
  checkUserPermission(permissions, resource, action) {
    // 精确匹配
    if (permissions.includes(`${resource}:${action}`)) {
      return true;
    }

    // 通配符匹配
    if (permissions.includes(`${resource}:*`)) {
      return true;
    }

    // 角色权限检查
    return this.checkRolePermission(resource, action);
  }

  /**
   * 检查角色权限
   * @param {string} resource - 资源标识
   * @param {string} action - 操作类型
   * @returns {boolean} 是否有权限
   */
  checkRolePermission(resource, action) {
    const store = userStore();
    const userRoles = store.userInfo?.roles || [];
    
    for (const role of userRoles) {
      const rolePermissions = this.getRolePermissions(role);
      if (rolePermissions.includes(`${resource}:${action}`) || 
          rolePermissions.includes(`${resource}:*`)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 获取角色权限
   * @param {string} role - 角色标识
   * @returns {Array} 权限列表
   */
  getRolePermissions(role) {
    if (this.roleCache.has(role)) {
      return this.roleCache.get(role);
    }

    // 这里应该从后端获取角色权限，暂时返回空数组
    const permissions = [];
    this.roleCache.set(role, permissions);
    
    return permissions;
  }

  /**
   * 检查是否为超级管理员
   * @returns {boolean} 是否为超级管理员
   */
  isSuperAdmin() {
    const store = userStore();
    const userRoles = store.userInfo?.roles || [];
    return userRoles.includes('super_admin') || userRoles.includes('admin');
  }

  /**
   * 清除权限缓存
   */
  clearPermissionCache() {
    this.permissionCache.clear();
    this.roleCache.clear();
  }

  /**
   * 刷新用户权限
   * @returns {Promise} 刷新Promise
   */
  async refreshUserPermissions() {
    try {
      // 清除缓存
      this.clearPermissionCache();
      
      // 重新获取用户信息
      const store = userStore();
      await store.getUserInfo();
      
      console.log('用户权限已刷新');
    } catch (error) {
      console.error('刷新用户权限失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const permissionIntegration = new PermissionIntegration();

export default permissionIntegration;
```

### 1.3 会员信息管理集成

**文件位置**: `hooks/integration/MemberIntegration.js`

```javascript
import { userStore } from '@/store/index.js';
import jeecgResponseHandler from './JeecgResponseHandler.js';

/**
 * 会员信息管理集成
 * 对应后端LoginMemberUtil工具类
 */
class MemberIntegration {
  constructor() {
    this.memberInfoCache = null;
    this.cacheExpireTime = 10 * 60 * 1000; // 10分钟缓存
    this.lastCacheTime = 0;
  }

  /**
   * 获取当前登录会员ID
   * 对应后端LoginMemberUtil.getLoginMemberId()
   * @returns {string|null} 会员ID
   */
  getLoginMemberId() {
    const store = userStore();
    const userInfo = store.userInfo;
    
    return userInfo?.id || userInfo?.memberId || null;
  }

  /**
   * 获取当前登录会员信息
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 会员信息
   */
  async getLoginMemberInfo(useCache = true) {
    // 检查缓存
    if (useCache && this.isValidCache()) {
      return this.memberInfoCache;
    }

    try {
      const response = await uni.http.get(uni.api.getMemberInfo);
      const standardized = jeecgResponseHandler.standardizeResponse(response);
      
      if (standardized.success) {
        const memberInfo = standardized.result;
        
        // 更新本地存储
        const store = userStore();
        store.setUserInfo(memberInfo);
        
        // 更新缓存
        this.memberInfoCache = memberInfo;
        this.lastCacheTime = Date.now();
        
        return memberInfo;
      } else {
        throw new Error(standardized.message || '获取会员信息失败');
      }
    } catch (error) {
      console.error('获取会员信息失败:', error);
      throw error;
    }
  }

  /**
   * 验证会员身份
   * @returns {boolean} 是否为有效会员
   */
  validateMemberIdentity() {
    const memberId = this.getLoginMemberId();
    const store = userStore();
    const token = store.token;
    
    return !!(memberId && token);
  }

  /**
   * 获取会员余额
   * @returns {Promise<number>} 会员余额
   */
  async getMemberBalance() {
    try {
      const memberInfo = await this.getLoginMemberInfo();
      return memberInfo?.balance || 0;
    } catch (error) {
      console.error('获取会员余额失败:', error);
      return 0;
    }
  }

  /**
   * 获取会员积分
   * @returns {Promise<number>} 会员积分
   */
  async getMemberPoints() {
    try {
      const memberInfo = await this.getLoginMemberInfo();
      return memberInfo?.points || 0;
    } catch (error) {
      console.error('获取会员积分失败:', error);
      return 0;
    }
  }

  /**
   * 获取会员等级
   * @returns {Promise<number>} 会员等级
   */
  async getMemberLevel() {
    try {
      const memberInfo = await this.getLoginMemberInfo();
      return memberInfo?.memberLevel || 1;
    } catch (error) {
      console.error('获取会员等级失败:', error);
      return 1;
    }
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isValidCache() {
    return this.memberInfoCache && 
           (Date.now() - this.lastCacheTime) < this.cacheExpireTime;
  }

  /**
   * 清除会员信息缓存
   */
  clearMemberCache() {
    this.memberInfoCache = null;
    this.lastCacheTime = 0;
  }

  /**
   * 更新会员信息
   * @param {Object} memberInfo - 会员信息
   */
  updateMemberInfo(memberInfo) {
    const store = userStore();
    store.setUserInfo(memberInfo);
    
    // 更新缓存
    this.memberInfoCache = memberInfo;
    this.lastCacheTime = Date.now();
  }

  /**
   * 检查会员状态
   * @returns {Promise<Object>} 会员状态信息
   */
  async checkMemberStatus() {
    try {
      const memberInfo = await this.getLoginMemberInfo();
      
      return {
        isActive: memberInfo?.status === 1,
        isVip: memberInfo?.memberLevel > 1,
        hasBalance: (memberInfo?.balance || 0) > 0,
        hasPoints: (memberInfo?.points || 0) > 0,
        memberLevel: memberInfo?.memberLevel || 1,
        registerDays: this.calculateRegisterDays(memberInfo?.createTime)
      };
    } catch (error) {
      console.error('检查会员状态失败:', error);
      return {
        isActive: false,
        isVip: false,
        hasBalance: false,
        hasPoints: false,
        memberLevel: 1,
        registerDays: 0
      };
    }
  }

  /**
   * 计算注册天数
   * @param {string} createTime - 注册时间
   * @returns {number} 注册天数
   */
  calculateRegisterDays(createTime) {
    if (!createTime) return 0;
    
    const registerDate = new Date(createTime);
    const now = new Date();
    const diffTime = Math.abs(now - registerDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }

  /**
   * 同步会员信息到本地存储
   * @returns {Promise<void>} 同步Promise
   */
  async syncMemberInfoToLocal() {
    try {
      const memberInfo = await this.getLoginMemberInfo(false);
      
      // 同步到本地存储
      uni.setStorageSync('memberInfo', memberInfo);
      uni.setStorageSync('memberInfoUpdateTime', Date.now());
      
      console.log('会员信息已同步到本地存储');
    } catch (error) {
      console.error('同步会员信息到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储恢复会员信息
   * @returns {Object|null} 会员信息
   */
  restoreMemberInfoFromLocal() {
    try {
      const memberInfo = uni.getStorageSync('memberInfo');
      const updateTime = uni.getStorageSync('memberInfoUpdateTime');
      
      // 检查本地数据是否过期
      if (memberInfo && updateTime && 
          (Date.now() - updateTime) < this.cacheExpireTime) {
        
        this.memberInfoCache = memberInfo;
        this.lastCacheTime = updateTime;
        
        // 更新store
        const store = userStore();
        store.setUserInfo(memberInfo);
        
        return memberInfo;
      }
      
      return null;
    } catch (error) {
      console.error('从本地存储恢复会员信息失败:', error);
      return null;
    }
  }
}

// 创建单例实例
const memberIntegration = new MemberIntegration();

export default memberIntegration;
```

---

## 🚀 2. MVP开发流程对接

### 2.1 MVP适配器

**文件位置**: `hooks/integration/MVPAdapter.js`

```javascript
import jeecgResponseHandler from './JeecgResponseHandler.js';

/**
 * MVP开发模式适配器
 * 支持基础功能优先、渐进式增强的开发模式
 */
class MVPAdapter {
  constructor() {
    this.fallbackData = new Map();
    this.featureFlags = new Map();
    this.performanceMetrics = new Map();
  }

  /**
   * 基础功能优先的API调用
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {Object} params - 请求参数
   * @param {Object} options - 选项
   * @returns {Promise} API调用Promise
   */
  async callBasicApi(category, action, params = {}, options = {}) {
    const {
      enableCache = true,
      fallbackData = null,
      timeout = 10000,
      retryCount = 1,
      enableFallback = true
    } = options;

    const startTime = Date.now();
    
    try {
      // 基础功能调用
      const config = {
        timeout,
        enableCache,
        ...options
      };

      const response = await uni.apiClient.get(category, action, params, config);
      const standardized = jeecgResponseHandler.standardizeResponse(response);
      
      // 记录性能指标
      this.recordPerformance(`${category}.${action}`, Date.now() - startTime, true);
      
      return standardized;
    } catch (error) {
      console.warn(`基础API调用失败，尝试降级方案: ${category}.${action}`, error);
      
      // 记录性能指标
      this.recordPerformance(`${category}.${action}`, Date.now() - startTime, false);
      
      // MVP模式：提供降级方案
      if (enableFallback) {
        return this.handleApiFallback(category, action, fallbackData, error);
      }
      
      throw error;
    }
  }

  /**
   * 处理API降级方案
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {any} fallbackData - 降级数据
   * @param {Error} originalError - 原始错误
   * @returns {Object} 降级响应
   */
  handleApiFallback(category, action, fallbackData, originalError) {
    const apiKey = `${category}.${action}`;
    
    // 1. 使用提供的降级数据
    if (fallbackData) {
      console.log(`使用提供的降级数据: ${apiKey}`);
      return jeecgResponseHandler.createResponse(true, fallbackData, '使用降级数据');
    }
    
    // 2. 使用预设的降级数据
    const presetFallback = this.fallbackData.get(apiKey);
    if (presetFallback) {
      console.log(`使用预设降级数据: ${apiKey}`);
      return jeecgResponseHandler.createResponse(true, presetFallback, '使用预设降级数据');
    }
    
    // 3. 使用本地缓存数据
    const cachedData = this.getLocalCachedData(apiKey);
    if (cachedData) {
      console.log(`使用本地缓存数据: ${apiKey}`);
      return jeecgResponseHandler.createResponse(true, cachedData, '使用本地缓存数据');
    }
    
    // 4. 返回空数据结构
    const emptyData = this.generateEmptyData(category, action);
    console.log(`使用空数据结构: ${apiKey}`);
    return jeecgResponseHandler.createResponse(true, emptyData, '使用空数据结构');
  }

  /**
   * 渐进式功能增强
   * @param {Object} basicResult - 基础结果
   * @param {Array} enhancementApis - 增强功能API列表
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 增强后的结果
   */
  async enhanceWithAdvancedFeatures(basicResult, enhancementApis = [], options = {}) {
    if (!basicResult.success) {
      return basicResult;
    }

    const {
      enableParallel = true,
      timeout = 5000,
      ignoreErrors = true
    } = options;

    try {
      let enhancements;
      
      if (enableParallel) {
        // 并行调用增强功能API
        enhancements = await Promise.allSettled(
          enhancementApis.map(api => this.callEnhancementApi(api, timeout))
        );
      } else {
        // 串行调用增强功能API
        enhancements = [];
        for (const api of enhancementApis) {
          try {
            const result = await this.callEnhancementApi(api, timeout);
            enhancements.push({ status: 'fulfilled', value: result });
          } catch (error) {
            enhancements.push({ status: 'rejected', reason: error });
          }
        }
      }

      // 合并增强数据
      enhancements.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const standardized = jeecgResponseHandler.standardizeResponse(result.value);
          if (standardized.success) {
            const enhancementKey = `enhancement_${index}`;
            basicResult.result[enhancementKey] = standardized.result;
          }
        } else if (!ignoreErrors) {
          console.error(`增强功能API调用失败:`, result.reason);
        }
      });

      return basicResult;
    } catch (error) {
      console.error('功能增强失败:', error);
      
      if (ignoreErrors) {
        return basicResult; // 忽略错误，返回基础结果
      } else {
        throw error;
      }
    }
  }

  /**
   * 调用增强功能API
   * @param {Object} api - API配置
   * @param {number} timeout - 超时时间
   * @returns {Promise} API调用Promise
   */
  async callEnhancementApi(api, timeout) {
    const { category, action, params = {}, options = {} } = api;
    
    return uni.apiClient.get(category, action, params, {
      timeout,
      enableCache: true,
      ...options
    });
  }

  /**
   * 设置降级数据
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @param {any} data - 降级数据
   */
  setFallbackData(category, action, data) {
    const apiKey = `${category}.${action}`;
    this.fallbackData.set(apiKey, data);
    console.log(`降级数据已设置: ${apiKey}`);
  }

  /**
   * 设置功能开关
   * @param {string} feature - 功能名称
   * @param {boolean} enabled - 是否启用
   */
  setFeatureFlag(feature, enabled) {
    this.featureFlags.set(feature, enabled);
    console.log(`功能开关已设置: ${feature} = ${enabled}`);
  }

  /**
   * 检查功能是否启用
   * @param {string} feature - 功能名称
   * @returns {boolean} 是否启用
   */
  isFeatureEnabled(feature) {
    return this.featureFlags.get(feature) || false;
  }

  /**
   * 获取本地缓存数据
   * @param {string} apiKey - API键
   * @returns {any} 缓存数据
   */
  getLocalCachedData(apiKey) {
    try {
      const cacheKey = `mvp_cache_${apiKey}`;
      const cached = uni.getStorageSync(cacheKey);
      
      if (cached) {
        const parsedCache = JSON.parse(cached);
        const expireTime = parsedCache.expireTime || 0;
        
        if (Date.now() < expireTime) {
          return parsedCache.data;
        } else {
          uni.removeStorageSync(cacheKey);
        }
      }
      
      return null;
    } catch (error) {
      console.error('获取本地缓存数据失败:', error);
      return null;
    }
  }

  /**
   * 生成空数据结构
   * @param {string} category - API分类
   * @param {string} action - API动作
   * @returns {any} 空数据结构
   */
  generateEmptyData(category, action) {
    // 根据API类型生成相应的空数据结构
    const emptyDataTemplates = {
      'mall.indexNew': {
        banners: [],
        categories: [],
        hotGoods: [],
        notices: []
      },
      'order.list': {
        current: 1,
        size: 10,
        total: 0,
        pages: 0,
        records: []
      },
      'task.myPublished': {
        current: 1,
        size: 10,
        total: 0,
        pages: 0,
        records: []
      }
    };

    const apiKey = `${category}.${action}`;
    return emptyDataTemplates[apiKey] || {};
  }

  /**
   * 记录性能指标
   * @param {string} api - API标识
   * @param {number} duration - 耗时
   * @param {boolean} success - 是否成功
   */
  recordPerformance(api, duration, success) {
    if (!this.performanceMetrics.has(api)) {
      this.performanceMetrics.set(api, {
        totalCalls: 0,
        successCalls: 0,
        totalDuration: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Infinity
      });
    }

    const metrics = this.performanceMetrics.get(api);
    metrics.totalCalls++;
    
    if (success) {
      metrics.successCalls++;
    }
    
    metrics.totalDuration += duration;
    metrics.avgDuration = metrics.totalDuration / metrics.totalCalls;
    metrics.maxDuration = Math.max(metrics.maxDuration, duration);
    metrics.minDuration = Math.min(metrics.minDuration, duration);
  }

  /**
   * 获取性能指标
   * @param {string} api - API标识
   * @returns {Object} 性能指标
   */
  getPerformanceMetrics(api) {
    return this.performanceMetrics.get(api) || null;
  }

  /**
   * 获取所有性能指标
   * @returns {Object} 所有性能指标
   */
  getAllPerformanceMetrics() {
    const result = {};
    for (const [api, metrics] of this.performanceMetrics.entries()) {
      result[api] = { ...metrics };
    }
    return result;
  }

  /**
   * 清除性能指标
   */
  clearPerformanceMetrics() {
    this.performanceMetrics.clear();
  }
}

// 创建单例实例
const mvpAdapter = new MVPAdapter();

export default mvpAdapter;
```

---

## 🔧 3. 任务管理器集成

### 3.1 网络请求任务管理器

**文件位置**: `hooks/integration/NetworkTaskManager.js`

```javascript
/**
 * 网络请求任务管理器
 * 与项目任务管理器深度集成
 */
class NetworkTaskManager {
  constructor() {
    this.activeTasks = new Map();
    this.taskHistory = [];
    this.maxHistorySize = 100;
    this.taskIdCounter = 0;
  }

  /**
   * 创建网络请求任务
   * @param {Object} config - 任务配置
   * @returns {Object} 任务对象
   */
  createTask(config) {
    const taskId = this.generateTaskId();
    const task = {
      id: taskId,
      category: config.category,
      action: config.action,
      params: config.params || {},
      options: config.options || {},
      status: 'pending',
      priority: config.priority || 'normal',
      createTime: Date.now(),
      startTime: null,
      endTime: null,
      duration: 0,
      retryCount: 0,
      maxRetries: config.maxRetries || 3,
      result: null,
      error: null,
      metadata: {
        userAgent: uni.getSystemInfoSync().platform,
        networkType: 'unknown',
        ...config.metadata
      }
    };

    this.activeTasks.set(taskId, task);
    console.log(`网络请求任务已创建: ${taskId} - ${config.category}.${config.action}`);

    return task;
  }

  /**
   * 执行任务
   * @param {string} taskId - 任务ID
   * @returns {Promise} 执行Promise
   */
  async executeTask(taskId) {
    const task = this.activeTasks.get(taskId);
    if (!task) {
      throw new Error(`任务不存在: ${taskId}`);
    }

    task.status = 'running';
    task.startTime = Date.now();

    try {
      // 获取网络信息
      await this.updateNetworkInfo(task);

      // 执行API调用
      const response = await uni.apiClient.get(
        task.category,
        task.action,
        task.params,
        task.options
      );

      // 处理成功响应
      task.status = 'completed';
      task.result = response;
      task.endTime = Date.now();
      task.duration = task.endTime - task.startTime;

      console.log(`任务执行成功: ${taskId} (${task.duration}ms)`);

      // 移动到历史记录
      this.moveToHistory(task);

      return response;
    } catch (error) {
      // 处理错误
      task.error = {
        message: error.message,
        code: error.code || 'UNKNOWN',
        stack: error.stack
      };
      task.retryCount++;

      // 自动重试机制
      if (task.retryCount < task.maxRetries) {
        console.log(`任务重试 ${task.retryCount}/${task.maxRetries}: ${taskId}`);

        // 计算重试延迟（指数退避）
        const delay = Math.min(1000 * Math.pow(2, task.retryCount - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));

        return this.executeTask(taskId);
      } else {
        task.status = 'failed';
        task.endTime = Date.now();
        task.duration = task.endTime - task.startTime;

        console.error(`任务执行失败: ${taskId}`, error);

        // 移动到历史记录
        this.moveToHistory(task);

        throw error;
      }
    }
  }

  /**
   * 批量执行任务
   * @param {Array} taskConfigs - 任务配置列表
   * @param {Object} options - 执行选项
   * @returns {Promise<Array>} 执行结果列表
   */
  async executeBatchTasks(taskConfigs, options = {}) {
    const {
      concurrent = 3,
      failFast = false,
      timeout = 30000
    } = options;

    // 创建任务
    const tasks = taskConfigs.map(config => this.createTask(config));

    // 分批执行
    const results = [];
    const batches = this.chunkArray(tasks, concurrent);

    for (const batch of batches) {
      try {
        const batchPromises = batch.map(task =>
          Promise.race([
            this.executeTask(task.id),
            this.createTimeoutPromise(timeout, task.id)
          ])
        );

        if (failFast) {
          const batchResults = await Promise.all(batchPromises);
          results.push(...batchResults);
        } else {
          const batchResults = await Promise.allSettled(batchPromises);
          results.push(...batchResults);
        }
      } catch (error) {
        if (failFast) {
          throw error;
        } else {
          results.push({ status: 'rejected', reason: error });
        }
      }
    }

    return results;
  }

  /**
   * 取消任务
   * @param {string} taskId - 任务ID
   */
  cancelTask(taskId) {
    const task = this.activeTasks.get(taskId);
    if (task && task.status === 'pending') {
      task.status = 'cancelled';
      task.endTime = Date.now();
      task.duration = task.endTime - (task.startTime || task.createTime);

      this.moveToHistory(task);
      console.log(`任务已取消: ${taskId}`);
    }
  }

  /**
   * 获取任务状态
   * @param {string} taskId - 任务ID
   * @returns {string} 任务状态
   */
  getTaskStatus(taskId) {
    const task = this.activeTasks.get(taskId) ||
                 this.taskHistory.find(t => t.id === taskId);
    return task ? task.status : 'not_found';
  }

  /**
   * 获取任务详情
   * @param {string} taskId - 任务ID
   * @returns {Object|null} 任务详情
   */
  getTaskDetail(taskId) {
    return this.activeTasks.get(taskId) ||
           this.taskHistory.find(t => t.id === taskId) ||
           null;
  }

  /**
   * 获取活跃任务列表
   * @returns {Array} 活跃任务列表
   */
  getActiveTasks() {
    return Array.from(this.activeTasks.values());
  }

  /**
   * 获取任务历史
   * @param {Object} filter - 过滤条件
   * @returns {Array} 任务历史列表
   */
  getTaskHistory(filter = {}) {
    let history = [...this.taskHistory];

    if (filter.status) {
      history = history.filter(task => task.status === filter.status);
    }

    if (filter.category) {
      history = history.filter(task => task.category === filter.category);
    }

    if (filter.startTime && filter.endTime) {
      history = history.filter(task =>
        task.createTime >= filter.startTime &&
        task.createTime <= filter.endTime
      );
    }

    return history;
  }

  /**
   * 获取任务统计信息
   * @returns {Object} 统计信息
   */
  getTaskStatistics() {
    const allTasks = [...this.activeTasks.values(), ...this.taskHistory];

    const stats = {
      total: allTasks.length,
      active: this.activeTasks.size,
      completed: 0,
      failed: 0,
      cancelled: 0,
      avgDuration: 0,
      successRate: 0
    };

    let totalDuration = 0;
    let completedCount = 0;

    allTasks.forEach(task => {
      switch (task.status) {
        case 'completed':
          stats.completed++;
          completedCount++;
          totalDuration += task.duration;
          break;
        case 'failed':
          stats.failed++;
          break;
        case 'cancelled':
          stats.cancelled++;
          break;
      }
    });

    if (completedCount > 0) {
      stats.avgDuration = Math.round(totalDuration / completedCount);
      stats.successRate = Math.round((stats.completed / (stats.completed + stats.failed)) * 100);
    }

    return stats;
  }

  /**
   * 清理历史记录
   * @param {Object} options - 清理选项
   */
  cleanupHistory(options = {}) {
    const {
      maxAge = 24 * 60 * 60 * 1000, // 24小时
      maxCount = this.maxHistorySize,
      status = null
    } = options;

    const now = Date.now();

    this.taskHistory = this.taskHistory.filter(task => {
      // 按状态过滤
      if (status && task.status !== status) {
        return true;
      }

      // 按时间过滤
      if (now - task.createTime > maxAge) {
        return false;
      }

      return true;
    });

    // 按数量限制
    if (this.taskHistory.length > maxCount) {
      this.taskHistory = this.taskHistory
        .sort((a, b) => b.createTime - a.createTime)
        .slice(0, maxCount);
    }

    console.log(`任务历史已清理，剩余 ${this.taskHistory.length} 条记录`);
  }

  /**
   * 生成任务ID
   * @returns {string} 任务ID
   */
  generateTaskId() {
    return `task_${Date.now()}_${++this.taskIdCounter}`;
  }

  /**
   * 更新网络信息
   * @param {Object} task - 任务对象
   */
  async updateNetworkInfo(task) {
    try {
      const networkInfo = await uni.getNetworkType();
      task.metadata.networkType = networkInfo.networkType;
    } catch (error) {
      console.warn('获取网络信息失败:', error);
    }
  }

  /**
   * 移动任务到历史记录
   * @param {Object} task - 任务对象
   */
  moveToHistory(task) {
    this.activeTasks.delete(task.id);
    this.taskHistory.unshift(task);

    // 限制历史记录大小
    if (this.taskHistory.length > this.maxHistorySize) {
      this.taskHistory = this.taskHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 数组分块
   * @param {Array} array - 原数组
   * @param {number} size - 块大小
   * @returns {Array} 分块后的数组
   */
  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 创建超时Promise
   * @param {number} timeout - 超时时间
   * @param {string} taskId - 任务ID
   * @returns {Promise} 超时Promise
   */
  createTimeoutPromise(timeout, taskId) {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`任务超时: ${taskId}`));
      }, timeout);
    });
  }
}

// 创建单例实例
const networkTaskManager = new NetworkTaskManager();

export default networkTaskManager;
```

---

## 📋 4. 完整集成示例

### 4.1 初始化集成

**文件位置**: `hooks/integration/index.js`

```javascript
import apiClient from '../client/ApiClient.js';
import CachePlugin from '../plugins/CachePlugin.js';
import jeecgResponseHandler from './JeecgResponseHandler.js';
import permissionIntegration from './PermissionIntegration.js';
import memberIntegration from './MemberIntegration.js';
import mvpAdapter from './MVPAdapter.js';
import networkTaskManager from './NetworkTaskManager.js';

/**
 * 网络请求系统集成初始化
 */
class NetworkIntegration {
  constructor() {
    this.initialized = false;
  }

  /**
   * 初始化集成系统
   * @param {Object} options - 初始化选项
   */
  async initialize(options = {}) {
    if (this.initialized) {
      console.warn('网络请求系统已初始化');
      return;
    }

    try {
      console.log('开始初始化网络请求系统...');

      // 1. 初始化API客户端
      await apiClient.initialize({
        plugins: ['CachePlugin']
      });

      // 2. 设置MVP降级数据
      this.setupMVPFallbackData();

      // 3. 恢复会员信息
      memberIntegration.restoreMemberInfoFromLocal();

      // 4. 设置全局错误处理
      this.setupGlobalErrorHandling();

      // 5. 设置性能监控
      this.setupPerformanceMonitoring();

      this.initialized = true;
      console.log('网络请求系统初始化完成');

      // 6. 执行初始化后的任务
      await this.postInitializationTasks();

    } catch (error) {
      console.error('网络请求系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置MVP降级数据
   */
  setupMVPFallbackData() {
    // 首页降级数据
    mvpAdapter.setFallbackData('mall', 'indexNew', {
      banners: [],
      categories: [],
      hotGoods: [],
      notices: [{ content: '系统维护中，请稍后再试' }]
    });

    // 订单列表降级数据
    mvpAdapter.setFallbackData('order', 'list', {
      current: 1,
      size: 10,
      total: 0,
      pages: 0,
      records: []
    });

    // 任务列表降级数据
    mvpAdapter.setFallbackData('task', 'myPublished', {
      current: 1,
      size: 10,
      total: 0,
      pages: 0,
      records: []
    });

    console.log('MVP降级数据设置完成');
  }

  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandling() {
    // 监听未捕获的Promise错误
    uni.onUnhandledRejection && uni.onUnhandledRejection((event) => {
      console.error('未捕获的Promise错误:', event.reason);

      // 上报错误（可选）
      this.reportError('unhandled_promise_rejection', event.reason);
    });

    // 监听网络状态变化
    uni.onNetworkStatusChange && uni.onNetworkStatusChange((res) => {
      console.log('网络状态变化:', res);

      if (!res.isConnected) {
        uni.showToast({
          icon: 'none',
          title: '网络连接已断开'
        });
      }
    });
  }

  /**
   * 设置性能监控
   */
  setupPerformanceMonitoring() {
    // 定期清理任务历史
    setInterval(() => {
      networkTaskManager.cleanupHistory({
        maxAge: 24 * 60 * 60 * 1000, // 24小时
        maxCount: 50
      });
    }, 60 * 60 * 1000); // 每小时执行一次

    // 定期上报性能指标
    setInterval(() => {
      const stats = networkTaskManager.getTaskStatistics();
      console.log('网络请求性能统计:', stats);

      // 可以在这里上报到监控系统
      this.reportPerformanceMetrics(stats);
    }, 5 * 60 * 1000); // 每5分钟执行一次
  }

  /**
   * 初始化后任务
   */
  async postInitializationTasks() {
    try {
      // 同步会员信息
      if (memberIntegration.validateMemberIdentity()) {
        await memberIntegration.syncMemberInfoToLocal();
      }

      // 刷新权限信息
      if (permissionIntegration.isSuperAdmin()) {
        await permissionIntegration.refreshUserPermissions();
      }

      console.log('初始化后任务执行完成');
    } catch (error) {
      console.warn('初始化后任务执行失败:', error);
    }
  }

  /**
   * 上报错误
   * @param {string} type - 错误类型
   * @param {any} error - 错误信息
   */
  reportError(type, error) {
    // 这里可以集成错误监控服务
    console.log('错误上报:', { type, error });
  }

  /**
   * 上报性能指标
   * @param {Object} metrics - 性能指标
   */
  reportPerformanceMetrics(metrics) {
    // 这里可以集成性能监控服务
    console.log('性能指标上报:', metrics);
  }

  /**
   * 获取系统状态
   * @returns {Object} 系统状态
   */
  getSystemStatus() {
    return {
      initialized: this.initialized,
      apiClient: apiClient.getStatus(),
      memberInfo: memberIntegration.validateMemberIdentity(),
      taskStats: networkTaskManager.getTaskStatistics(),
      performanceMetrics: mvpAdapter.getAllPerformanceMetrics()
    };
  }
}

// 创建单例实例
const networkIntegration = new NetworkIntegration();

// 导出集成模块
export {
  networkIntegration,
  apiClient,
  jeecgResponseHandler,
  permissionIntegration,
  memberIntegration,
  mvpAdapter,
  networkTaskManager
};

export default networkIntegration;
```

### 4.2 使用示例

```javascript
// main.js - 应用启动时初始化
import networkIntegration from '@/hooks/integration/index.js';

App({
  async onLaunch() {
    try {
      // 初始化网络请求系统
      await networkIntegration.initialize();
      console.log('应用启动完成');
    } catch (error) {
      console.error('应用启动失败:', error);
    }
  }
});

// 页面中使用示例
export default {
  async onLoad() {
    try {
      // 使用MVP适配器调用API
      const result = await mvpAdapter.callBasicApi('mall', 'indexNew', {}, {
        enableCache: true,
        fallbackData: { banners: [], categories: [] }
      });

      if (result.success) {
        this.indexData = result.result;
      }
    } catch (error) {
      console.error('加载首页数据失败:', error);
    }
  }
};
```

---

**注意**: 本项目集成方案严格遵循heartful-mall项目的实际架构和开发规范，确保与现有系统的无缝集成和向后兼容性。所有实现都考虑了MVP开发模式、jeecg-boot框架特性和实际部署环境的需求。
