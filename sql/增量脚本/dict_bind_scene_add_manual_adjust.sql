-- =====================================================
-- 扩展bind_scene数据字典，新增"手动调整"枚举值
-- 功能：为会员推荐人手动调整功能添加可追溯性标识
-- 作者：heartful-mall
-- 日期：2025-01-07
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 验证bind_scene字典是否存在
-- =====================================================
SELECT 
    '=== bind_scene字典验证 ===' as verification_step,
    '' as dict_name,
    '' as dict_code,
    '' as description
UNION ALL
SELECT 
    '字典信息',
    d.dict_name,
    d.dict_code,
    d.description
FROM sys_dict d 
WHERE d.dict_code = 'bind_scene' AND d.del_flag = '0';

-- =====================================================
-- 2. 查看现有的bind_scene字典项
-- =====================================================
SELECT 
    '=== 现有bind_scene字典项 ===' as verification_step,
    '' as item_text,
    '' as item_value,
    '' as description,
    '' as sort_order
UNION ALL
SELECT 
    '字典项',
    di.item_text,
    di.item_value,
    di.description,
    CAST(di.sort_order AS CHAR)
FROM sys_dict_item di
JOIN sys_dict d ON d.id = di.dict_id
WHERE d.dict_code = 'bind_scene' 
AND d.del_flag = '0' 
AND di.status = 1
ORDER BY CAST(sort_order AS UNSIGNED);

-- =====================================================
-- 3. 检查"手动调整"枚举值是否已存在
-- =====================================================
SET @existing_count = (
    SELECT COUNT(*)
    FROM sys_dict_item di
    JOIN sys_dict d ON d.id = di.dict_id
    WHERE d.dict_code = 'bind_scene' 
    AND di.item_value = '3'
    AND d.del_flag = '0'
);

-- =====================================================
-- 4. 添加"手动调整"枚举值（如果不存在）
-- =====================================================
SET @dict_id = (
    SELECT id 
    FROM sys_dict 
    WHERE dict_code = 'bind_scene' 
    AND del_flag = '0'
    LIMIT 1
);

-- 生成新的字典项ID
SET @new_item_id = REPLACE(UUID(), '-', '');

-- 条件插入：只有当字典存在且"手动调整"项不存在时才插入
INSERT INTO sys_dict_item (
    id, 
    dict_id, 
    item_text, 
    item_value, 
    description, 
    sort_order, 
    status, 
    create_by, 
    create_time,
    update_by,
    update_time
)
SELECT 
    @new_item_id,
    @dict_id,
    '手动调整',
    '3',
    '管理员手动调整推荐人关系',
    4,
    1,
    'admin',
    NOW(),
    'admin',
    NOW()
WHERE @dict_id IS NOT NULL 
AND @existing_count = 0;

-- =====================================================
-- 5. 验证添加结果
-- =====================================================
SELECT 
    '=== 添加结果验证 ===' as verification_step,
    '' as result_info
UNION ALL
SELECT 
    '操作结果',
    CASE 
        WHEN @dict_id IS NULL THEN '❌ bind_scene字典不存在'
        WHEN @existing_count > 0 THEN '⚠️ 手动调整枚举值已存在，跳过添加'
        ELSE '✅ 手动调整枚举值添加成功'
    END as result_info;

-- =====================================================
-- 6. 显示更新后的完整字典项列表
-- =====================================================
SELECT 
    '=== 更新后的bind_scene字典项列表 ===' as verification_step,
    '' as item_text,
    '' as item_value,
    '' as description,
    '' as sort_order,
    '' as status
UNION ALL
SELECT 
    '字典项',
    di.item_text,
    di.item_value,
    di.description,
    CAST(di.sort_order AS CHAR),
    CASE di.status WHEN 1 THEN '启用' ELSE '禁用' END
FROM sys_dict_item di
JOIN sys_dict d ON d.id = di.dict_id
WHERE d.dict_code = 'bind_scene' 
AND d.del_flag = '0'
ORDER BY di.sort_order;

-- =====================================================
-- 7. 验证字典在代码中的使用
-- =====================================================
SELECT 
    '=== 使用说明 ===' as info_type,
    '' as info_content
UNION ALL
SELECT 
    '实体类注解',
    '@Dict(dicCode = "bind_scene")'
UNION ALL
SELECT 
    'Excel注解',
    '@Excel(name = "绑定关系场景", width = 15, dicCode = "bind_scene")'
UNION ALL
SELECT 
    '新增枚举值',
    '3 - 手动调整（管理员手动调整推荐人关系）'
UNION ALL
SELECT 
    '使用场景',
    '在修改推荐人功能中设置 memberList.setBindScene("3")';

-- 提交事务
COMMIT;

-- =====================================================
-- 8. 回滚脚本（如需回滚，请手动执行以下语句）
-- =====================================================
/*
-- 回滚脚本（谨慎使用）
START TRANSACTION;

-- 删除手动调整枚举值
DELETE FROM sys_dict_item 
WHERE dict_id = (
    SELECT id FROM sys_dict WHERE dict_code = 'bind_scene'
) 
AND item_value = '3' 
AND item_text = '手动调整';

-- 验证回滚结果
SELECT 
    '回滚验证' as verification_step,
    COUNT(*) as remaining_count
FROM sys_dict_item di
JOIN sys_dict d ON d.id = di.dict_id
WHERE d.dict_code = 'bind_scene' 
AND di.item_value = '3';

COMMIT;
*/

-- =====================================================
-- 9. 使用示例
-- =====================================================
/*
-- 在Java代码中使用示例：
// 设置绑定场景为手动调整
memberList.setBindScene("3");

// 查询手动调整的会员
LambdaQueryWrapper<MemberList> queryWrapper = new LambdaQueryWrapper<MemberList>()
    .eq(MemberList::getBindScene, "3");
*/
