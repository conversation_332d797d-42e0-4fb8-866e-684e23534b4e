-- =====================================================
-- 为TaskAcceptanceRecord表添加实际截止时间字段
-- 功能：存储根据接受时间计算的实际截止时间
-- 版本：v1.2
-- 创建时间：2025-01-05
-- =====================================================

START TRANSACTION;

-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'task_acceptance_record' 
    AND column_name = 'actual_deadline'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE task_acceptance_record ADD COLUMN actual_deadline DATETIME NULL COMMENT ''实际截止时间（根据接受时间和任务类型计算）''',
    'SELECT ''actual_deadline字段已存在，跳过添加'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建索引（提升查询性能）
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'task_acceptance_record' 
    AND index_name = 'idx_acceptance_actual_deadline'
);

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_acceptance_actual_deadline ON task_acceptance_record(actual_deadline)',
    'SELECT ''idx_acceptance_actual_deadline索引已存在，跳过创建'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证结果
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'task_acceptance_record' 
AND column_name = 'actual_deadline';

COMMIT;

SELECT '实际截止时间字段已添加到task_acceptance_record表' as message;
