-- =====================================================
-- 任务接受记录状态字典扩展 - 添加"已取消"状态
-- 功能：为参与中任务取消功能添加新的状态枚举
-- 作者：heartful-mall AI Assistant
-- 日期：2025-01-06
-- 版本：V1.0
-- =====================================================

-- 检查字典是否存在
SELECT 
    '开始扩展acceptance_status字典' as message,
    COUNT(*) as existing_dict_count
FROM sys_dict 
WHERE dict_code = 'acceptance_status';

-- 获取字典ID
SET @dict_id = (SELECT id FROM sys_dict WHERE dict_code = 'acceptance_status' LIMIT 1);

-- 检查"已取消"状态是否已存在
SELECT 
    '检查已取消状态是否存在' as check_type,
    COUNT(*) as existing_count,
    CASE 
        WHEN COUNT(*) > 0 THEN '⚠️ 已存在，跳过创建'
        ELSE '✅ 不存在，可以创建'
    END as status
FROM sys_dict_item 
WHERE dict_id = @dict_id AND item_value = '6';

-- 添加"已取消"状态（仅在不存在时添加）
INSERT INTO sys_dict_item (
    id, 
    dict_id, 
    item_text, 
    item_value, 
    description, 
    sort_order, 
    status, 
    create_time,
    create_by
) 
SELECT 
    REPLACE(UUID(), '-', ''),
    @dict_id,
    '已取消',
    '6',
    '用户主动取消任务接受',
    60,
    1,
    NOW(),
    'system_task_cancel'
WHERE NOT EXISTS (
    SELECT 1 FROM sys_dict_item 
    WHERE dict_id = @dict_id AND item_value = '6'
);

-- 验证添加结果
SELECT 
    '字典扩展完成验证' as verification_title,
    '' as detail
UNION ALL
SELECT 
    '字典编码', 
    d.dict_code
FROM sys_dict d
WHERE d.dict_code = 'acceptance_status'
UNION ALL
SELECT 
    '字典名称', 
    d.dict_name
FROM sys_dict d
WHERE d.dict_code = 'acceptance_status'
UNION ALL
SELECT 
    '状态项总数', 
    CAST(COUNT(*) AS CHAR)
FROM sys_dict_item di
JOIN sys_dict d ON d.id = di.dict_id
WHERE d.dict_code = 'acceptance_status';

-- 显示所有状态项
SELECT 
    '📋 acceptance_status字典完整状态列表' as title,
    '' as item_text,
    '' as item_value,
    '' as description,
    '' as sort_order
UNION ALL
SELECT 
    '状态项',
    di.item_text,
    di.item_value,
    di.description,
    CAST(di.sort_order AS CHAR)
FROM sys_dict_item di
JOIN sys_dict d ON d.id = di.dict_id
WHERE d.dict_code = 'acceptance_status'
ORDER BY CAST(sort_order AS UNSIGNED);

-- 特别验证新增的"已取消"状态
SELECT 
    '🎯 新增状态验证' as verification_type,
    CASE 
        WHEN COUNT(*) = 1 THEN '✅ 已取消状态添加成功'
        WHEN COUNT(*) = 0 THEN '❌ 已取消状态添加失败'
        ELSE '⚠️ 存在重复的已取消状态'
    END as result,
    COUNT(*) as count
FROM sys_dict_item di
JOIN sys_dict d ON d.id = di.dict_id
WHERE d.dict_code = 'acceptance_status' 
AND di.item_value = '6' 
AND di.item_text = '已取消';

-- =====================================================
-- 扩展说明
-- =====================================================

/*
acceptance_status字典状态说明：

现有状态：
1 - 进行中：已接受但未提交审核
2 - 待审核：已提交等待审核  
3 - 已完成：审核通过并获得奖励
4 - 未通过：审核未通过
5 - 已过期：超过截止时间

新增状态：
6 - 已取消：用户主动取消任务接受

业务规则：
- 只有状态为"1-进行中"的任务可以取消
- 取消前需要检查是否已过期（基于actualDeadline）
- 取消后需要将任务额度回退到task_publish表
- 取消操作不可逆转

技术实现：
- 前端：MyAcceptedTasks.vue添加取消按钮和交互逻辑
- 后端：TaskAcceptanceRecordController添加cancel接口
- 业务：TaskAcceptanceRecordService实现取消业务逻辑
*/

-- 完成提示
SELECT '🎉 acceptance_status字典扩展完成！' as completion_message;
