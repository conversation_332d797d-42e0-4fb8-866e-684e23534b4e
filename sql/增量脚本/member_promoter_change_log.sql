-- =====================================================
-- 会员推荐人修改记录表
-- 功能：记录管理员手动修改推荐人的详细信息，包含修改原因
-- 作者：heartful-mall
-- 日期：2025-01-07
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 创建会员推荐人修改记录表
-- =====================================================
CREATE TABLE IF NOT EXISTS `member_promoter_change_log` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `member_id` varchar(50) NOT NULL COMMENT '会员ID',
  `member_nick_name` varchar(100) DEFAULT NULL COMMENT '会员昵称（冗余字段）',
  `member_phone` varchar(20) DEFAULT NULL COMMENT '会员手机号（冗余字段）',
  `original_promoter_id` varchar(50) DEFAULT NULL COMMENT '原推荐人ID',
  `original_promoter_name` varchar(100) DEFAULT NULL COMMENT '原推荐人昵称（冗余字段）',
  `original_promoter_type` varchar(2) DEFAULT NULL COMMENT '原推荐人类型：0店铺，1会员，2平台',
  `new_promoter_id` varchar(50) NOT NULL COMMENT '新推荐人ID',
  `new_promoter_name` varchar(100) DEFAULT NULL COMMENT '新推荐人昵称（冗余字段）',
  `new_promoter_type` varchar(2) DEFAULT '1' COMMENT '新推荐人类型：0店铺，1会员，2平台',
  `change_reason` text NOT NULL COMMENT '修改原因',
  `operator_id` varchar(50) DEFAULT NULL COMMENT '操作员ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作员姓名',
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态（0正常，1已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_change_time` (`change_time`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员推荐人修改记录表';

-- =====================================================
-- 2. 验证表创建结果
-- =====================================================
SELECT 
    '=== 表创建验证 ===' as verification_step,
    '' as table_info
UNION ALL
SELECT 
    '表基本信息',
    CONCAT('表名: ', TABLE_NAME, ', 引擎: ', ENGINE, ', 字符集: ', TABLE_COLLATION)
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'member_promoter_change_log';

-- =====================================================
-- 3. 验证字段结构
-- =====================================================
SELECT 
    '=== 字段结构验证 ===' as verification_step,
    '' as column_name,
    '' as data_type,
    '' as is_nullable,
    '' as column_comment
UNION ALL
SELECT 
    '字段信息',
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'member_promoter_change_log'
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- 4. 验证索引创建
-- =====================================================
SELECT 
    '=== 索引验证 ===' as verification_step,
    '' as index_name,
    '' as column_name,
    '' as non_unique
UNION ALL
SELECT 
    '索引信息',
    INDEX_NAME,
    COLUMN_NAME,
    CASE NON_UNIQUE WHEN 0 THEN '唯一索引' ELSE '普通索引' END
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'member_promoter_change_log'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- =====================================================
-- 5. 插入测试数据（可选）
-- =====================================================
/*
-- 测试数据示例
INSERT INTO member_promoter_change_log (
    id, 
    member_id, 
    member_nick_name, 
    member_phone,
    original_promoter_id,
    original_promoter_name,
    original_promoter_type,
    new_promoter_id,
    new_promoter_name,
    new_promoter_type,
    change_reason,
    operator_id,
    operator_name,
    change_time,
    create_by
) VALUES (
    REPLACE(UUID(), '-', ''),
    'test_member_id',
    '测试会员',
    '13800138000',
    'old_promoter_id',
    '原推荐人',
    '1',
    'new_promoter_id', 
    '新推荐人',
    '1',
    '业务调整需要修改推荐人关系',
    'admin',
    '系统管理员',
    NOW(),
    'admin'
);
*/

-- =====================================================
-- 6. 使用说明
-- =====================================================
SELECT 
    '=== 使用说明 ===' as usage_type,
    '' as usage_content
UNION ALL
SELECT 
    '记录修改',
    '每次手动修改推荐人时，在member_promoter_change_log表中插入一条记录'
UNION ALL
SELECT 
    '查询历史',
    'SELECT * FROM member_promoter_change_log WHERE member_id = ? ORDER BY change_time DESC'
UNION ALL
SELECT 
    '统计分析',
    '可按操作员、时间范围等维度统计推荐人修改情况'
UNION ALL
SELECT 
    '审计追溯',
    '所有修改操作可追溯，包含修改原因、操作员、时间等完整信息';

-- 提交事务
COMMIT;

-- =====================================================
-- 7. 回滚脚本（如需回滚，请手动执行以下语句）
-- =====================================================
/*
-- 回滚脚本（谨慎使用）
START TRANSACTION;

-- 删除表
DROP TABLE IF EXISTS member_promoter_change_log;

-- 验证回滚结果
SELECT 
    '回滚验证' as verification_step,
    COUNT(*) as table_count
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'member_promoter_change_log';

COMMIT;
*/

-- =====================================================
-- 8. 相关查询示例
-- =====================================================
/*
-- 查询指定会员的推荐人修改历史
SELECT 
    member_nick_name,
    member_phone,
    original_promoter_name,
    new_promoter_name,
    change_reason,
    operator_name,
    change_time
FROM member_promoter_change_log 
WHERE member_id = '指定会员ID' 
AND del_flag = '0'
ORDER BY change_time DESC;

-- 查询指定时间范围内的修改记录
SELECT 
    member_nick_name,
    original_promoter_name,
    new_promoter_name,
    change_reason,
    operator_name,
    change_time
FROM member_promoter_change_log 
WHERE change_time BETWEEN '2025-01-01' AND '2025-01-31'
AND del_flag = '0'
ORDER BY change_time DESC;

-- 统计各操作员的修改次数
SELECT 
    operator_name,
    COUNT(*) as change_count,
    MIN(change_time) as first_change,
    MAX(change_time) as last_change
FROM member_promoter_change_log 
WHERE del_flag = '0'
GROUP BY operator_id, operator_name
ORDER BY change_count DESC;
*/
