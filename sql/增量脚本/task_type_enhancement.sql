-- =====================================================
-- heartful-mall 任务发布功能增强 - 数据库变更脚本
-- 功能：新增任务类型分类和动态截止时间逻辑
-- 版本：v1.0
-- 创建时间：2025-01-05
-- 说明：渐进式增强，确保向后兼容性
-- =====================================================

-- 检查表是否存在
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'task_publish';

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 新增任务类型字段
-- =====================================================
-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'task_publish' 
    AND column_name = 'task_type'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE task_publish ADD COLUMN task_type VARCHAR(10) DEFAULT ''1'' COMMENT ''任务类型：1-短期任务，2-长期任务''',
    'SELECT ''task_type字段已存在，跳过添加'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. 新增完成天数字段
-- =====================================================
-- 检查字段是否已存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'task_publish' 
    AND column_name = 'days_to_complete'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE task_publish ADD COLUMN days_to_complete INT NULL COMMENT ''几天内完成（仅长期任务）''',
    'SELECT ''days_to_complete字段已存在，跳过添加'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. 创建索引（提升查询性能）
-- =====================================================
-- 检查索引是否已存在
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'task_publish' 
    AND index_name = 'idx_task_publish_task_type'
);

-- 如果索引不存在则创建
SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_task_publish_task_type ON task_publish(task_type)',
    'SELECT ''idx_task_publish_task_type索引已存在，跳过创建'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. 验证字段添加结果
-- =====================================================
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'task_publish' 
AND column_name IN ('task_type', 'days_to_complete')
ORDER BY ordinal_position;

-- =====================================================
-- 5. 验证索引创建结果
-- =====================================================
SELECT 
    index_name,
    column_name,
    seq_in_index
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'task_publish' 
AND index_name = 'idx_task_publish_task_type';

-- =====================================================
-- 6. 统计现有数据情况
-- =====================================================
SELECT 
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN task_type IS NOT NULL THEN 1 END) as tasks_with_type,
    COUNT(CASE WHEN task_type IS NULL THEN 1 END) as tasks_without_type
FROM task_publish;

-- 提交事务
COMMIT;

-- =====================================================
-- 7. 回滚脚本（如需回滚，请手动执行以下语句）
-- =====================================================
/*
-- 回滚脚本（谨慎使用）
START TRANSACTION;

-- 删除索引
DROP INDEX IF EXISTS idx_task_publish_task_type ON task_publish;

-- 删除字段（注意：会丢失数据）
ALTER TABLE task_publish DROP COLUMN IF EXISTS days_to_complete;
ALTER TABLE task_publish DROP COLUMN IF EXISTS task_type;

COMMIT;
*/

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 
    '数据库变更脚本执行完成' as status,
    NOW() as completion_time,
    'task_type和days_to_complete字段已成功添加到task_publish表' as message;
