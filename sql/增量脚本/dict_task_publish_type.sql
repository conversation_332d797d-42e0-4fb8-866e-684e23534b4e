-- =====================================================
-- heartful-mall 任务发布类型字典配置脚本
-- 功能：创建任务发布类型字典和字典项
-- 版本：v1.0
-- 创建时间：2025-01-06
-- 说明：使用动态UUID生成，确保可移植性
-- 字典编码：task_publish_type（避免与营销模块task_type冲突）
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 检查并创建任务发布类型字典
-- =====================================================
-- 检查字典是否已存在
SET @dict_exists = (
    SELECT COUNT(*) 
    FROM sys_dict 
    WHERE dict_code = 'task_publish_type' AND del_flag = 0
);

-- 如果字典不存在则创建
SET @dict_id = REPLACE(UUID(), '-', '');

SET @sql = IF(@dict_exists = 0, 
    CONCAT('INSERT INTO sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, type) VALUES (''', 
           @dict_id, ''', ''任务发布类型'', ''task_publish_type'', ''任务发布类型字典：短期任务和长期任务'', 0, ''admin'', NOW(), 0)'),
    'SELECT ''task_publish_type字典已存在，跳过创建'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 获取字典ID（无论是新创建的还是已存在的）
SET @dict_id = (
    SELECT id 
    FROM sys_dict 
    WHERE dict_code = 'task_publish_type' AND del_flag = 0 
    LIMIT 1
);

-- =====================================================
-- 2. 检查并创建字典项：短期任务
-- =====================================================
-- 检查短期任务字典项是否已存在
SET @item1_exists = (
    SELECT COUNT(*) 
    FROM sys_dict_item 
    WHERE dict_id = @dict_id AND item_value = '1' AND status = 1
);

-- 如果字典项不存在则创建
SET @item1_id = REPLACE(UUID(), '-', '');

SET @sql = IF(@item1_exists = 0, 
    CONCAT('INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time) VALUES (''', 
           @item1_id, ''', ''', @dict_id, ''', ''短期任务'', ''1'', ''短期任务：直接设置最终截止时间'', 1, 1, ''admin'', NOW())'),
    'SELECT ''短期任务字典项已存在，跳过创建'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. 检查并创建字典项：长期任务
-- =====================================================
-- 检查长期任务字典项是否已存在
SET @item2_exists = (
    SELECT COUNT(*) 
    FROM sys_dict_item 
    WHERE dict_id = @dict_id AND item_value = '2' AND status = 1
);

-- 如果字典项不存在则创建
SET @item2_id = REPLACE(UUID(), '-', '');

SET @sql = IF(@item2_exists = 0, 
    CONCAT('INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time) VALUES (''', 
           @item2_id, ''', ''', @dict_id, ''', ''长期任务'', ''2'', ''长期任务：可设置完成期限，系统取较早时间'', 2, 1, ''admin'', NOW())'),
    'SELECT ''长期任务字典项已存在，跳过创建'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. 验证字典配置结果
-- =====================================================
-- 查询字典主表
SELECT 
    id,
    dict_name,
    dict_code,
    description,
    create_time
FROM sys_dict 
WHERE dict_code = 'task_publish_type' AND del_flag = 0;

-- 查询字典项
SELECT 
    sdi.id,
    sdi.item_text,
    sdi.item_value,
    sdi.description,
    sdi.sort_order,
    sdi.status,
    sdi.create_time
FROM sys_dict_item sdi
INNER JOIN sys_dict sd ON sdi.dict_id = sd.id
WHERE sd.dict_code = 'task_publish_type' AND sd.del_flag = 0 AND sdi.status = 1
ORDER BY sdi.sort_order;

-- =====================================================
-- 5. 测试字典查询功能
-- =====================================================
-- 模拟前端字典查询
SELECT 
    sdi.item_text as label,
    sdi.item_value as value,
    sdi.description
FROM sys_dict_item sdi
INNER JOIN sys_dict sd ON sdi.dict_id = sd.id
WHERE sd.dict_code = 'task_publish_type' 
AND sd.del_flag = 0 
AND sdi.status = 1
ORDER BY sdi.sort_order;

-- 提交事务
COMMIT;

-- =====================================================
-- 6. 回滚脚本（如需回滚，请手动执行以下语句）
-- =====================================================
/*
-- 回滚脚本（谨慎使用）
START TRANSACTION;

-- 删除字典项
DELETE FROM sys_dict_item 
WHERE dict_id IN (
    SELECT id FROM sys_dict WHERE dict_code = 'task_publish_type'
);

-- 删除字典
DELETE FROM sys_dict WHERE dict_code = 'task_publish_type';

COMMIT;
*/

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 
    '任务发布类型字典配置完成' as status,
    NOW() as completion_time,
    'task_publish_type字典及相关字典项已成功创建' as message,
    @dict_id as dict_id;
