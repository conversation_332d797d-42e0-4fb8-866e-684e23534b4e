-- =====================================================
-- 会员分销设置列表修改推荐人功能性能优化
-- 功能：为查询助梦家列表和推荐人关系查询添加复合索引
-- 作者：heartful-mall
-- 日期：2025-01-07
-- =====================================================

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 检查现有索引情况
-- =====================================================
SELECT 
    '=== 现有相关索引检查 ===' as check_step,
    '' as index_name,
    '' as column_name,
    '' as index_type
UNION ALL
SELECT 
    '现有索引',
    Key_name,
    Column_name,
    Index_type
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'heartful-mall' 
AND TABLE_NAME = 'member_list'
AND Column_name IN ('is_love_ambassador', 'del_flag', 'nick_name', 'phone', 'promoter', 'promoter_type')
ORDER BY Key_name, Seq_in_index;

-- =====================================================
-- 2. 创建助梦家查询优化索引
-- =====================================================
-- 为查询助梦家列表创建复合索引
-- 查询条件：is_love_ambassador = '1' AND del_flag = '0' ORDER BY create_time DESC
-- 搜索条件：nick_name LIKE '%keyword%' OR phone LIKE '%keyword%'

-- 检查索引是否已存在
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'heartful-mall' 
    AND TABLE_NAME = 'member_list'
    AND INDEX_NAME = 'idx_love_ambassador_del_flag_create_time'
);

-- 创建助梦家查询复合索引（如果不存在）
SET @sql = CASE 
    WHEN @index_exists = 0 THEN 
        'CREATE INDEX idx_love_ambassador_del_flag_create_time ON member_list (is_love_ambassador, del_flag, create_time DESC)'
    ELSE 
        'SELECT "索引 idx_love_ambassador_del_flag_create_time 已存在" as result'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. 创建推荐人关系查询优化索引
-- =====================================================
-- 为推荐人关系查询创建复合索引
-- 查询条件：promoter = ? AND promoter_type = '1' AND del_flag = '0'

-- 检查索引是否已存在
SET @index_exists2 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'heartful-mall' 
    AND TABLE_NAME = 'member_list'
    AND INDEX_NAME = 'idx_promoter_type_del_flag'
);

-- 创建推荐人关系查询复合索引（如果不存在）
SET @sql2 = CASE 
    WHEN @index_exists2 = 0 THEN 
        'CREATE INDEX idx_promoter_type_del_flag ON member_list (promoter, promoter_type, del_flag)'
    ELSE 
        'SELECT "索引 idx_promoter_type_del_flag 已存在" as result'
END;

PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- =====================================================
-- 4. 创建会员路径查询优化索引
-- =====================================================
-- 为会员路径查询创建索引
-- 查询条件：member_path LIKE '%uniqueId%' AND del_flag = '0'

-- 检查索引是否已存在
SET @index_exists3 = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'heartful-mall' 
    AND TABLE_NAME = 'member_list'
    AND INDEX_NAME = 'idx_member_path_del_flag'
);

-- 创建会员路径查询复合索引（如果不存在）
SET @sql3 = CASE 
    WHEN @index_exists3 = 0 THEN 
        'CREATE INDEX idx_member_path_del_flag ON member_list (member_path, del_flag)'
    ELSE 
        'SELECT "索引 idx_member_path_del_flag 已存在" as result'
END;

PREPARE stmt3 FROM @sql3;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

-- =====================================================
-- 5. 验证索引创建结果
-- =====================================================
SELECT 
    '=== 索引创建结果验证 ===' as verification_step,
    '' as index_name,
    '' as columns,
    '' as status
UNION ALL
SELECT 
    '新建索引',
    INDEX_NAME,
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX),
    '已创建'
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'heartful-mall' 
AND TABLE_NAME = 'member_list'
AND INDEX_NAME IN (
    'idx_love_ambassador_del_flag_create_time',
    'idx_promoter_type_del_flag',
    'idx_member_path_del_flag'
)
GROUP BY INDEX_NAME;

-- =====================================================
-- 6. 性能测试查询
-- =====================================================
-- 测试助梦家查询性能
EXPLAIN SELECT id, nick_name, phone, is_love_ambassador, member_level 
FROM member_list 
WHERE is_love_ambassador = '1' 
AND del_flag = '0' 
ORDER BY create_time DESC 
LIMIT 10;

-- 测试推荐人关系查询性能
EXPLAIN SELECT COUNT(*) 
FROM member_list 
WHERE promoter = 'b609e7e5d4b6e9833158555783497319' 
AND promoter_type = '1' 
AND del_flag = '0';

-- =====================================================
-- 7. 使用建议
-- =====================================================
SELECT 
    '=== 性能优化使用建议 ===' as suggestion_type,
    '' as suggestion_content
UNION ALL
SELECT 
    '查询助梦家',
    '使用 is_love_ambassador + del_flag + create_time 复合索引，避免文件排序'
UNION ALL
SELECT 
    '统计下级会员',
    '使用 promoter + promoter_type + del_flag 复合索引，提高统计查询效率'
UNION ALL
SELECT 
    '会员路径查询',
    '使用 member_path + del_flag 复合索引，但LIKE查询仍需注意性能'
UNION ALL
SELECT 
    '搜索优化',
    '昵称和手机号搜索建议使用全文索引或搜索引擎（如ES）'
UNION ALL
SELECT 
    '分页优化',
    '大数据量分页建议使用游标分页而非OFFSET分页';

-- 提交事务
COMMIT;

-- =====================================================
-- 8. 回滚脚本（如需回滚，请手动执行以下语句）
-- =====================================================
/*
-- 回滚脚本（谨慎使用）
START TRANSACTION;

-- 删除创建的索引
DROP INDEX IF EXISTS idx_love_ambassador_del_flag_create_time ON member_list;
DROP INDEX IF EXISTS idx_promoter_type_del_flag ON member_list;
DROP INDEX IF EXISTS idx_member_path_del_flag ON member_list;

-- 验证回滚结果
SELECT 
    '回滚验证' as verification_step,
    COUNT(*) as remaining_index_count
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'heartful-mall' 
AND TABLE_NAME = 'member_list'
AND INDEX_NAME IN (
    'idx_love_ambassador_del_flag_create_time',
    'idx_promoter_type_del_flag',
    'idx_member_path_del_flag'
);

COMMIT;
*/

-- =====================================================
-- 9. 监控查询
-- =====================================================
/*
-- 性能监控查询（定期执行）

-- 1. 检查慢查询
SELECT 
    sql_text,
    exec_count,
    avg_timer_wait/1000000000 as avg_time_seconds,
    sum_timer_wait/1000000000 as total_time_seconds
FROM performance_schema.events_statements_summary_by_digest 
WHERE sql_text LIKE '%member_list%'
AND sql_text LIKE '%is_love_ambassador%'
ORDER BY avg_timer_wait DESC
LIMIT 10;

-- 2. 检查索引使用情况
SELECT 
    object_name,
    index_name,
    count_read,
    count_write,
    count_read/count_write as read_write_ratio
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE object_schema = 'heartful-mall'
AND object_name = 'member_list'
AND index_name IN (
    'idx_love_ambassador_del_flag_create_time',
    'idx_promoter_type_del_flag',
    'idx_member_path_del_flag'
)
ORDER BY count_read DESC;
*/
