package org.jeecg.modules.task.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;

/**
 * @Description: 待审核任务VO - 包含任务接受记录和关联的任务发布信息
 * @Author: jeecg-boot
 * @Date: 2025-01-04
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="PendingAuditTaskVO对象", description="待审核任务VO - 包含任务接受记录和关联的任务发布信息")
public class PendingAuditTaskVO extends TaskAcceptanceRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    // ==================== 关联的TaskPublish字段 ====================
    
    /**任务标题*/
    @Excel(name = "任务标题", width = 20)
    @ApiModelProperty(value = "任务标题")
    private String taskTitle;

    /**任务描述*/
    @Excel(name = "任务描述", width = 30)
    @ApiModelProperty(value = "任务描述")
    private String taskDescription;

    /**任务要求和完成标准*/
    @Excel(name = "任务要求", width = 30)
    @ApiModelProperty(value = "任务要求和完成标准")
    private String taskRequirements;

    /**任务总数量*/
    @Excel(name = "任务总数量", width = 15)
    @ApiModelProperty(value = "任务总数量")
    private Integer taskTotalCount;

    /**剩余可接数量*/
    @Excel(name = "剩余数量", width = 15)
    @ApiModelProperty(value = "剩余可接数量")
    private Integer taskRemainingCount;

    /**截止时间*/
    @Excel(name = "截止时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "截止时间")
    private Date taskDeadline;

    /**发布者类型（字典：publisher_type）1-普通用户 2-平台管理员*/
    @Excel(name = "发布者类型", width = 15, dicCode = "publisher_type")
    @Dict(dicCode = "publisher_type")
    @ApiModelProperty(value = "发布者类型")
    private String taskPublisherType;

    /**是否官方任务（字典：yn）0-否 1-是*/
    @Excel(name = "是否官方任务", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否官方任务")
    private Integer taskIsOfficial;

    /**任务状态（字典：task_status）1-进行中 2-已满额 3-已完成 4-已过期 5-已下架*/
    @Excel(name = "任务状态", width = 15, dicCode = "task_status")
    @Dict(dicCode = "task_status")
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;

    /**优先级（字典：task_priority）0-普通 1-高 2-紧急*/
    @Excel(name = "优先级", width = 15, dicCode = "task_priority")
    @Dict(dicCode = "task_priority")
    @ApiModelProperty(value = "优先级")
    private Integer taskPriority;

    /**任务创建时间*/
    @Excel(name = "任务创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "任务创建时间")
    private Date taskCreateTime;

    // ==================== 扩展字段 ====================
    
    /**接受者昵称（用于显示）*/
    @ApiModelProperty(value = "接受者昵称")
    private String acceptorNickname;

    /**接受者头像（用于显示）*/
    @ApiModelProperty(value = "接受者头像")
    private String acceptorAvatar;

    /**提交材料图片数量*/
    @ApiModelProperty(value = "提交材料图片数量")
    private Integer submitImageCount;

    /**是否逾期提交*/
    @ApiModelProperty(value = "是否逾期提交")
    private Boolean isOverdue;

    /**距离截止时间（小时）*/
    @ApiModelProperty(value = "距离截止时间（小时）")
    private Long hoursToDeadline;
}
