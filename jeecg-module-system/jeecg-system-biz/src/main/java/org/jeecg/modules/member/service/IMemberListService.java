package org.jeecg.modules.member.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.agency.vo.AgencyWorkbenchVO;
import org.jeecg.modules.alliance.vo.AllianceWorkbenchVO;
import org.jeecg.modules.marketing.vo.MarketingDiscountCouponVO;
import org.jeecg.modules.member.dto.MemberListDTO;
import org.jeecg.modules.member.entity.MemberList;
import org.jeecg.modules.member.vo.MemberCertificateVO;
import org.jeecg.modules.member.vo.MemberDiscountVO;
import org.jeecg.modules.member.vo.MemberListVO;
import org.jeecg.modules.store.vo.StoreManageVO;
import org.jeecg.modules.system.vo.SysWorkbenchVO;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestHeader;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 会员列表
 * @Author: jeecg-boot
 * @Date:   2019-10-24
 * @Version: V1.0
 */
public interface IMemberListService extends IService<MemberList> {

    /**
     * 用户端获取用户信息接口
     * @param sysUserId
     * @param memberId
     * @param softModel
     * @return
     */
     Map<String, Object> getMemberInfo(String sysUserId, String memberId, String softModel);

    public List<MemberList> selectMemberListById(String memberListId);

    IPage<MemberListVO> findMemberList(Page<MemberListVO> page, MemberListVO memberListVO);

// 根据会员折扣信息查询会员折扣优惠券
    IPage<MarketingDiscountCouponVO> findMemberDiscount(Page<MarketingDiscountCouponVO> page,MemberDiscountVO memberDiscountVO);

    IPage<MemberCertificateVO> findMemberCertificate(Page<MemberCertificateVO> page, MemberCertificateVO memberCertificateVO);


    /**
     * 查询用户团队数量
     *
     * @param memberId
     * @return
     */
    public MemberListVO findMemberDistributionCount(String memberId);

    /**
     * 查询用户的级别按照时间排序
     *
     * @param page
     * @param memberId
     * @return
     */
    public IPage<Map<String,Object>> findMemberLevelList(Page<Map<String,Object>> page, String memberId);

    MemberListDTO getStoreSexSum(SysWorkbenchVO sysWorkbenchVO);

    AgencyWorkbenchVO getAgencySexSum(AgencyWorkbenchVO agencyWorkbenchVO);

    Map<String,Long> getSysSexSum(SysWorkbenchVO sysWorkbenchVO);

    IPage<MemberListVO> findAgencyMemberList(Page<MemberListVO> page, MemberListVO memberListVO);

    Map<String,Object> returnPromoter(String id);

    Map<String,Object> returnMemberNameById(String promoter, String promoterType);
    /**
     * 获取用户管理会员数据
     * @param page
     * @param sysUserId
     * @param searchNickNamePhone
     * @return
     */
    IPage<Map<String,Object>> getMyMemberList(Page<MemberList> page, String sysUserId, String searchNickNamePhone);

    AllianceWorkbenchVO getFranchiseeSexSum(AllianceWorkbenchVO workbench);

    IPage<MemberListVO> findAllianceMemberlist(Page<StoreManageVO> page, MemberListDTO memberListDTO);

    List<Map<String,Object>> likeMemberByPhone(String phone);
    /**
     * 查询会员信息(包含删除状态)
     * @param memberListId
     * @return
     */
    MemberList  getMemberListById(String memberListId);

    MemberListVO findMemberDistributionCountByMemberType(String id);

    Long findMemberVipByMarketingGiftBag(String id);

    MemberListVO findMemberVipByMarketingGiftBagCount(String id);

    Long findMemberVipByMarketingGiftBagAndStraightPushId(String straightPushId, String id);


    IPage<Map<String,Object>> getmemberListByTManageId(Page<Map<String,Object>> page, String id,String memberDesignationGroupId);

    //boolean designateMember(MemberList levelDownMember, String designateMemberId, String memberId);

    //void totalMembersSub(MemberList memberList,MemberList levelDownMember);

    //void totalMembersAdd(MemberList memberList);

    //void memberListSetTManageId(MemberList memberList, String tMemberId);

    IPage<MemberListVO> memberDesignationPageList(Page<MemberListVO> page, MemberListDTO memberListDTO);

    List<MemberListVO> getUnderlingList(String id);

    void setPromoter(MemberList memberList,String tMemberId);
    void setPromoter(MemberList memberList,String tMemberId,String bindScene);

// 根据电话号码获取指定成员列表
    List<Map<String,Object>> getDesignateMemberListByPhone(String phone);

    List<MemberList> getMemberDesignationListById(String marketingGiftBagId, String memberDesignationId);

    /**
     * 这是注册登录的时候分享关系和称号团队的设置
     * @param memberList
     */
    public void setLoginRegister(MemberList memberList,String sysUserId,String tMemberId);

    public void updatePromoter(MemberList memberList);

    /**
     * 这是注册登录的时候分享关系的设置
     * @param memberList
     * @param tMemberId
     */
    public void setLoginRegister(MemberList memberList,String tMemberId);

    /**
     * 会员添加分享二维码
     * @param memberList
     */
    public void addShareQr(MemberList memberList,String sysUserId);

    /**
     * 增加会员金额
     *
     * @param memberId 会员ID
     * @param balance 金额
     * @param orderNo 订单号
     * @param payType 支付类型
     * @return
     */
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType);
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource);
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, String remarks);
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource, String remarks);
    
    /**
     * 增加会员金额（店铺补助金专用）
     *
     * @param memberId 会员ID
     * @param balance 金额
     * @param orderNo 订单号
     * @param payType 支付类型
     * @param remarks 备注
     * @param expireTime 补助金过期时间
     * @param subsidyStoreId 接收补助的店铺ID
     * @return
     */
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, String remarks, java.util.Date expireTime, String subsidyStoreId);

    /**
     * 增加会员金额
     *
     * @param memberId 会员ID
     * @param balance 金额
     * @param orderNo 订单号
     * @param payType 支付类型
     * @return
     */
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType);
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource);
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType, String remarks);
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource, String remarks);


    /**
     * 余额充值成功
     * @param payBalanceLogId
     */
    public void rechargeBalance(String payBalanceLogId);

    /**
     * 查询直推人数
     *
     * @param memberId
     * @return
     */
    IPage<Map<String,Object>> pushingNumber(Page<Map<String,Object>> page,@Param("memberId") String memberId);

    /**
     * 获取间推人数
     *
     * @param memberId
     * @return
     */
    public int betweenPush(String memberId);

    /**
     * 获取会员创业业绩统计
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 业绩统计结果
     */
    Map<String, BigDecimal> getMemberBusinessPerformance(String memberId, String startDate, String endDate);

    /**
     * 获取会员分销佣金统计
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分销佣金和订单数量统计结果
     */
    Map<String, Object> getMemberDistributionCommission(String memberId, String startDate, String endDate);

    /**
     * 获取会员分销订单列表
     * @param page 分页参数
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orderConfirmStatus 订单确认状态 -1：全部分销订单（包含已确认订单、未确认订单，订单状态为1,2,3,5）
     *                          0：已确认订单（订单状态3、5）
     *                          1：未确认订单（订单状态1、2）
     *                          默认为-1，查询全部分销订单
     * @return 分销订单列表分页结果
     */
    IPage<Map<String, Object>> getMemberDistributionOrders(Page<Map<String, Object>> page, String memberId, String startDate, String endDate, String orderConfirmStatus);

    /**
     * 获取会员团队明细列表
     * @param page 分页参数
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 团队明细列表分页结果
     */
    IPage<Map<String, Object>> getTeamDetailList(Page<Map<String, Object>> page, String memberId, String startDate, String endDate);

    /**
     * 获取会员未过期店铺补助金汇总金额
     * @param memberId 会员ID
     * @return 未过期店铺补助金汇总金额
     */
    public BigDecimal getMemberValidStoreSubsidyAmount(String memberId);
    
    /**
     * 获取会员可用店铺补助金汇总金额（状态为可用且未过期的剩余金额）
     * @param memberId 会员ID
     * @return 可用店铺补助金汇总金额
     */
    public BigDecimal getMemberAvailableStoreSubsidyAmount(String memberId);
    
    /**
     * 获取会员已过期店铺补助金汇总金额
     * @param memberId 会员ID
     * @return 已过期店铺补助金汇总金额
     */
    public BigDecimal getMemberExpiredStoreSubsidyAmount(String memberId);
    
    /**
     * 扣除会员店铺专用补助金
     * @param memberId 会员ID
     * @param storeId 店铺ID
     * @param amount 需要扣除的金额
     * @param orderNo 订单号
     * @param payType 支付类型
     * @return 实际扣除的补助金金额
     */
    public BigDecimal subtractStoreSubsidy(String memberId, String storeId, BigDecimal amount, String orderNo, String payType);
    
    /**
     * 获取用户在指定店铺的补助金信息
     * @param memberId 会员ID
     * @param storeId 店铺ID
     * @return 包含总金额和最近过期时间的Map
     */
    public Map<String, Object> getMemberStoreSubsidyInfo(String memberId, String storeId);

    /**
     * 店铺补助金退款恢复
     * 当订单退款时，恢复该订单使用的店铺补助金到原发放记录中
     * 
     * @param memberId 会员ID
     * @param orderNo 订单号
     * @return 恢复的店铺补助金总金额
     */
    BigDecimal restoreStoreSubsidyForRefund(String memberId, String orderNo);
}
