package org.jeecg.modules.taskAcceptanceRecord.vo;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;

/**
 * @Description: 任务接受记录VO - 包含会员信息的任务接受记录
 * @Author: jeecg-boot
 * @Date: 2025-01-12
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TaskAcceptanceRecordVO对象", description="任务接受记录VO - 包含会员信息的任务接受记录")
public class TaskAcceptanceRecordVO extends TaskAcceptanceRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    // ==================== 关联的会员信息字段 ====================
    
    /**接受者昵称（关联member_list.nick_name）*/
    @Excel(name = "接受者昵称", width = 15)
    @ApiModelProperty(value = "接受者昵称")
    private String acceptorNickName;

    /**接受者手机号（关联member_list.phone）*/
    @Excel(name = "接受者手机号", width = 15)
    @ApiModelProperty(value = "接受者手机号")
    private String acceptorPhone;

    /**接受者头像（关联member_list.head_portrait）*/
    @ApiModelProperty(value = "接受者头像")
    private String acceptorHeadPortrait;

    // ==================== 扩展字段（可选） ====================
    
    /**接受者信息显示文本（昵称+手机号格式）*/
    @ApiModelProperty(value = "接受者信息显示文本")
    private String acceptorDisplayText;

    /**是否为VIP会员*/
    @ApiModelProperty(value = "是否为VIP会员")
    private Boolean isVipMember;

    /**会员等级*/
    @ApiModelProperty(value = "会员等级")
    private String memberGrade;

    // ==================== 业务逻辑方法 ====================
    
    /**
     * 获取格式化的接受者信息显示文本
     * 格式：昵称(手机号脱敏)
     * @return 格式化的显示文本
     */
    public String getFormattedAcceptorInfo() {
        if (acceptorNickName == null && acceptorPhone == null) {
            return "未知用户";
        }
        
        if (acceptorNickName != null && acceptorPhone != null) {
            // 手机号脱敏处理：138****1234
            String maskedPhone = maskPhone(acceptorPhone);
            return acceptorNickName + "(" + maskedPhone + ")";
        }
        
        if (acceptorNickName != null) {
            return acceptorNickName;
        }
        
        if (acceptorPhone != null) {
            return maskPhone(acceptorPhone);
        }
        
        return "未知用户";
    }
    
    /**
     * 手机号脱敏处理
     * @param phone 原始手机号
     * @return 脱敏后的手机号
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        
        if (phone.length() == 11) {
            // 标准11位手机号：138****1234
            return phone.substring(0, 3) + "****" + phone.substring(7);
        } else {
            // 其他长度：保留前3位和后2位
            int prefixLen = Math.min(3, phone.length() - 2);
            int suffixLen = Math.min(2, phone.length() - prefixLen);
            return phone.substring(0, prefixLen) + "****" + phone.substring(phone.length() - suffixLen);
        }
    }
}
