package org.jeecg.modules.taskPublish.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.service.ITaskPublishService;
import org.jeecg.modules.taskPublish.vo.TaskPublishVO;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;

/**
 * @Description: 任务发布表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Api(tags="任务发布表")
@RestController
@RequestMapping("/taskPublish/taskPublish")
@Slf4j
public class TaskPublishController {
	@Autowired
	private ITaskPublishService taskPublishService;
	
	/**
	 * 分页列表查询
	 *
	 * @param taskPublish
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务发布表-分页列表查询")
	@ApiOperation(value="任务发布表-分页列表查询", notes="任务发布表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(TaskPublish taskPublish,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TaskPublish> queryWrapper = QueryGenerator.initQueryWrapper(taskPublish, req.getParameterMap());
		Page<TaskPublish> page = new Page<TaskPublish>(pageNo, pageSize);
		IPage<TaskPublish> pageList = taskPublishService.page(page, queryWrapper);

		// 转换为VO并填充完成进度信息和动态截止时间
		List<TaskPublishVO> voList = taskPublishService.convertToVOWithProgress(pageList.getRecords());

		// 构建返回的分页对象
		Page<TaskPublishVO> voPage = new Page<>(pageNo, pageSize, pageList.getTotal());
		voPage.setRecords(voList);

		return Result.OK(voPage);
	}
	
	/**
	 *   添加
	 *
	 * @param taskPublish
	 * @return
	 */
	@AutoLog(value = "任务发布表-添加")
	@ApiOperation(value="任务发布表-添加", notes="任务发布表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody TaskPublish taskPublish) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		taskPublish.setPublisherId(sysUser.getId());
		taskPublish.setPublisherType("2");
		taskPublish.setRemainingCount(taskPublish.getTotalCount());
		taskPublish.setStatus("1");
		taskPublish.setIsOfficial(1);
		taskPublish.setPriority(0);
		BigDecimal totalAmount = calculateTotalReward(taskPublish.getUnitPrice(), taskPublish.getTotalCount());
		taskPublish.setDeductedBalance(totalAmount);
		taskPublish.setPaidBalance(BigDecimal.ZERO);
		taskPublish.setRefundBalance(BigDecimal.ZERO);
		taskPublishService.save(taskPublish);
		return Result.OK("发布任务成功！");
	}

	public BigDecimal calculateTotalReward(BigDecimal unitPrice, Integer count) {
		if (unitPrice == null || count == null || count <= 0) {
			return BigDecimal.ZERO;
		}
		return unitPrice.multiply(new BigDecimal(count));
	}
	
	/**
	 *  编辑
	 *
	 * @param taskPublish
	 * @return
	 */
	@AutoLog(value = "任务发布表-编辑")
	@ApiOperation(value="任务发布表-编辑", notes="任务发布表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody TaskPublish taskPublish) {
		taskPublishService.updateById(taskPublish);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务发布表-通过id删除")
	@ApiOperation(value="任务发布表-通过id删除", notes="任务发布表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		taskPublishService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务发布表-批量删除")
	@ApiOperation(value="任务发布表-批量删除", notes="任务发布表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.taskPublishService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务发布表-通过id查询")
	@ApiOperation(value="任务发布表-通过id查询", notes="任务发布表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		TaskPublish taskPublish = taskPublishService.getById(id);
		if(taskPublish==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(taskPublish);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param taskPublish
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TaskPublish taskPublish) {
        // 过滤选中数据
        String selections = request.getParameter("selections");
        QueryWrapper<TaskPublish> queryWrapper = QueryGenerator.initQueryWrapper(taskPublish, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //Step.1 调用导出方法
        List<TaskPublish> pageList = taskPublishService.list(queryWrapper);
        List<TaskPublish> exportList = null;

        // 过滤选中数据
        if(oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "任务发布表列表");
        mv.addObject(NormalExcelConstants.CLASS, TaskPublish.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("任务发布表数据", "导出人:"+sysUser.getRealname(), "任务发布表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<TaskPublish> listTaskPublishs = ExcelImportUtil.importExcel(file.getInputStream(),TaskPublish.class,params);
                taskPublishService.saveBatch(listTaskPublishs);
                return Result.OK("文件导入成功！数据行数:" + listTaskPublishs.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            }
        }
        return Result.OK("文件导入失败！");
    }

    /**
     * 更新任务状态（上下架）
     *
     * @param id
     * @param status
     * @return
     */
    @AutoLog(value = "任务发布表-更新状态")
    @ApiOperation(value="任务发布表-更新状态", notes="任务发布表-更新状态")
    @PostMapping(value = "/updateStatus")
    public Result<?> updateStatus(@RequestParam(name="id",required=true) String id,
                                  @RequestParam(name="status",required=true) String status) {
        TaskPublish taskPublish = taskPublishService.getById(id);
        if(taskPublish == null) {
            return Result.error("未找到对应的任务");
        }

        taskPublish.setStatus(status);
        taskPublishService.updateById(taskPublish);

        String message = "1".equals(status) ? "上架成功！" : "下架成功！";
        return Result.OK(message);
    }

}
