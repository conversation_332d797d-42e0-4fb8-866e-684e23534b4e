package org.jeecg.modules.taskAcceptanceRecord.controller;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.config.jwt.utils.LoginMemberUtil;
import org.jeecg.modules.taskAcceptanceRecord.service.ITaskAcceptanceRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: C端任务接受记录Controller - 需要登录的接口
 * @Author: AI Assistant
 * @Date: 2025-01-06
 * @Version: V1.0
 */
@Api(tags="C端任务接受记录管理")
@RestController
@RequestMapping("/after/taskAcceptanceRecord")
@Slf4j
public class AfterTaskAcceptanceRecordController {

    @Autowired
    private ITaskAcceptanceRecordService taskAcceptanceRecordService;

    /**
     * 取消任务接受记录
     * 用于C端用户主动取消已接受的任务
     * 
     * 业务规则：
     * 1. 只有"进行中"状态的任务可以取消
     * 2. 任务不能已过期（基于actualDeadline）
     * 3. 取消后额度回退到task_publish表
     * 4. 任务状态更新为"已取消"
     *
     * @param id 任务接受记录ID
     * @return 取消结果
     */
    @AutoLog(value = "任务接受记录-取消")
    @ApiOperation(value="取消任务接受记录", notes="用户主动取消已接受的任务")
    @PostMapping(value = "/cancel/{id}")
    public Result<String> cancel(@PathVariable(name="id") String id) {
        try {
            // 获取当前登录用户ID（遵循C端API规范）
            String memberId = LoginMemberUtil.getLoginMemberId();
            log.info("用户{}尝试取消任务接受记录：{}", memberId, id);
            
            // 调用Service层的取消业务逻辑
            boolean result = taskAcceptanceRecordService.cancelAcceptanceRecord(id, memberId);
            
            if (result) {
                log.info("用户{}成功取消任务接受记录：{}", memberId, id);
                return Result.OK("任务取消成功");
            } else {
                log.warn("用户{}取消任务接受记录失败：{}", memberId, id);
                return Result.error("任务取消失败");
            }
        } catch (IllegalStateException e) {
            // 业务状态异常（如任务已过期、状态不允许取消等）
            log.warn("任务取消失败，业务状态异常：{}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (SecurityException e) {
            // 权限异常（如用户试图取消不属于自己的任务）
            log.warn("任务取消失败，权限异常：{}", e.getMessage());
            return Result.error("无权限操作此任务");
        } catch (Exception e) {
            // 系统异常
            log.error("任务取消失败，系统异常：", e);
            return Result.error("系统异常，请稍后再试");
        }
    }
}
