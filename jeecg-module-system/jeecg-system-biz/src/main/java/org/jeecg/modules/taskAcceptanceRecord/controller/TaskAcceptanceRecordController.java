package org.jeecg.modules.taskAcceptanceRecord.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;
import org.jeecg.modules.taskAcceptanceRecord.vo.TaskAcceptanceRecordVO;
import org.jeecg.modules.taskAcceptanceRecord.service.ITaskAcceptanceRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;

/**
 * @Description: 任务接受记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Api(tags="任务接受记录表")
@RestController
@RequestMapping("/taskAcceptanceRecord/taskAcceptanceRecord")
@Slf4j
public class TaskAcceptanceRecordController {
	@Autowired
	private ITaskAcceptanceRecordService taskAcceptanceRecordService;
	
	/**
	 * 分页列表查询（包含会员信息）
	 *
	 * @param taskAcceptanceRecord
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "任务接受记录表-分页列表查询")
	@ApiOperation(value="任务接受记录表-分页列表查询", notes="任务接受记录表-分页列表查询（包含会员信息）")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(TaskAcceptanceRecord taskAcceptanceRecord,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<TaskAcceptanceRecord> queryWrapper = QueryGenerator.initQueryWrapper(taskAcceptanceRecord, req.getParameterMap());
		Page<TaskAcceptanceRecordVO> page = new Page<TaskAcceptanceRecordVO>(pageNo, pageSize);
		IPage<TaskAcceptanceRecordVO> pageList = taskAcceptanceRecordService.queryPageListWithMemberInfo(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param taskAcceptanceRecord
	 * @return
	 */
	@AutoLog(value = "任务接受记录表-添加")
	@ApiOperation(value="任务接受记录表-添加", notes="任务接受记录表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody TaskAcceptanceRecord taskAcceptanceRecord) {
		taskAcceptanceRecordService.save(taskAcceptanceRecord);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param taskAcceptanceRecord
	 * @return
	 */
	@AutoLog(value = "任务接受记录表-编辑")
	@ApiOperation(value="任务接受记录表-编辑", notes="任务接受记录表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody TaskAcceptanceRecord taskAcceptanceRecord) {
		taskAcceptanceRecordService.updateById(taskAcceptanceRecord);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务接受记录表-通过id删除")
	@ApiOperation(value="任务接受记录表-通过id删除", notes="任务接受记录表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		taskAcceptanceRecordService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "任务接受记录表-批量删除")
	@ApiOperation(value="任务接受记录表-批量删除", notes="任务接受记录表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.taskAcceptanceRecordService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "任务接受记录表-通过id查询")
	@ApiOperation(value="任务接受记录表-通过id查询", notes="任务接受记录表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		TaskAcceptanceRecord taskAcceptanceRecord = taskAcceptanceRecordService.getById(id);
		if(taskAcceptanceRecord==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(taskAcceptanceRecord);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param taskAcceptanceRecord
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, TaskAcceptanceRecord taskAcceptanceRecord) {
        // 过滤选中数据
        String selections = request.getParameter("selections");
        QueryWrapper<TaskAcceptanceRecord> queryWrapper = QueryGenerator.initQueryWrapper(taskAcceptanceRecord, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //Step.1 调用导出方法
        List<TaskAcceptanceRecord> pageList = taskAcceptanceRecordService.list(queryWrapper);
        List<TaskAcceptanceRecord> exportList = null;

        // 过滤选中数据
        if(oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "任务接受记录表列表");
        mv.addObject(NormalExcelConstants.CLASS, TaskAcceptanceRecord.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("任务接受记录表数据", "导出人:"+sysUser.getRealname(), "任务接受记录表"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<TaskAcceptanceRecord> listTaskAcceptanceRecords = ExcelImportUtil.importExcel(file.getInputStream(),TaskAcceptanceRecord.class,params);
                taskAcceptanceRecordService.saveBatch(listTaskAcceptanceRecords);
                return Result.OK("文件导入成功！数据行数:" + listTaskAcceptanceRecords.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            }
        }
        return Result.OK("文件导入失败！");
    }

    /**
     * 审核任务接受记录
     *
     * @param id
     * @param status
     * @param auditReason
     * @param auditOpinion
     * @return
     */
    @AutoLog(value = "任务接受记录表-审核")
    @ApiOperation(value="任务接受记录表-审核", notes="任务接受记录表-审核")
    @PostMapping(value = "/audit")
    public Result<?> audit(@RequestParam(name="id",required=true) String id,
                          @RequestParam(name="status",required=true) String status,
                          @RequestParam(name="auditReason",required=false) String auditReason,
                          @RequestParam(name="auditOpinion",required=false) String auditOpinion) {
        TaskAcceptanceRecord record = taskAcceptanceRecordService.getById(id);
        if(record == null) {
            return Result.error("未找到对应的任务接受记录");
        }

        // 更新审核信息
        record.setStatus(status);
        record.setAuditReason(auditReason);
        record.setAuditOpinion(auditOpinion);
        record.setAuditTime(new java.util.Date());

        taskAcceptanceRecordService.updateById(record);

        String message = "3".equals(status) ? "审核通过！" : "审核拒绝！";
        return Result.OK(message);
    }



}
