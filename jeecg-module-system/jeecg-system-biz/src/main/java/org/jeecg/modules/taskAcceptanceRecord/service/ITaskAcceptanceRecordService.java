package org.jeecg.modules.taskAcceptanceRecord.service;

import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;
import org.jeecg.modules.taskAcceptanceRecord.vo.TaskAcceptanceRecordVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * @Description: 任务接受记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface ITaskAcceptanceRecordService extends IService<TaskAcceptanceRecord> {

    /**
     * 取消任务接受记录
     * 用于C端用户主动取消已接受的任务
     *
     * @param recordId 任务接受记录ID
     * @param memberId 当前登录用户ID（用于权限校验）
     * @return 取消结果
     * @throws IllegalStateException 当任务状态不允许取消或已过期时抛出
     * @throws SecurityException 当用户无权限操作此任务时抛出
     */
    boolean cancelAcceptanceRecord(String recordId, String memberId) throws IllegalStateException, SecurityException;

    /**
     * 分页查询包含会员信息的任务接受记录
     * 通过关联查询获取接受者的昵称、手机号等会员信息
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 包含会员信息的任务接受记录分页结果
     */
    IPage<TaskAcceptanceRecordVO> queryPageListWithMemberInfo(Page<TaskAcceptanceRecordVO> page, QueryWrapper<TaskAcceptanceRecord> queryWrapper);

}
