package org.jeecg.modules.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.member.service.IMemberListService;
import org.jeecg.modules.task.service.TaskBusinessService;
import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;
import org.jeecg.modules.taskAcceptanceRecord.service.ITaskAcceptanceRecordService;
import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.service.ITaskPublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 任务业务逻辑服务实现
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Slf4j
@Service
public class TaskBusinessServiceImpl implements TaskBusinessService {

    @Autowired
    private ITaskPublishService taskPublishService;

    @Autowired
    private ITaskAcceptanceRecordService taskAcceptanceRecordService;

    @Autowired
    private IMemberListService memberListService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishTask(TaskPublish taskPublish) {
        log.info("开始发布任务，发布者ID：{}，任务标题：{}", taskPublish.getPublisherId(), taskPublish.getTitle());

        try {
            // 1. 计算预扣助力值总额
            BigDecimal totalAmount = calculateTotalReward(taskPublish.getUnitPrice(), taskPublish.getTotalCount());
            taskPublish.setDeductedBalance(totalAmount);
            taskPublish.setRemainingCount(taskPublish.getTotalCount());
            taskPublish.setStatus("1"); // 进行中
            taskPublish.setPaidBalance(BigDecimal.ZERO);
            taskPublish.setRefundBalance(BigDecimal.ZERO);

            // 2. 预扣发布者助力值
            String orderNo = "TASK_PUBLISH_" + System.currentTimeMillis();
            boolean deductResult = memberListService.subtractBlance(
                taskPublish.getPublisherId(),
                totalAmount,
                orderNo,
                "54", // 任务发布预扣
                "任务发布预扣助力值：" + taskPublish.getTitle()
            );

            if (!deductResult) {
                log.error("任务发布失败，助力值预扣失败，发布者ID：{}，预扣金额：{}", taskPublish.getPublisherId(), totalAmount);
                return false;
            }

            // 3. 保存任务信息
            boolean saveResult = taskPublishService.save(taskPublish);
            if (!saveResult) {
                log.error("任务发布失败，任务信息保存失败，发布者ID：{}", taskPublish.getPublisherId());
                return false;
            }

            log.info("任务发布成功，任务ID：{}，预扣助力值：{}", taskPublish.getId(), totalAmount);
            return true;

        } catch (Exception e) {
            log.error("任务发布异常，发布者ID：{}，异常信息：{}", taskPublish.getPublisherId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean acceptTask(String taskId, String acceptorId, Integer acceptCount) {
        log.info("开始接受任务，任务ID：{}，接受者ID：{}，接受数量：{}", taskId, acceptorId, acceptCount);

        try {
            // 1. 检查任务是否可以接受
            if (!canAcceptTask(taskId, acceptorId, acceptCount)) {
                log.warn("任务接受失败，任务不可接受，任务ID：{}，接受者ID：{}", taskId, acceptorId);
                return false;
            }

            // 2. 获取任务信息
            TaskPublish taskPublish = taskPublishService.getById(taskId);
            if (taskPublish == null) {
                log.error("任务接受失败，任务不存在，任务ID：{}", taskId);
                return false;
            }

            // 3. 使用数据库行级锁更新剩余数量
            LambdaUpdateWrapper<TaskPublish> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TaskPublish::getId, taskId)
                        .eq(TaskPublish::getRemainingCount, taskPublish.getRemainingCount()) // 乐观锁
                        .set(TaskPublish::getRemainingCount, taskPublish.getRemainingCount() - acceptCount);

            // 如果剩余数量等于接受数量，则更新状态为已满额
            if (taskPublish.getRemainingCount().equals(acceptCount)) {
                updateWrapper.set(TaskPublish::getStatus, "2"); // 已满额
            }

            boolean updateResult = taskPublishService.update(updateWrapper);
            if (!updateResult) {
                log.warn("任务接受失败，任务状态更新失败（可能存在并发冲突），任务ID：{}", taskId);
                return false;
            }

            // 4. 创建接受记录
            Date acceptTime = new Date();
            TaskAcceptanceRecord record = new TaskAcceptanceRecord();
            record.setTaskId(taskId);
            record.setAcceptorId(acceptorId);
            record.setAcceptCount(acceptCount);
            record.setUnitPrice(taskPublish.getUnitPrice());
            record.setTotalReward(calculateTotalReward(taskPublish.getUnitPrice(), acceptCount));
            record.setStatus("1"); // 进行中
            record.setAcceptTime(acceptTime);
            record.setAutoAuditFlag(0); // 默认非自动审核

            // 5. 计算并设置实际截止时间
            Date actualDeadline = taskPublishService.calculateActualDeadline(taskPublish, acceptTime);
            record.setActualDeadline(actualDeadline);

            boolean saveResult = taskAcceptanceRecordService.save(record);
            if (!saveResult) {
                log.error("任务接受失败，接受记录保存失败，任务ID：{}，接受者ID：{}", taskId, acceptorId);
                return false;
            }

            log.info("任务接受成功，记录ID：{}，总奖励：{}", record.getId(), record.getTotalReward());
            return true;

        } catch (Exception e) {
            log.error("任务接受异常，任务ID：{}，接受者ID：{}，异常信息：{}", taskId, acceptorId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitTaskForAudit(String recordId, String submitContent, String submitImages) {
        log.info("开始提交任务审核，记录ID：{}", recordId);

        try {
            // 1. 获取接受记录
            TaskAcceptanceRecord record = taskAcceptanceRecordService.getById(recordId);
            if (record == null) {
                log.error("提交审核失败，接受记录不存在，记录ID：{}", recordId);
                return false;
            }

            // 2. 检查记录状态
            if (!"1".equals(record.getStatus())) {
                log.warn("提交审核失败，记录状态不正确，记录ID：{}，当前状态：{}", recordId, record.getStatus());
                return false;
            }

            // 3. 更新记录状态和提交信息
            record.setStatus("2"); // 待审核
            record.setSubmitContent(submitContent);
            record.setSubmitImages(submitImages);
            record.setSubmitTime(new Date());

            boolean updateResult = taskAcceptanceRecordService.updateById(record);
            if (!updateResult) {
                log.error("提交审核失败，记录更新失败，记录ID：{}", recordId);
                return false;
            }

            log.info("任务提交审核成功，记录ID：{}", recordId);
            return true;

        } catch (Exception e) {
            log.error("提交任务审核异常，记录ID：{}，异常信息：{}", recordId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditTask(String recordId, String auditorId, boolean auditResult, String auditReason, String auditOpinion) {
        log.info("开始审核任务，记录ID：{}，审核人ID：{}，审核结果：{}", recordId, auditorId, auditResult);

        try {
            // 1. 获取接受记录
            TaskAcceptanceRecord record = taskAcceptanceRecordService.getById(recordId);
            if (record == null) {
                log.error("任务审核失败，接受记录不存在，记录ID：{}", recordId);
                return false;
            }

            // 2. 检查记录状态
            if (!"2".equals(record.getStatus())) {
                log.warn("任务审核失败，记录状态不正确，记录ID：{}，当前状态：{}", recordId, record.getStatus());
                return false;
            }

            // 3. 更新审核信息
            record.setAuditorId(auditorId);
            record.setAuditTime(new Date());
            record.setAuditReason(auditReason);
            record.setAuditOpinion(auditOpinion);

            if (auditResult) {
                // 审核通过
                record.setStatus("3"); // 已完成

                // 发放奖励给接受者
                String orderNo = "TASK_REWARD_" + System.currentTimeMillis();
                boolean rewardResult = memberListService.addBlance(
                    record.getAcceptorId(),
                    record.getTotalReward(),
                    orderNo,
                    "55", // 任务奖励发放
                    "任务完成奖励：记录ID " + recordId
                );

                if (!rewardResult) {
                    log.error("任务审核失败，奖励发放失败，记录ID：{}，接受者ID：{}", recordId, record.getAcceptorId());
                    return false;
                }

                // 更新任务的已发放助力值
                TaskPublish taskPublish = taskPublishService.getById(record.getTaskId());
                if (taskPublish != null) {
                    taskPublish.setPaidBalance(taskPublish.getPaidBalance().add(record.getTotalReward()));
                    taskPublishService.updateById(taskPublish);
                }

                log.info("任务审核通过，奖励已发放，记录ID：{}，奖励金额：{}", recordId, record.getTotalReward());
            } else {
                // 审核不通过
                record.setStatus("4"); // 未通过
                log.info("任务审核不通过，记录ID：{}，原因：{}", recordId, auditReason);
            }

            boolean updateResult = taskAcceptanceRecordService.updateById(record);
            if (!updateResult) {
                log.error("任务审核失败，记录更新失败，记录ID：{}", recordId);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("任务审核异常，记录ID：{}，异常信息：{}", recordId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean canAcceptTask(String taskId, String acceptorId, Integer acceptCount) {
        // 1. 检查任务是否存在
        TaskPublish taskPublish = taskPublishService.getById(taskId);
        if (taskPublish == null) {
            return false;
        }

        // 2. 检查任务状态
        if (!"1".equals(taskPublish.getStatus())) {
            return false;
        }

        // 3. 检查剩余数量
        if (taskPublish.getRemainingCount() < acceptCount) {
            return false;
        }

        // 4. 检查截止时间
        if (taskPublish.getDeadline() != null && taskPublish.getDeadline().before(new Date())) {
            return false;
        }

        // 5. 检查是否已经接受过该任务
        LambdaQueryWrapper<TaskAcceptanceRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskAcceptanceRecord::getTaskId, taskId)
                   .eq(TaskAcceptanceRecord::getAcceptorId, acceptorId)
                   .in(TaskAcceptanceRecord::getStatus, "1", "2", "3"); // 进行中、待审核、已完成

        long count = taskAcceptanceRecordService.count(queryWrapper);
        if (count > 0) {
            return false; // 已经接受过该任务
        }

        return true;
    }

    @Override
    public BigDecimal calculateTotalReward(BigDecimal unitPrice, Integer count) {
        if (unitPrice == null || count == null || count <= 0) {
            return BigDecimal.ZERO;
        }
        return unitPrice.multiply(new BigDecimal(count));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTask(String taskId, String operatorId, String reason) {
        log.info("开始取消任务，任务ID：{}，操作人ID：{}，原因：{}", taskId, operatorId, reason);

        try {
            // 1. 获取任务信息
            TaskPublish taskPublish = taskPublishService.getById(taskId);
            if (taskPublish == null) {
                log.error("取消任务失败，任务不存在，任务ID：{}", taskId);
                return false;
            }

            // 2. 检查任务状态
            if (!"1".equals(taskPublish.getStatus()) && !"2".equals(taskPublish.getStatus())) {
                log.warn("取消任务失败，任务状态不允许取消，任务ID：{}，当前状态：{}", taskId, taskPublish.getStatus());
                return false;
            }

            // 3. 计算需要退回的助力值
            BigDecimal refundAmount = taskPublish.getDeductedBalance().subtract(taskPublish.getPaidBalance());
            
            if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 退回剩余助力值给发布者
                String orderNo = "TASK_REFUND_" + System.currentTimeMillis();
                boolean refundResult = memberListService.addBlance(
                    taskPublish.getPublisherId(),
                    refundAmount,
                    orderNo,
                    "53", // 任务退回
                    "任务取消退回助力值：" + reason
                );

                if (!refundResult) {
                    log.error("取消任务失败，助力值退回失败，任务ID：{}，退回金额：{}", taskId, refundAmount);
                    return false;
                }

                taskPublish.setRefundBalance(refundAmount);
            }

            // 4. 更新任务状态
            taskPublish.setStatus("5"); // 已下架
            boolean updateResult = taskPublishService.updateById(taskPublish);
            if (!updateResult) {
                log.error("取消任务失败，任务状态更新失败，任务ID：{}", taskId);
                return false;
            }

            log.info("任务取消成功，任务ID：{}，退回助力值：{}", taskId, refundAmount);
            return true;

        } catch (Exception e) {
            log.error("取消任务异常，任务ID：{}，异常信息：{}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleTaskExpired(String taskId) {
        log.info("开始处理任务过期，任务ID：{}", taskId);

        try {
            // 1. 获取任务信息
            TaskPublish taskPublish = taskPublishService.getById(taskId);
            if (taskPublish == null) {
                log.error("处理任务过期失败，任务不存在，任务ID：{}", taskId);
                return false;
            }

            // 2. 检查任务是否已过期
            if (taskPublish.getDeadline() == null || taskPublish.getDeadline().after(new Date())) {
                log.warn("处理任务过期失败，任务未过期，任务ID：{}", taskId);
                return false;
            }

            // 3. 计算需要退回的助力值
            BigDecimal refundAmount = taskPublish.getDeductedBalance().subtract(taskPublish.getPaidBalance());
            
            if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 退回剩余助力值给发布者
                String orderNo = "TASK_EXPIRED_" + System.currentTimeMillis();
                boolean refundResult = memberListService.addBlance(
                    taskPublish.getPublisherId(),
                    refundAmount,
                    orderNo,
                    "56", // 任务退回
                    "任务过期退回助力值"
                );

                if (!refundResult) {
                    log.error("处理任务过期失败，助力值退回失败，任务ID：{}，退回金额：{}", taskId, refundAmount);
                    return false;
                }

                taskPublish.setRefundBalance(refundAmount);
            }

            // 4. 更新任务状态为已过期
            taskPublish.setStatus("4"); // 已过期
            boolean updateResult = taskPublishService.updateById(taskPublish);
            if (!updateResult) {
                log.error("处理任务过期失败，任务状态更新失败，任务ID：{}", taskId);
                return false;
            }

            // 5. 更新相关接受记录状态为已过期
            LambdaUpdateWrapper<TaskAcceptanceRecord> recordUpdateWrapper = new LambdaUpdateWrapper<>();
            recordUpdateWrapper.eq(TaskAcceptanceRecord::getTaskId, taskId)
                              .in(TaskAcceptanceRecord::getStatus, "1", "2") // 进行中、待审核
                              .set(TaskAcceptanceRecord::getStatus, "5"); // 已过期

            taskAcceptanceRecordService.update(recordUpdateWrapper);

            log.info("任务过期处理成功，任务ID：{}，退回助力值：{}", taskId, refundAmount);
            return true;

        } catch (Exception e) {
            log.error("处理任务过期异常，任务ID：{}，异常信息：{}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoAuditTask(String recordId) {
        log.info("开始自动审核任务，记录ID：{}", recordId);

        try {
            // 自动审核逻辑：直接通过审核
            return auditTask(recordId, "SYSTEM", true, null, "自动审核通过");

        } catch (Exception e) {
            log.error("自动审核任务异常，记录ID：{}，异常信息：{}", recordId, e.getMessage(), e);
            return false;
        }
    }
}
