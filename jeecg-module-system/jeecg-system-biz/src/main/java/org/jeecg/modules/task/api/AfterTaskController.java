package org.jeecg.modules.task.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.config.jwt.utils.LoginMemberUtil;
import org.jeecg.modules.task.service.TaskFlowService;
import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;
import org.jeecg.modules.taskAcceptanceRecord.service.ITaskAcceptanceRecordService;
import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.service.ITaskPublishService;
import org.jeecg.modules.taskPublish.vo.TaskPublishVO;
import org.jeecg.modules.task.vo.PendingAuditTaskVO;
import org.jeecg.modules.task.vo.MyAcceptedTaskVO;
import org.jeecg.modules.member.service.IMemberListService;
import org.jeecg.modules.member.entity.MemberList;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 任务发布认证接口（需要登录）
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Api(tags = "任务发布认证接口")
@RestController
@RequestMapping("/after/task")
@Slf4j
public class AfterTaskController {

    @Autowired
    private ITaskPublishService taskPublishService;

    @Autowired
    private ITaskAcceptanceRecordService taskAcceptanceRecordService;

    @Autowired
    private TaskFlowService taskFlowService;

    @Autowired
    private IMemberListService memberListService;

    /**
     * 发布任务
     * 
     * @param taskPublish 任务发布信息
     * @return 发布结果
     */
    @AutoLog(value = "发布任务")
    @ApiOperation(value = "发布任务", notes = "发布新任务")
    @PostMapping("/publish")
    public Result<TaskPublish> publishTask(TaskPublish taskPublish) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 验证任务时间逻辑
            List<String> validationErrors = taskPublishService.validateTaskTimeLogic(taskPublish);
            if (!validationErrors.isEmpty()) {
                return Result.error("任务参数验证失败：" + String.join(", ", validationErrors));
            }

            // 设置发布者信息
            taskPublish.setPublisherId(memberId);
            taskPublish.setPublisherType("1"); // 普通用户
            taskPublish.setIsOfficial(0); // 非官方任务
            taskPublish.setStatus("1");
            taskPublish.setCreateTime(new Date());
            taskPublish.setRemainingCount(taskPublish.getTotalCount());
            taskPublish.setPriority(0);

            // 设置任务类型默认值（向后兼容）
            if (taskPublish.getTaskType() == null || taskPublish.getTaskType().trim().isEmpty()) {
                taskPublish.setTaskType("1"); // 默认为短期任务
            }

            // 执行发布流程
            TaskFlowService.TaskFlowResult flowResult = taskFlowService.publishTaskFlow(taskPublish);
            if (!flowResult.isSuccess()) {
                return Result.error(flowResult.getMessage());
            }

            return Result.OK("任务发布成功", taskPublish);

        } catch (Exception e) {
            log.error("发布任务异常，用户ID：{}，异常信息：{}", LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("发布任务失败");
        }
    }

    /**
     * 接受任务
     * 
     * @param taskId 任务ID
     * @param acceptCount 接受数量
     * @return 接受结果
     */
    @AutoLog(value = "接受任务")
    @ApiOperation(value = "接受任务", notes = "接受指定任务")
    @PostMapping("/accept")
    public Result<String> acceptTask(
            @ApiParam(value = "任务ID", required = true) @RequestParam("taskId") String taskId,
            @ApiParam(value = "接受数量", required = true) @RequestParam("acceptCount") Integer acceptCount) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 执行接受流程
            TaskFlowService.TaskFlowResult flowResult = taskFlowService.acceptTaskFlow(taskId, memberId, acceptCount);
            if (!flowResult.isSuccess()) {
                return Result.error(flowResult.getMessage());
            }

            return Result.OK("任务接受成功");

        } catch (Exception e) {
            log.error("接受任务异常，任务ID：{}，用户ID：{}，异常信息：{}", taskId, LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("接受任务失败");
        }
    }

    /**
     * 提交任务审核
     * 
     * @param recordId 接受记录ID
     * @param submitContent 完成说明
     * @param submitImages 证明材料图片URLs
     * @return 提交结果
     */
    @AutoLog(value = "提交任务审核")
    @ApiOperation(value = "提交任务审核", notes = "提交任务完成材料进行审核")
    @PostMapping("/submit")
    public Result<String> submitTask(
            @ApiParam(value = "接受记录ID", required = true) @RequestParam("recordId") String recordId,
            @ApiParam(value = "完成说明", required = true) @RequestParam("submitContent") String submitContent,
            @ApiParam(value = "证明材料图片URLs") @RequestParam(value = "submitImages", required = false) String submitImages) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 验证记录归属
            TaskAcceptanceRecord record = taskAcceptanceRecordService.getById(recordId);
            if (record == null) {
                return Result.error("接受记录不存在");
            }
            if (!memberId.equals(record.getAcceptorId())) {
                return Result.error("无权限操作该记录");
            }

            // 执行提交流程
            TaskFlowService.TaskFlowResult flowResult = taskFlowService.submitTaskFlow(recordId, submitContent, submitImages);
            if (!flowResult.isSuccess()) {
                return Result.error(flowResult.getMessage());
            }

            return Result.OK("提交审核成功");

        } catch (Exception e) {
            log.error("提交任务审核异常，记录ID：{}，用户ID：{}，异常信息：{}", recordId, LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("提交审核失败");
        }
    }

    /**
     * 审核任务
     * 
     * @param recordId 接受记录ID
     * @param auditResult 审核结果
     * @param auditReason 审核理由
     * @param auditOpinion 审核意见
     * @return 审核结果
     */
    @AutoLog(value = "审核任务")
    @ApiOperation(value = "审核任务", notes = "审核任务完成情况")
    @PostMapping("/audit")
    public Result<String> auditTask(
            @ApiParam(value = "接受记录ID", required = true) @RequestParam("recordId") String recordId,
            @ApiParam(value = "审核结果：true-通过，false-不通过", required = true) @RequestParam("auditResult") Boolean auditResult,
            @ApiParam(value = "审核理由") @RequestParam(value = "auditReason", required = false) String auditReason,
            @ApiParam(value = "审核意见") @RequestParam(value = "auditOpinion", required = false) String auditOpinion) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 执行审核流程
            TaskFlowService.TaskFlowResult flowResult = taskFlowService.auditTaskFlow(
                recordId, memberId, auditResult, auditReason, auditOpinion);
            if (!flowResult.isSuccess()) {
                return Result.error(flowResult.getMessage());
            }

            return Result.OK("审核完成");

        } catch (Exception e) {
            log.error("审核任务异常，记录ID：{}，审核人ID：{}，异常信息：{}", recordId, LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("审核失败");
        }
    }

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @param reason 取消原因
     * @return 取消结果
     */
    @AutoLog(value = "取消任务")
    @ApiOperation(value = "取消任务", notes = "取消已发布的任务")
    @PostMapping("/cancel")
    public Result<String> cancelTask(
            @ApiParam(value = "任务ID", required = true) @RequestParam("taskId") String taskId,
            @ApiParam(value = "取消原因") @RequestParam(value = "reason", required = false) String reason) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 执行取消流程
            TaskFlowService.TaskFlowResult flowResult = taskFlowService.cancelTaskFlow(taskId, memberId, reason);
            if (!flowResult.isSuccess()) {
                return Result.error(flowResult.getMessage());
            }

            return Result.OK("任务取消成功");

        } catch (Exception e) {
            log.error("取消任务异常，任务ID：{}，用户ID：{}，异常信息：{}", taskId, LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("取消任务失败");
        }
    }

    /**
     * 我发布的任务列表
     * 
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param status 任务状态
     * @return 任务列表
     */
    @AutoLog(value = "我发布的任务列表")
    @ApiOperation(value = "我发布的任务列表", notes = "获取当前用户发布的任务列表")
    @GetMapping("/my-published")
    public Result<IPage<TaskPublishVO>> getMyPublishedTasks(
            @ApiParam(value = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "页大小", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "任务状态") @RequestParam(name = "status", required = false) String status) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 参数验证
            if (pageNo < 1) pageNo = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            // 构建查询条件
            LambdaQueryWrapper<TaskPublish> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TaskPublish::getPublisherId, memberId);

            if (StringUtils.hasText(status)) {
                queryWrapper.eq(TaskPublish::getStatus, status);
            }

            queryWrapper.orderByDesc(TaskPublish::getCreateTime);

            // 分页查询
            Page<TaskPublish> page = new Page<>(pageNo, pageSize);
            IPage<TaskPublish> pageList = taskPublishService.page(page, queryWrapper);

            // 转换为VO并填充完成进度信息
            List<TaskPublishVO> voList = taskPublishService.convertToVOWithProgress(pageList.getRecords());

            // 构建返回的分页对象
            Page<TaskPublishVO> voPage = new Page<>(pageNo, pageSize, pageList.getTotal());
            voPage.setRecords(voList);

            return Result.OK(voPage);

        } catch (Exception e) {
            log.error("获取我发布的任务列表异常，用户ID：{}，异常信息：{}", LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("获取任务列表失败");
        }
    }

    /**
     * 我接受的任务列表
     *
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param status 记录状态
     * @return 接受记录列表（包含任务基础信息）
     */
    @AutoLog(value = "我接受的任务列表")
    @ApiOperation(value = "我接受的任务列表", notes = "获取当前用户接受的任务列表（包含任务基础信息）")
    @GetMapping("/my-accepted")
    public Result<IPage<MyAcceptedTaskVO>> getMyAcceptedTasks(
            @ApiParam(value = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "页大小", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "记录状态") @RequestParam(name = "status", required = false) String status) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 参数验证
            if (pageNo < 1) pageNo = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            // 构建查询条件
            LambdaQueryWrapper<TaskAcceptanceRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TaskAcceptanceRecord::getAcceptorId, memberId);

            if (StringUtils.hasText(status)) {
                queryWrapper.eq(TaskAcceptanceRecord::getStatus, status);
            }

            queryWrapper.orderByDesc(TaskAcceptanceRecord::getCreateTime);

            // 分页查询接受记录
            Page<TaskAcceptanceRecord> page = new Page<>(pageNo, pageSize);
            IPage<TaskAcceptanceRecord> recordPageList = taskAcceptanceRecordService.page(page, queryWrapper);

            // 提取任务ID列表进行批量查询
            List<String> taskIds = recordPageList.getRecords().stream()
                    .map(TaskAcceptanceRecord::getTaskId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询任务发布信息
            Map<String, TaskPublish> taskPublishMap = new HashMap<>();
            if (!taskIds.isEmpty()) {
                LambdaQueryWrapper<TaskPublish> taskQueryWrapper = new LambdaQueryWrapper<>();
                taskQueryWrapper.in(TaskPublish::getId, taskIds);
                List<TaskPublish> taskPublishList = taskPublishService.list(taskQueryWrapper);
                taskPublishMap = taskPublishList.stream()
                        .collect(Collectors.toMap(TaskPublish::getId, task -> task));
            }

            // 批量查询发布者信息（普通用户）
            Set<String> memberPublisherIds = taskPublishMap.values().stream()
                    .filter(task -> "1".equals(task.getPublisherType()))
                    .map(TaskPublish::getPublisherId)
                    .collect(Collectors.toSet());

            Map<String, MemberList> memberMap = new HashMap<>();
            if (!memberPublisherIds.isEmpty()) {
                LambdaQueryWrapper<MemberList> memberQueryWrapper = new LambdaQueryWrapper<>();
                memberQueryWrapper.in(MemberList::getId, memberPublisherIds);
                List<MemberList> memberList = memberListService.list(memberQueryWrapper);
                memberMap = memberList.stream()
                        .collect(Collectors.toMap(MemberList::getId, member -> member));
            }

            // 组装VO数据
            List<MyAcceptedTaskVO> voList = new ArrayList<>();
            for (TaskAcceptanceRecord record : recordPageList.getRecords()) {
                MyAcceptedTaskVO vo = new MyAcceptedTaskVO();

                // 复制接受记录的所有字段
                BeanUtils.copyProperties(record, vo);

                // 获取关联的任务发布信息
                TaskPublish taskPublish = taskPublishMap.get(record.getTaskId());
                if (taskPublish != null) {
                    // 设置任务基础信息
                    vo.setTaskTitle(taskPublish.getTitle());
                    vo.setTaskDescription(taskPublish.getDescription());
                    vo.setTaskRequirements(taskPublish.getTaskRequirements());
                    vo.setTaskTotalCount(taskPublish.getTotalCount());
                    vo.setTaskRemainingCount(taskPublish.getRemainingCount());
                    vo.setTaskDeadline(taskPublish.getDeadline());
                    vo.setTaskPublisherId(taskPublish.getPublisherId());
                    vo.setTaskPublisherType(taskPublish.getPublisherType());
                    vo.setTaskIsOfficial(taskPublish.getIsOfficial());
                    vo.setTaskStatus(taskPublish.getStatus());
                    vo.setTaskPriority(taskPublish.getPriority());
                    vo.setTaskCreateTime(taskPublish.getCreateTime());

                    // 设置发布者信息
                    if ("1".equals(taskPublish.getPublisherType())) {
                        // 普通用户发布者
                        MemberList member = memberMap.get(taskPublish.getPublisherId());
                        if (member != null) {
                            vo.setPublisherNickname(member.getNickName());
                            vo.setPublisherAvatar(member.getHeadPortrait());
                        }
                    } else {
                        // 平台管理员发布者
                        vo.setPublisherNickname("平台官方");
                        vo.setPublisherAvatar("");
                    }

                    // 计算距离截止时间
                    if (taskPublish.getDeadline() != null) {
                        long diffInMillies = taskPublish.getDeadline().getTime() - System.currentTimeMillis();
                        long hoursToDeadline = diffInMillies / (1000 * 60 * 60);
                        vo.setHoursToDeadline(hoursToDeadline);
                        vo.setIsNearDeadline(hoursToDeadline <= 24 && hoursToDeadline > 0);
                    }
                }

                // 设置扩展字段
                if (StringUtils.hasText(record.getSubmitImages())) {
                    String[] images = record.getSubmitImages().split(",");
                    vo.setSubmitImageCount(images.length);
                } else {
                    vo.setSubmitImageCount(0);
                }

                voList.add(vo);
            }

            // 构建返回的分页对象
            Page<MyAcceptedTaskVO> voPage = new Page<>(pageNo, pageSize);
            voPage.setRecords(voList);
            voPage.setTotal(recordPageList.getTotal());
            voPage.setSize(recordPageList.getSize());
            voPage.setCurrent(recordPageList.getCurrent());
            voPage.setPages(recordPageList.getPages());

            return Result.OK(voPage);

        } catch (Exception e) {
            log.error("获取我接受的任务列表异常，用户ID：{}，异常信息：{}", LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("获取任务列表失败");
        }
    }

    /**
     * 待审核任务列表
     *
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 待审核任务列表
     */
    @AutoLog(value = "待审核任务列表")
    @ApiOperation(value = "待审核任务列表", notes = "获取当前用户需要审核的任务列表")
    @GetMapping("/pending-audit")
    public Result<IPage<PendingAuditTaskVO>> getPendingAuditTasks(
            @ApiParam(value = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "页大小", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            // 参数验证
            if (pageNo < 1) pageNo = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            // 查询当前用户发布的任务中，状态为待审核的接受记录
            LambdaQueryWrapper<TaskAcceptanceRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TaskAcceptanceRecord::getStatus, "2") // 待审核状态
                       .exists("SELECT 1 FROM task_publish tp WHERE tp.id = task_acceptance_record.task_id AND tp.publisher_id = '" + memberId + "'")
                       .orderByAsc(TaskAcceptanceRecord::getSubmitTime);

            // 分页查询接受记录
            Page<TaskAcceptanceRecord> page = new Page<>(pageNo, pageSize);
            IPage<TaskAcceptanceRecord> recordPageList = taskAcceptanceRecordService.page(page, queryWrapper);

            // 组装VO数据
            List<PendingAuditTaskVO> voList = new ArrayList<>();
            for (TaskAcceptanceRecord record : recordPageList.getRecords()) {
                PendingAuditTaskVO vo = new PendingAuditTaskVO();

                // 复制接受记录的所有字段
                BeanUtils.copyProperties(record, vo);

                // 查询关联的任务发布信息
                TaskPublish taskPublish = taskPublishService.getById(record.getTaskId());
                if (taskPublish != null) {
                    vo.setTaskTitle(taskPublish.getTitle());
                    vo.setTaskDescription(taskPublish.getDescription());
                    vo.setTaskRequirements(taskPublish.getTaskRequirements());
                    vo.setTaskTotalCount(taskPublish.getTotalCount());
                    vo.setTaskRemainingCount(taskPublish.getRemainingCount());
                    vo.setTaskDeadline(taskPublish.getDeadline());
                    vo.setTaskPublisherType(taskPublish.getPublisherType());
                    vo.setTaskIsOfficial(taskPublish.getIsOfficial());
                    vo.setTaskStatus(taskPublish.getStatus());
                    vo.setTaskPriority(taskPublish.getPriority());
                    vo.setTaskCreateTime(taskPublish.getCreateTime());
                }

                // 设置扩展字段
                if (StringUtils.hasText(record.getSubmitImages())) {
                    String[] images = record.getSubmitImages().split(",");
                    vo.setSubmitImageCount(images.length);
                }

                // 计算是否逾期提交
                if (taskPublish != null && record.getSubmitTime() != null && taskPublish.getDeadline() != null) {
                    vo.setIsOverdue(record.getSubmitTime().after(taskPublish.getDeadline()));
                }

                // 计算距离截止时间
                if (taskPublish != null && taskPublish.getDeadline() != null) {
                    long diffInMillies = taskPublish.getDeadline().getTime() - System.currentTimeMillis();
                    vo.setHoursToDeadline(diffInMillies / (1000 * 60 * 60));
                }

                voList.add(vo);
            }

            // 构建返回的分页对象
            Page<PendingAuditTaskVO> voPage = new Page<>(pageNo, pageSize);
            voPage.setRecords(voList);
            voPage.setTotal(recordPageList.getTotal());
            voPage.setSize(recordPageList.getSize());
            voPage.setCurrent(recordPageList.getCurrent());
            voPage.setPages(recordPageList.getPages());

            return Result.OK(voPage);

        } catch (Exception e) {
            log.error("获取待审核任务列表异常，用户ID：{}，异常信息：{}", LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("获取待审核任务列表失败");
        }
    }

    /**
     * 任务接受记录详情
     * 
     * @param recordId 记录ID
     * @return 记录详情
     */
    @AutoLog(value = "任务接受记录详情")
    @ApiOperation(value = "任务接受记录详情", notes = "获取任务接受记录详细信息")
    @GetMapping("/record/{recordId}")
    public Result<TaskAcceptanceRecord> getRecordDetail(
            @ApiParam(value = "记录ID", required = true) @PathVariable("recordId") String recordId) {

        try {
            // 获取登录用户ID
            String memberId = LoginMemberUtil.getLoginMemberId();
            if (!StringUtils.hasText(memberId)) {
                return Result.error("用户未登录");
            }

            TaskAcceptanceRecord record = taskAcceptanceRecordService.getById(recordId);
            if (record == null) {
                return Result.error("记录不存在");
            }

            // 验证权限：只有接受者或发布者可以查看
            TaskPublish taskPublish = taskPublishService.getById(record.getTaskId());
            if (taskPublish == null) {
                return Result.error("关联任务不存在");
            }

            if (!memberId.equals(record.getAcceptorId()) && !memberId.equals(taskPublish.getPublisherId())) {
                return Result.error("无权限查看该记录");
            }

            return Result.OK(record);

        } catch (Exception e) {
            log.error("获取任务接受记录详情异常，记录ID：{}，用户ID：{}，异常信息：{}", recordId, LoginMemberUtil.getLoginMemberId(), e.getMessage(), e);
            return Result.error("获取记录详情失败");
        }
    }
}
