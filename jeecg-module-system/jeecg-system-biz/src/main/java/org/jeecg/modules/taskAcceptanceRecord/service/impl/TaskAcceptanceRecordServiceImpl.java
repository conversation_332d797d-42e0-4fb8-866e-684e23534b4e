package org.jeecg.modules.taskAcceptanceRecord.service.impl;

import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;
import org.jeecg.modules.taskAcceptanceRecord.mapper.TaskAcceptanceRecordMapper;
import org.jeecg.modules.taskAcceptanceRecord.service.ITaskAcceptanceRecordService;
import org.jeecg.modules.taskAcceptanceRecord.vo.TaskAcceptanceRecordVO;
import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.service.ITaskPublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.Date;

/**
 * @Description: 任务接受记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Service
@Slf4j
public class TaskAcceptanceRecordServiceImpl extends ServiceImpl<TaskAcceptanceRecordMapper, TaskAcceptanceRecord> implements ITaskAcceptanceRecordService {

    @Autowired
    private ITaskPublishService taskPublishService;

    /**
     * 取消任务接受记录
     * 实现业务逻辑：权限校验、时间检测、额度回退、状态更新
     *
     * @param recordId 任务接受记录ID
     * @param memberId 当前登录用户ID（用于权限校验）
     * @return 取消结果
     * @throws IllegalStateException 当任务状态不允许取消或已过期时抛出
     * @throws SecurityException 当用户无权限操作此任务时抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelAcceptanceRecord(String recordId, String memberId) throws IllegalStateException, SecurityException {
        log.info("开始取消任务接受记录，recordId: {}, memberId: {}", recordId, memberId);

        try {
            // 1. 获取任务接受记录
            TaskAcceptanceRecord record = this.getById(recordId);
            if (record == null) {
                throw new IllegalStateException("任务接受记录不存在");
            }

            // 2. 权限校验：确保用户只能取消自己的任务
            if (!memberId.equals(record.getAcceptorId())) {
                throw new SecurityException("无权限操作此任务");
            }

            // 3. 检查任务状态是否允许取消
            if (!"1".equals(record.getStatus())) {
                throw new IllegalStateException("只有进行中的任务才能取消");
            }

            // 3. 检查任务是否已过期（基于actualDeadline）
            if (record.getActualDeadline() != null) {
                Date currentTime = new Date();
                if (record.getActualDeadline().before(currentTime)) {
                    throw new IllegalStateException("任务已过期，无法取消");
                }
            }

            // 4. 获取关联的任务发布信息
            TaskPublish taskPublish = taskPublishService.getById(record.getTaskId());
            if (taskPublish == null) {
                throw new IllegalStateException("关联的任务发布信息不存在");
            }

            // 5. 回退任务额度到任务主表
            Integer acceptCount = record.getAcceptCount();
            Integer currentRemaining = taskPublish.getRemainingCount();
            taskPublish.setRemainingCount(currentRemaining + acceptCount);

            // 6. 更新任务发布表
            boolean updateTaskResult = taskPublishService.updateById(taskPublish);
            if (!updateTaskResult) {
                log.error("更新任务发布表失败，taskId: {}", record.getTaskId());
                throw new RuntimeException("更新任务发布表失败");
            }

            // 7. 更新任务接受记录状态为"已取消"
            record.setStatus("6"); // 6-已取消
            record.setUpdateTime(new Date());
            boolean updateRecordResult = this.updateById(record);

            if (!updateRecordResult) {
                log.error("更新任务接受记录失败，recordId: {}", recordId);
                throw new RuntimeException("更新任务接受记录失败");
            }

            log.info("任务取消成功，recordId: {}, 回退数量: {}", recordId, acceptCount);
            return true;

        } catch (IllegalStateException e) {
            // 业务异常，直接抛出
            log.warn("任务取消失败，业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            // 系统异常，包装后抛出
            log.error("任务取消失败，系统异常：", e);
            throw new RuntimeException("系统异常，任务取消失败", e);
        }
    }

    /**
     * 分页查询包含会员信息的任务接受记录
     * 通过关联查询获取接受者的昵称、手机号等会员信息
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 包含会员信息的任务接受记录分页结果
     */
    @Override
    public IPage<TaskAcceptanceRecordVO> queryPageListWithMemberInfo(Page<TaskAcceptanceRecordVO> page, QueryWrapper<TaskAcceptanceRecord> queryWrapper) {
        log.info("开始查询包含会员信息的任务接受记录，页码: {}, 页大小: {}", page.getCurrent(), page.getSize());

        try {
            // 调用Mapper的关联查询方法
            IPage<TaskAcceptanceRecordVO> result = this.baseMapper.queryPageListWithMemberInfo(page, queryWrapper);

            log.info("查询完成，总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());
            return result;

        } catch (Exception e) {
            log.error("查询包含会员信息的任务接受记录失败：", e);
            throw new RuntimeException("查询任务接受记录失败", e);
        }
    }

}
