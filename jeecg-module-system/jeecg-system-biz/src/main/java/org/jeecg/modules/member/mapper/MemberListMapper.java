package org.jeecg.modules.member.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.agency.vo.AgencyWorkbenchVO;
import org.jeecg.modules.alliance.vo.AllianceWorkbenchVO;
import org.jeecg.modules.marketing.vo.MarketingDiscountCouponVO;
import org.jeecg.modules.member.dto.MemberListDTO;
import org.jeecg.modules.member.entity.MemberList;
import org.jeecg.modules.member.vo.MemberCertificateVO;
import org.jeecg.modules.member.vo.MemberDiscountVO;
import org.jeecg.modules.member.vo.MemberListVO;
import org.jeecg.modules.store.vo.StoreManageVO;
import org.jeecg.modules.system.vo.SysWorkbenchVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description: 会员列表
 * @Author: jeecg-boot
 * @Date:   2019-10-24
 * @Version: V1.0
 */
public interface MemberListMapper extends BaseMapper<MemberList> {
     List<MemberList> selectMemberListById(@Param("memberListId") String memberListId);

    IPage<MemberListVO> findMemberList(Page<MemberListVO> page,@Param("memberListVO") MemberListVO memberListVO);

    IPage<MarketingDiscountCouponVO> findMemberDiscount(Page<MarketingDiscountCouponVO> page,@Param("memberDiscountVO") MemberDiscountVO memberDiscountVO);

    IPage<MemberCertificateVO> findMemberCertificate(Page<MemberCertificateVO> page, @Param("memberCertificateVO") MemberCertificateVO memberCertificateVO);


    /**
     * 查询用户团队数量
     *
     * @param memberId
     * @return
     */
    public MemberListVO findMemberDistributionCount(@Param("memberId") String memberId);

    /**
     * 查询用户的级别按照时间排序
     *
     * @param page
     * @param memberId
     * @return
     */
    public IPage<Map<String,Object>> findMemberLevelList(Page<Map<String,Object>> page,@Param("memberId") String memberId);

    Map<String,Long> getStoreSexSum(@Param("sysWorkbenchVO") SysWorkbenchVO sysWorkbenchVO);

    Map<String,Object> getAgencySexSum(@Param("agencyWorkbenchVO") AgencyWorkbenchVO agencyWorkbenchVO);

    Map<String,Long> getSysSexSum(@Param("sysWorkbenchVO") SysWorkbenchVO sysWorkbenchVO);

    IPage<MemberListVO> findAgencyMemberList(Page<MemberListVO> page,@Param("memberListVO") MemberListVO memberListVO);


    /**
     * 获取用户管理会员数据
     * @param page
     * @param sysUserId
     * @param searchNickNamePhone
     * @return
     */
    IPage<Map<String,Object>> getMyMemberList(Page<MemberList> page,@Param("sysUserId")String sysUserId,@Param("searchNickNamePhone")String searchNickNamePhone);

    Map<String,Object> getFranchiseeSexSum(@Param("workbench") AllianceWorkbenchVO workbench);

    IPage<MemberListVO> findAllianceMemberlist(Page<StoreManageVO> page,@Param("memberListDTO") MemberListDTO memberListDTO);

    List<Map<String,Object>> likeMemberByPhone(@Param("phone") String phone);

    /**
     * 查询会员信息(包含删除状态)
     * @param memberListId
     * @return
     */
    MemberList  getMemberListById(@Param("memberListId") String memberListId);

    MemberListVO findMemberDistributionCountByMemberType(@Param("id") String id);

    Long findMemberVipByMarketingGiftBag(@Param("id") String id);

    MemberListVO findMemberVipByMarketingGiftBagCount(@Param("id") String id);

    Long findMemberVipByMarketingGiftBagAndStraightPushId(@Param("straightPushId") String straightPushId,@Param("id") String id);

    IPage<Map<String,Object>> getmemberListByTManageId(Page<Map<String,Object>> page,@Param("id") String id,@Param("memberDesignationGroupId") String memberDesignationGroupId);

    IPage<MemberListVO> memberDesignationPageList(Page<MemberListVO> page,@Param("memberListDTO") MemberListDTO memberListDTO);

    List<MemberListVO> getUnderlingList(@Param("id") String id);

    List<Map<String,Object>> getDesignateMemberListByPhone(@Param("phone") String phone);

    /**
     * 查询会员总业绩（包含自身和下级所有业绩）
     * @param memberId 会员ID
     * @param uniqueId 会员唯一标识
     * @return 业绩数据列表
     */
    List<Map<String, Object>> queryMemberTotalPerformance(@Param("memberId") String memberId, @Param("uniqueId") String uniqueId, @Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 查询会员直接业绩（自身和直接下级业绩）
     * @param memberId 会员ID
     * @param uniqueId 会员唯一标识
     * @return 业绩数据列表
     */
    List<Map<String, Object>> queryMemberDirectPerformance(@Param("memberId") String memberId, @Param("uniqueId") String uniqueId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询会员分销佣金
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 佣金数据列表
     */
    List<Map<String, Object>> queryMemberCommission(@Param("memberId") String memberId, @Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 查询会员分销订单数量
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 订单数量数据列表
     */
    List<Map<String, Object>> queryMemberCommissionOrderCount(@Param("memberId") String memberId, @Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 查询会员分销订单列表
     * @param page 分页参数
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orderConfirmStatus 订单确认状态 -1：全部分销订单（包含已确认订单、未确认订单）
     *                          0：已确认订单（订单状态3、5）
     *                          1：未确认订单（订单状态1、2）
     * @return 订单列表数据
     */
    IPage<Map<String, Object>> queryMemberDistributionOrders(Page<Map<String, Object>> page, @Param("memberId") String memberId, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("orderConfirmStatus") String orderConfirmStatus);

    List<MemberList> getMemberDesignationListById(@Param("marketingGiftBagId") String marketingGiftBagId,@Param("memberDesignationId") String memberDesignationId);
    
    /**
     * 查询团队明细分页列表
     * @param page 分页参数
     * @param memberId 会员ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 团队明细列表数据
     */
    IPage<Map<String, Object>> queryTeamDetailList(Page<Map<String, Object>> page, @Param("memberId") String memberId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 计算团队成员的影响力分数（全部历史数据）
     * @param memberPath 会员路径
     * @return 影响力分数
     */
    BigDecimal calculateInfluenceScoreForTeamMember(@Param("memberPath") String memberPath);

    /**
     * 查询直推人数
     *
     * @param memberId
     * @return
     */
    IPage<Map<String,Object>> pushingNumber(Page<Map<String,Object>> page,@Param("memberId") String memberId);

    /**
     * 获取间推人数
     *
     * @param memberId
     * @return
     */
    public int betweenPush(@Param("memberId") String memberId);

    /**
     * 查询用户已贡献业绩
     *
     * @param memberPath
     * @return
     */
    public BigDecimal getContributionPerformance(@Param("memberPath") String memberPath);

    void updatePromoter(MemberList memberList);
}
