package org.jeecg.modules.taskAcceptanceRecord.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;
import org.jeecg.modules.taskAcceptanceRecord.vo.TaskAcceptanceRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * @Description: 任务接受记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface TaskAcceptanceRecordMapper extends BaseMapper<TaskAcceptanceRecord> {

    /**
     * 分页查询包含会员信息的任务接受记录
     * 通过LEFT JOIN关联member_list表获取会员昵称和手机号信息
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 包含会员信息的任务接受记录分页结果
     */
    IPage<TaskAcceptanceRecordVO> queryPageListWithMemberInfo(
        Page<TaskAcceptanceRecordVO> page,
        @Param("ew") QueryWrapper<TaskAcceptanceRecord> queryWrapper
    );

}
