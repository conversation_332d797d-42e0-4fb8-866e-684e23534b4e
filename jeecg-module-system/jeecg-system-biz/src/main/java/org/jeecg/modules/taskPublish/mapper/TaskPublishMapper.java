package org.jeecg.modules.taskPublish.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.taskPublish.entity.TaskPublish;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 任务发布表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface TaskPublishMapper extends BaseMapper<TaskPublish> {

    /**
     * 批量查询任务完成进度
     * @param taskIds 任务ID列表
     * @return Map<taskId, completedCount> 任务ID与完成数量的映射
     */
    @Select("<script>" +
            "SELECT task_id, COUNT(*) as completed_count " +
            "FROM task_acceptance_record " +
            "WHERE task_id IN " +
            "<foreach collection='taskIds' item='taskId' open='(' separator=',' close=')'>" +
            "#{taskId}" +
            "</foreach> " +
            "AND status = '3' " +
            "GROUP BY task_id" +
            "</script>")
    List<Map<String, Object>> batchQueryCompletedCount(@Param("taskIds") List<String> taskIds);
}
