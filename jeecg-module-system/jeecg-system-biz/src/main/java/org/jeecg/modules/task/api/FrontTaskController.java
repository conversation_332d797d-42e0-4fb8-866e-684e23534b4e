package org.jeecg.modules.task.api;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.service.ITaskPublishService;
import org.jeecg.modules.taskPublish.vo.TaskPublishVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * @Description: 任务发布公开接口（无需登录）
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Api(tags = "任务发布公开接口")
@RestController
@RequestMapping("/front/task")
@Slf4j
public class FrontTaskController {

    @Autowired
    private ITaskPublishService taskPublishService;

    /**
     * 任务列表（公开接口，无需登录）
     * 
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param status 任务状态
     * @param priority 优先级
     * @param isOfficial 是否官方任务
     * @param keyword 关键词搜索
     * @param sortField 排序字段
     * @param sortOrder 排序方式
     * @param req 请求对象
     * @return 任务列表
     */
    @AutoLog(value = "任务列表-公开接口")
    @ApiOperation(value = "任务列表", notes = "获取任务列表，支持分页和筛选")
    @GetMapping("/list")
    public Result<IPage<TaskPublishVO>> getTaskList(
            @ApiParam(value = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "页大小", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "任务状态：1-进行中，2-已满额") @RequestParam(name = "status", required = false) String status,
            @ApiParam(value = "优先级：0-普通，1-高，2-紧急") @RequestParam(name = "priority", required = false) Integer priority,
            @ApiParam(value = "是否官方任务：0-否，1-是") @RequestParam(name = "isOfficial", required = false) Integer isOfficial,
            @ApiParam(value = "关键词搜索") @RequestParam(name = "keyword", required = false) String keyword,
            @ApiParam(value = "排序字段") @RequestParam(name = "sortField", defaultValue = "createTime") String sortField,
            @ApiParam(value = "排序方式：asc-升序，desc-降序") @RequestParam(name = "sortOrder", defaultValue = "desc") String sortOrder,
            HttpServletRequest req) {

        try {
            // 参数验证
            if (pageNo < 1) pageNo = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 10;

            // 构建查询条件
            LambdaQueryWrapper<TaskPublish> queryWrapper = new LambdaQueryWrapper<>();
            
            // 只查询进行中的任务（公开可见）
            queryWrapper.in(TaskPublish::getStatus, "1");
            
            // 只查询未过期的任务
            queryWrapper.and(wrapper -> wrapper.isNull(TaskPublish::getDeadline)
                                              .or()
                                              .ge(TaskPublish::getDeadline, new Date()));

            // 状态筛选
            if (StringUtils.hasText(status)) {
                queryWrapper.eq(TaskPublish::getStatus, status);
            }

            // 优先级筛选
            if (priority != null) {
                queryWrapper.eq(TaskPublish::getPriority, priority);
            }

            // 是否官方任务筛选
            if (isOfficial != null) {
                queryWrapper.eq(TaskPublish::getIsOfficial, isOfficial);
            }

            // 关键词搜索（标题和描述）
            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> wrapper.like(TaskPublish::getTitle, keyword)
                                                   .or()
                                                   .like(TaskPublish::getDescription, keyword));
            }

            // 排序
            if ("asc".equalsIgnoreCase(sortOrder)) {
                switch (sortField) {
                    case "unitPrice":
                        queryWrapper.orderByAsc(TaskPublish::getUnitPrice);
                        break;
                    case "totalCount":
                        queryWrapper.orderByAsc(TaskPublish::getTotalCount);
                        break;
                    case "remainingCount":
                        queryWrapper.orderByAsc(TaskPublish::getRemainingCount);
                        break;
                    case "deadline":
                        queryWrapper.orderByAsc(TaskPublish::getDeadline);
                        break;
                    default:
                        queryWrapper.orderByAsc(TaskPublish::getCreateTime);
                        break;
                }
            } else {
                switch (sortField) {
                    case "unitPrice":
                        queryWrapper.orderByDesc(TaskPublish::getUnitPrice);
                        break;
                    case "totalCount":
                        queryWrapper.orderByDesc(TaskPublish::getTotalCount);
                        break;
                    case "remainingCount":
                        queryWrapper.orderByDesc(TaskPublish::getRemainingCount);
                        break;
                    case "deadline":
                        queryWrapper.orderByDesc(TaskPublish::getDeadline);
                        break;
                    default:
                        queryWrapper.orderByDesc(TaskPublish::getCreateTime);
                        break;
                }
            }

            // 分页查询
            Page<TaskPublish> page = new Page<>(pageNo, pageSize);
            IPage<TaskPublish> pageList = taskPublishService.page(page, queryWrapper);

            // 转换为VO并填充完成进度信息
            List<TaskPublishVO> voList = taskPublishService.convertToVOWithProgress(pageList.getRecords());

            // 构建返回的分页对象
            Page<TaskPublishVO> voPage = new Page<>(pageNo, pageSize, pageList.getTotal());
            voPage.setRecords(voList);

            return Result.OK(voPage);

        } catch (Exception e) {
            log.error("获取任务列表异常：{}", e.getMessage(), e);
            return Result.error("获取任务列表失败");
        }
    }

    /**
     * 任务详情（公开接口，无需登录）
     * 
     * @param id 任务ID
     * @return 任务详情
     */
    @AutoLog(value = "任务详情-公开接口")
    @ApiOperation(value = "任务详情", notes = "获取任务详细信息")
    @GetMapping("/detail/{id}")
    public Result<TaskPublish> getTaskDetail(
            @ApiParam(value = "任务ID", required = true) @PathVariable("id") String id) {

        try {
            if (!StringUtils.hasText(id)) {
                return Result.error("任务ID不能为空");
            }

            TaskPublish taskPublish = taskPublishService.getById(id);
            if (taskPublish == null) {
                return Result.error("任务不存在");
            }

            // 只返回公开可见的任务（进行中、已满额）
            if (!"1".equals(taskPublish.getStatus()) && !"2".equals(taskPublish.getStatus())) {
                return Result.error("任务不可见");
            }

            return Result.OK(taskPublish);

        } catch (Exception e) {
            log.error("获取任务详情异常，任务ID：{}，异常信息：{}", id, e.getMessage(), e);
            return Result.error("获取任务详情失败");
        }
    }

    /**
     * 热门任务列表（公开接口，无需登录）
     * 
     * @param limit 限制数量
     * @return 热门任务列表
     */
    @AutoLog(value = "热门任务列表-公开接口")
    @ApiOperation(value = "热门任务列表", notes = "获取热门任务列表")
    @GetMapping("/hot")
    public Result<IPage<TaskPublish>> getHotTaskList(
            @ApiParam(value = "限制数量", example = "10") @RequestParam(name = "limit", defaultValue = "10") Integer limit) {

        try {
            // 参数验证
            if (limit < 1 || limit > 50) limit = 10;

            // 构建查询条件：进行中的任务，按单价降序排列
            LambdaQueryWrapper<TaskPublish> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TaskPublish::getStatus, "1") // 只查询进行中的任务
                       .and(wrapper -> wrapper.isNull(TaskPublish::getDeadline)
                                              .or()
                                              .ge(TaskPublish::getDeadline, new Date())) // 未过期
                       .orderByDesc(TaskPublish::getUnitPrice) // 按单价降序
                       .orderByDesc(TaskPublish::getCreateTime); // 再按创建时间降序

            // 分页查询
            Page<TaskPublish> page = new Page<>(1, limit);
            IPage<TaskPublish> pageList = taskPublishService.page(page, queryWrapper);

            return Result.OK(pageList);

        } catch (Exception e) {
            log.error("获取热门任务列表异常：{}", e.getMessage(), e);
            return Result.error("获取热门任务列表失败");
        }
    }

    /**
     * 任务统计信息（公开接口，无需登录）
     * 
     * @return 统计信息
     */
    @AutoLog(value = "任务统计信息-公开接口")
    @ApiOperation(value = "任务统计信息", notes = "获取任务统计信息")
    @GetMapping("/statistics")
    public Result<Object> getTaskStatistics() {

        try {
            // 统计进行中的任务数量
            LambdaQueryWrapper<TaskPublish> ongoingWrapper = new LambdaQueryWrapper<>();
            ongoingWrapper.eq(TaskPublish::getStatus, "1")
                         .and(wrapper -> wrapper.isNull(TaskPublish::getDeadline)
                                                .or()
                                                .ge(TaskPublish::getDeadline, new Date()));
            long ongoingCount = taskPublishService.count(ongoingWrapper);

            // 统计今日新增任务数量
            Date today = new Date();
            Date startOfDay = new Date(today.getYear(), today.getMonth(), today.getDate());
            Date endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);

            LambdaQueryWrapper<TaskPublish> todayWrapper = new LambdaQueryWrapper<>();
            todayWrapper.between(TaskPublish::getCreateTime, startOfDay, endOfDay);
            long todayCount = taskPublishService.count(todayWrapper);

            // 统计官方任务数量
            LambdaQueryWrapper<TaskPublish> officialWrapper = new LambdaQueryWrapper<>();
            officialWrapper.eq(TaskPublish::getIsOfficial, 1)
                          .in(TaskPublish::getStatus, "1", "2")
                          .and(wrapper -> wrapper.isNull(TaskPublish::getDeadline)
                                                 .or()
                                                 .ge(TaskPublish::getDeadline, new Date()));
            long officialCount = taskPublishService.count(officialWrapper);

            // 构建返回结果
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("ongoingCount", ongoingCount);
            statistics.put("todayCount", todayCount);
            statistics.put("officialCount", officialCount);

            return Result.OK(statistics);

        } catch (Exception e) {
            log.error("获取任务统计信息异常：{}", e.getMessage(), e);
            return Result.error("获取统计信息失败");
        }
    }
}
