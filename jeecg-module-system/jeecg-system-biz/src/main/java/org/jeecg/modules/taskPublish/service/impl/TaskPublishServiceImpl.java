package org.jeecg.modules.taskPublish.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.mapper.TaskPublishMapper;
import org.jeecg.modules.taskPublish.service.ITaskPublishService;
import org.jeecg.modules.taskPublish.vo.TaskPublishVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 任务发布表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Service
public class TaskPublishServiceImpl extends ServiceImpl<TaskPublishMapper, TaskPublish> implements ITaskPublishService {

    @Override
    public List<TaskPublishVO> convertToVOWithProgress(List<TaskPublish> taskPublishList) {
        if (CollectionUtils.isEmpty(taskPublishList)) {
            return new ArrayList<>();
        }

        // 提取任务ID列表
        List<String> taskIds = taskPublishList.stream()
                .map(TaskPublish::getId)
                .collect(Collectors.toList());

        // 批量查询完成进度
        Map<String, Integer> completedCountMap = new HashMap<>();
        List<Map<String, Object>> completedResults = baseMapper.batchQueryCompletedCount(taskIds);
        for (Map<String, Object> result : completedResults) {
            String taskId = (String) result.get("task_id");
            Integer completedCount = ((Number) result.get("completed_count")).intValue();
            completedCountMap.put(taskId, completedCount);
        }

        // 转换为VO并设置完成进度
        List<TaskPublishVO> voList = new ArrayList<>();
        for (TaskPublish taskPublish : taskPublishList) {
            TaskPublishVO vo = new TaskPublishVO();
            BeanUtils.copyProperties(taskPublish, vo);

            // 设置完成数量（如果没有完成记录则为0）
            Integer completedCount = completedCountMap.getOrDefault(taskPublish.getId(), 0);
            vo.setCompletedCount(completedCount);

            // 计算并设置实际截止时间和显示时间
            Date actualDeadline = calculateDisplayDeadline(taskPublish);
            vo.setActualDeadline(actualDeadline);
            vo.setDisplayDeadline(formatDeadlineForDisplay(actualDeadline));

            // 计算完成进度百分比
            vo.calculateCompletedPercent();

            // 计算是否即将截止（基于实际截止时间）
            vo.calculateNearDeadline();

            voList.add(vo);
        }

        return voList;
    }

    /**
     * 计算实际截止时间（基于接受时间）
     * 短期任务：直接使用deadline
     * 长期任务：取deadline和(接受时间+天数)的较小值
     *
     * @param task 任务对象
     * @param acceptTime 接受任务的时间
     * @return 实际截止时间
     */
    public Date calculateActualDeadline(TaskPublish task, Date acceptTime) {
        if (task == null || task.getDeadline() == null || acceptTime == null) {
            return task != null ? task.getDeadline() : null;
        }

        // 短期任务或天数为空：直接使用deadline
        // 向后兼容：taskType为null时当作短期任务处理
        if (task.getTaskType() == null || "1".equals(task.getTaskType()) || task.getDaysToComplete() == null || task.getDaysToComplete() <= 0) {
            return task.getDeadline();
        }

        // 长期任务：计算从接受时间开始的截止时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(acceptTime);
        cal.add(Calendar.DAY_OF_MONTH, task.getDaysToComplete());
        Date deadlineFromAcceptTime = cal.getTime();

        // 返回较早的时间
        return task.getDeadline().before(deadlineFromAcceptTime) ? task.getDeadline() : deadlineFromAcceptTime;
    }

    /**
     * 计算实际截止时间（使用当前时间作为接受时间）
     * 主要用于前端预览
     *
     * @param task 任务对象
     * @return 实际截止时间
     */
    public Date calculateActualDeadline(TaskPublish task) {
        return calculateActualDeadline(task, new Date());
    }

    /**
     * 批量计算实际截止时间
     *
     * @param taskList 任务列表
     * @return 包含实际截止时间的任务列表
     */
    public List<TaskPublish> calculateActualDeadlines(List<TaskPublish> taskList) {
        if (CollectionUtils.isEmpty(taskList)) {
            return taskList;
        }

        for (TaskPublish task : taskList) {
            // 计算并设置实际截止时间
            calculateActualDeadline(task);
        }

        return taskList;
    }

    /**
     * 验证任务时间逻辑
     *
     * @param task 任务对象
     * @return 验证错误信息列表，空列表表示验证通过
     */
    public List<String> validateTaskTimeLogic(TaskPublish task) {
        List<String> errors = new ArrayList<>();

        if (task == null) {
            errors.add("任务对象不能为空");
            return errors;
        }

        // 验证截止时间
        if (task.getDeadline() == null) {
            errors.add("截止时间不能为空");
        } else if (task.getDeadline().before(new Date())) {
            errors.add("截止时间不能早于当前时间");
        }

        // 验证任务类型
        if (task.getTaskType() == null || (!task.getTaskType().equals("1") && !task.getTaskType().equals("2"))) {
            errors.add("任务类型必须为1（短期任务）或2（长期任务）");
        }

        // 验证长期任务的天数
        if ("2".equals(task.getTaskType())) {
            if (task.getDaysToComplete() == null) {
                errors.add("长期任务必须设置完成天数");
            } else if (task.getDaysToComplete() <= 0) {
                errors.add("完成天数必须大于0");
            } else if (task.getDaysToComplete() > 365) {
                errors.add("完成天数不能超过365天");
            }
        }

        return errors;
    }

    /**
     * 计算显示用的截止时间
     * 与C端逻辑保持完全一致
     *
     * @param task 任务对象
     * @return 计算后的显示截止时间
     */
    public Date calculateDisplayDeadline(TaskPublish task) {
        if (task == null || task.getDeadline() == null) {
            return new Date();
        }

        // 短期任务或没有天数限制：直接使用原始deadline
        // 向后兼容：taskType为null/undefined时当作短期任务处理
        if (task.getTaskType() == null || "1".equals(task.getTaskType()) ||
            task.getDaysToComplete() == null || task.getDaysToComplete() <= 0) {
            return task.getDeadline();
        }

        // 长期任务：计算从当前时间开始的截止时间
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, task.getDaysToComplete());
        Date deadlineFromNow = cal.getTime();

        // 返回较早的时间
        return task.getDeadline().before(deadlineFromNow) ? task.getDeadline() : deadlineFromNow;
    }

    /**
     * 格式化截止时间为显示字符串
     *
     * @param deadline 截止时间
     * @return 格式化后的时间字符串
     */
    public String formatDeadlineForDisplay(Date deadline) {
        if (deadline == null) {
            return "无截止时间";
        }

        try {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(deadline);
        } catch (Exception e) {
            return "时间格式错误";
        }
    }
}
