package org.jeecg.modules.taskPublish.service;

import java.util.Date;
import java.util.List;

import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.vo.TaskPublishVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 任务发布表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface ITaskPublishService extends IService<TaskPublish> {

    /**
     * 批量转换TaskPublish为TaskPublishVO，并填充完成进度信息
     * @param taskPublishList 任务发布列表
     * @return TaskPublishVO列表
     */
    List<TaskPublishVO> convertToVOWithProgress(List<TaskPublish> taskPublishList);

    /**
     * 计算实际截止时间（基于接受时间）
     * @param task 任务对象
     * @param acceptTime 接受任务的时间
     * @return 实际截止时间
     */
    Date calculateActualDeadline(TaskPublish task, Date acceptTime);

    /**
     * 计算实际截止时间（使用当前时间）
     * @param task 任务对象
     * @return 实际截止时间
     */
    Date calculateActualDeadline(TaskPublish task);

    /**
     * 验证任务时间逻辑
     * @param task 任务对象
     * @return 验证错误信息列表
     */
    List<String> validateTaskTimeLogic(TaskPublish task);
}
