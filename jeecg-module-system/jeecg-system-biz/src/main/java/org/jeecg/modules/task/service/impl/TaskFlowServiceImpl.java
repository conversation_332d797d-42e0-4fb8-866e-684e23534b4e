package org.jeecg.modules.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.task.service.*;
import org.jeecg.modules.taskAcceptanceRecord.entity.TaskAcceptanceRecord;
import org.jeecg.modules.taskAcceptanceRecord.service.ITaskAcceptanceRecordService;
import org.jeecg.modules.taskPublish.entity.TaskPublish;
import org.jeecg.modules.taskPublish.service.ITaskPublishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Description: 任务流程控制服务实现
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Slf4j
@Service
public class TaskFlowServiceImpl implements TaskFlowService {

    @Autowired
    private TaskBusinessService taskBusinessService;

    @Autowired
    private TaskValidationService taskValidationService;

    @Autowired
    private TaskConcurrencyService taskConcurrencyService;

    @Autowired
    private ITaskPublishService taskPublishService;

    @Autowired
    private ITaskAcceptanceRecordService taskAcceptanceRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskFlowResult publishTaskFlow(TaskPublish taskPublish) {
        log.info("开始任务发布流程，发布者ID：{}，任务标题：{}", taskPublish.getPublisherId(), taskPublish.getTitle());

        try {
            // 1. 参数验证
            String validationResult = taskValidationService.validateTaskPublish(taskPublish);
            if (validationResult != null) {
                return TaskFlowResult.failure(validationResult, "VALIDATION_ERROR");
            }

            // 2. 获取发布者锁，防止重复发布
        //    String lockKey = taskPublish.getPublisherId() + ":publish";
        //    if (!taskConcurrencyService.acquireUserLock(taskPublish.getPublisherId(), "publish", 30)) {
        //        return TaskFlowResult.failure("操作过于频繁，请稍后再试", "LOCK_FAILED");
        //    }

            try {
                // 3. 防重复提交检查
                if (!taskConcurrencyService.checkDuplicateSubmission(
                        taskPublish.getPublisherId(), 
                        taskPublish.getTitle(), 
                        "publish", 
                        30)) {
                    return TaskFlowResult.failure("请勿重复提交", "DUPLICATE_SUBMISSION");
                }

                // 4. 执行发布业务逻辑
                boolean publishResult = taskBusinessService.publishTask(taskPublish);
                if (!publishResult) {
                    return TaskFlowResult.failure("任务发布失败", "PUBLISH_FAILED");
                }

                log.info("任务发布流程成功，任务ID：{}", taskPublish.getId());
                return TaskFlowResult.success("任务发布成功", taskPublish);

            } finally {
                // 释放锁
            //    taskConcurrencyService.releaseUserLock(taskPublish.getPublisherId(), "publish");
            }

        } catch (Exception e) {
            log.error("任务发布流程异常，发布者ID：{}，异常信息：{}", taskPublish.getPublisherId(), e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskFlowResult acceptTaskFlow(String taskId, String acceptorId, Integer acceptCount) {
        log.info("开始任务接受流程，任务ID：{}，接受者ID：{}，接受数量：{}", taskId, acceptorId, acceptCount);

        try {
            // 1. 参数验证
            String validationResult = taskValidationService.validateTaskAcceptance(taskId, acceptorId, acceptCount);
            if (validationResult != null) {
                return TaskFlowResult.failure(validationResult, "VALIDATION_ERROR");
            }

            // 2. 获取任务锁，防止并发冲突
            if (!taskConcurrencyService.acquireTaskLock(taskId, 30)) {
                return TaskFlowResult.failure("任务正在被其他用户操作，请稍后再试", "LOCK_FAILED");
            }

            try {
                // 3. 获取用户锁，防止重复接受
                if (!taskConcurrencyService.acquireUserLock(acceptorId, "accept", 30)) {
                    return TaskFlowResult.failure("操作过于频繁，请稍后再试", "USER_LOCK_FAILED");
                }

                try {
                    // 4. 防重复提交检查
                    if (!taskConcurrencyService.checkDuplicateSubmission(acceptorId, taskId, "accept", 30)) {
                        return TaskFlowResult.failure("请勿重复提交", "DUPLICATE_SUBMISSION");
                    }

                    // 5. 再次验证任务状态（双重检查）
                    if (!taskValidationService.validateTaskStatus(taskId, "accept")) {
                        return TaskFlowResult.failure("任务状态已变更，无法接受", "STATUS_CHANGED");
                    }

                    // 7. 执行接受业务逻辑
                    boolean acceptResult = taskBusinessService.acceptTask(taskId, acceptorId, acceptCount);
                    if (!acceptResult) {
                        // 回滚剩余数量
                        taskConcurrencyService.atomicIncreaseRemainingCount(taskId, acceptCount);
                        return TaskFlowResult.failure("任务接受失败", "ACCEPT_FAILED");
                    }

                    // 8. 检查并更新任务状态
                    taskConcurrencyService.checkAndUpdateTaskStatus(taskId);

                    log.info("任务接受流程成功，任务ID：{}，接受者ID：{}", taskId, acceptorId);
                    return TaskFlowResult.success("任务接受成功");

                } finally {
                    taskConcurrencyService.releaseUserLock(acceptorId, "accept");
                }

            } finally {
                taskConcurrencyService.releaseTaskLock(taskId);
            }

        } catch (Exception e) {
            log.error("任务接受流程异常，任务ID：{}，接受者ID：{}，异常信息：{}", taskId, acceptorId, e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskFlowResult submitTaskFlow(String recordId, String submitContent, String submitImages) {
        log.info("开始任务提交审核流程，记录ID：{}", recordId);

        try {
            // 1. 获取接受记录
            TaskAcceptanceRecord record = taskAcceptanceRecordService.getById(recordId);
            if (record == null) {
                return TaskFlowResult.failure("接受记录不存在", "RECORD_NOT_FOUND");
            }

            // 2. 验证提交内容
            String validationResult = taskValidationService.validateSubmitContent(submitContent, submitImages);
            if (validationResult != null) {
                return TaskFlowResult.failure(validationResult, "VALIDATION_ERROR");
            }

            // 3. 验证记录状态
            if (!taskValidationService.validateRecordStatus(recordId, "submit")) {
                return TaskFlowResult.failure("记录状态不允许提交", "STATUS_ERROR");
            }

            // 4. 获取用户锁，防止重复提交
            if (!taskConcurrencyService.acquireUserLock(record.getAcceptorId(), "submit", 30)) {
                return TaskFlowResult.failure("操作过于频繁，请稍后再试", "LOCK_FAILED");
            }

            try {
                // 5. 防重复提交检查
                if (!taskConcurrencyService.checkDuplicateSubmission(
                        record.getAcceptorId(), 
                        recordId, 
                        "submit", 
                        30)) {
                    return TaskFlowResult.failure("请勿重复提交", "DUPLICATE_SUBMISSION");
                }

                // 6. 执行提交业务逻辑
                boolean submitResult = taskBusinessService.submitTaskForAudit(recordId, submitContent, submitImages);
                if (!submitResult) {
                    return TaskFlowResult.failure("提交审核失败", "SUBMIT_FAILED");
                }

                log.info("任务提交审核流程成功，记录ID：{}", recordId);
                return TaskFlowResult.success("提交审核成功");

            } finally {
                taskConcurrencyService.releaseUserLock(record.getAcceptorId(), "submit");
            }

        } catch (Exception e) {
            log.error("任务提交审核流程异常，记录ID：{}，异常信息：{}", recordId, e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskFlowResult auditTaskFlow(String recordId, String auditorId, boolean auditResult, String auditReason, String auditOpinion) {
        log.info("开始任务审核流程，记录ID：{}，审核人ID：{}，审核结果：{}", recordId, auditorId, auditResult);

        try {
            // 1. 获取接受记录
            TaskAcceptanceRecord record = taskAcceptanceRecordService.getById(recordId);
            if (record == null) {
                return TaskFlowResult.failure("接受记录不存在", "RECORD_NOT_FOUND");
            }

            // 2. 验证审核权限
            if (!taskValidationService.validateAuditPermission(recordId, auditorId)) {
                return TaskFlowResult.failure("无权限审核该任务", "PERMISSION_DENIED");
            }

            // 3. 验证记录状态
            if (!taskValidationService.validateRecordStatus(recordId, "audit")) {
                return TaskFlowResult.failure("记录状态不允许审核", "STATUS_ERROR");
            }

            // 4. 获取审核锁，防止重复审核
            if (!taskConcurrencyService.acquireUserLock(auditorId, "audit:" + recordId, 30)) {
                return TaskFlowResult.failure("操作过于频繁，请稍后再试", "LOCK_FAILED");
            }

            try {
                // 5. 防重复提交检查
                if (!taskConcurrencyService.checkDuplicateSubmission(auditorId, recordId, "audit", 30)) {
                    return TaskFlowResult.failure("请勿重复提交", "DUPLICATE_SUBMISSION");
                }

                // 6. 执行审核业务逻辑
                boolean auditBusinessResult = taskBusinessService.auditTask(recordId, auditorId, auditResult, auditReason, auditOpinion);
                if (!auditBusinessResult) {
                    return TaskFlowResult.failure("任务审核失败", "AUDIT_FAILED");
                }

                log.info("任务审核流程成功，记录ID：{}，审核结果：{}", recordId, auditResult ? "通过" : "不通过");
                return TaskFlowResult.success("任务审核成功");

            } finally {
                taskConcurrencyService.releaseUserLock(auditorId, "audit:" + recordId);
            }

        } catch (Exception e) {
            log.error("任务审核流程异常，记录ID：{}，审核人ID：{}，异常信息：{}", recordId, auditorId, e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskFlowResult cancelTaskFlow(String taskId, String operatorId, String reason) {
        log.info("开始任务取消流程，任务ID：{}，操作人ID：{}，原因：{}", taskId, operatorId, reason);

        try {
            // 1. 验证用户权限
            if (!taskValidationService.validateUserPermission(taskId, operatorId, "cancel")) {
                return TaskFlowResult.failure("无权限取消该任务", "PERMISSION_DENIED");
            }

            // 2. 验证任务状态
            if (!taskValidationService.validateTaskStatus(taskId, "cancel")) {
                return TaskFlowResult.failure("任务状态不允许取消", "STATUS_ERROR");
            }

            // 3. 获取任务锁
            if (!taskConcurrencyService.acquireTaskLock(taskId, 30)) {
                return TaskFlowResult.failure("任务正在被其他用户操作，请稍后再试", "LOCK_FAILED");
            }

            try {
                // 4. 执行取消业务逻辑
                boolean cancelResult = taskBusinessService.cancelTask(taskId, operatorId, reason);
                if (!cancelResult) {
                    return TaskFlowResult.failure("任务取消失败", "CANCEL_FAILED");
                }

                log.info("任务取消流程成功，任务ID：{}", taskId);
                return TaskFlowResult.success("任务取消成功");

            } finally {
                taskConcurrencyService.releaseTaskLock(taskId);
            }

        } catch (Exception e) {
            log.error("任务取消流程异常，任务ID：{}，操作人ID：{}，异常信息：{}", taskId, operatorId, e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskFlowResult expireTaskFlow(String taskId) {
        log.info("开始任务过期处理流程，任务ID：{}", taskId);

        try {
            // 1. 验证任务是否过期
            if (!taskValidationService.isTaskExpired(taskId)) {
                return TaskFlowResult.failure("任务未过期", "NOT_EXPIRED");
            }

            // 2. 获取任务锁
            if (!taskConcurrencyService.acquireTaskLock(taskId, 30)) {
                return TaskFlowResult.failure("任务正在被其他进程处理", "LOCK_FAILED");
            }

            try {
                // 3. 执行过期处理业务逻辑
                boolean expireResult = taskBusinessService.handleTaskExpired(taskId);
                if (!expireResult) {
                    return TaskFlowResult.failure("任务过期处理失败", "EXPIRE_FAILED");
                }

                log.info("任务过期处理流程成功，任务ID：{}", taskId);
                return TaskFlowResult.success("任务过期处理成功");

            } finally {
                taskConcurrencyService.releaseTaskLock(taskId);
            }

        } catch (Exception e) {
            log.error("任务过期处理流程异常，任务ID：{}，异常信息：{}", taskId, e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    @Override
    public TaskFlowResult batchExpireTasksFlow() {
        log.info("开始批量处理过期任务流程");

        try {
            // 查询所有过期的任务
            LambdaQueryWrapper<TaskPublish> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(TaskPublish::getStatus, "1", "2") // 进行中或已满额
                       .lt(TaskPublish::getDeadline, new Date()) // 截止时间小于当前时间
                       .isNotNull(TaskPublish::getDeadline);

            List<TaskPublish> expiredTasks = taskPublishService.list(queryWrapper);
            
            int successCount = 0;
            int failCount = 0;

            for (TaskPublish task : expiredTasks) {
                TaskFlowResult result = expireTaskFlow(task.getId());
                if (result.isSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                    log.warn("批量处理过期任务失败，任务ID：{}，原因：{}", task.getId(), result.getMessage());
                }
            }

            log.info("批量处理过期任务完成，总数：{}，成功：{}，失败：{}", expiredTasks.size(), successCount, failCount);
            return TaskFlowResult.success("批量处理完成", 
                String.format("总数：%d，成功：%d，失败：%d", expiredTasks.size(), successCount, failCount));

        } catch (Exception e) {
            log.error("批量处理过期任务异常，异常信息：{}", e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    @Override
    public TaskFlowResult syncTaskStatusFlow(String taskId) {
        log.info("开始任务状态同步流程，任务ID：{}", taskId);

        try {
            // 检查并更新任务状态
            boolean syncResult = taskConcurrencyService.checkAndUpdateTaskStatus(taskId);
            if (syncResult) {
                return TaskFlowResult.success("任务状态同步成功");
            } else {
                return TaskFlowResult.failure("任务状态同步失败", "SYNC_FAILED");
            }

        } catch (Exception e) {
            log.error("任务状态同步流程异常，任务ID：{}，异常信息：{}", taskId, e.getMessage(), e);
            return TaskFlowResult.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }
}
