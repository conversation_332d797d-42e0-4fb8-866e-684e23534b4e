package org.jeecg.modules.taskPublish.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 任务发布表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@TableName("task_publish")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="task_publish对象", description="任务发布表")
public class TaskPublish implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**任务标题*/
    @Excel(name = "任务标题", width = 15)
    @ApiModelProperty(value = "任务标题")
    private String title;

    /**任务描述*/
    @Excel(name = "任务描述", width = 15)
    @ApiModelProperty(value = "任务描述")
    private String description;

    /**任务要求和完成标准*/
    @Excel(name = "任务要求和完成标准", width = 15)
    @ApiModelProperty(value = "任务要求和完成标准")
    private String taskRequirements;

    /**任务总数量*/
    @Excel(name = "任务总数量", width = 15)
    @ApiModelProperty(value = "任务总数量")
    private Integer totalCount;

    /**剩余可接数量*/
    @Excel(name = "剩余可接数量", width = 15)
    @ApiModelProperty(value = "剩余可接数量")
    private Integer remainingCount;

    /**单个任务奖励（助力值）*/
    @Excel(name = "单个任务奖励", width = 15)
    @ApiModelProperty(value = "单个任务奖励（助力值）")
    private BigDecimal unitPrice;

    /**截止时间*/
    @Excel(name = "截止时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "截止时间")
    private Date deadline;

    /**发布者ID（关联member_list.id或sys_user.id）*/
    @Excel(name = "发布者ID", width = 15)
    @ApiModelProperty(value = "发布者ID")
    private String publisherId;

    /**发布者类型（字典：publisher_type）1-普通用户 2-平台管理员*/
    @Excel(name = "发布者类型", width = 15, dicCode = "publisher_type")
    @Dict(dicCode = "publisher_type")
    @ApiModelProperty(value = "发布者类型")
    private String publisherType;

    /**是否官方任务（字典：yn）0-否 1-是*/
    @Excel(name = "是否官方任务", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否官方任务")
    private Integer isOfficial;

    /**预扣助力值总额*/
    @Excel(name = "预扣助力值总额", width = 15)
    @ApiModelProperty(value = "预扣助力值总额")
    private BigDecimal deductedBalance;

    /**已发放助力值*/
    @Excel(name = "已发放助力值", width = 15)
    @ApiModelProperty(value = "已发放助力值")
    private BigDecimal paidBalance;

    /**退回助力值*/
    @Excel(name = "退回助力值", width = 15)
    @ApiModelProperty(value = "退回助力值")
    private BigDecimal refundBalance;

    /**任务状态（字典：task_status）1-进行中 2-已满额 3-已完成 4-已过期 5-已下架*/
    @Excel(name = "任务状态", width = 15, dicCode = "task_status")
    @Dict(dicCode = "task_status")
    @ApiModelProperty(value = "任务状态")
    private String status;

    /**优先级（字典：task_priority）0-普通 1-高 2-紧急*/
    @Excel(name = "优先级", width = 15, dicCode = "task_priority")
    @Dict(dicCode = "task_priority")
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    /**任务类型（字典：task_publish_type）1-短期任务 2-长期任务*/
    @Excel(name = "任务类型", width = 15, dicCode = "task_publish_type")
    @Dict(dicCode = "task_publish_type")
    @ApiModelProperty(value = "任务类型：1-短期任务，2-长期任务")
    private String taskType;

    /**几天内完成（天数）*/
    @Excel(name = "几天内完成", width = 15)
    @ApiModelProperty(value = "几天内完成（天数）")
    private Integer daysToComplete;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志 0-正常 1-删除*/
    @TableLogic
    @ApiModelProperty(value = "删除标志")
    private Integer delFlag;
}
