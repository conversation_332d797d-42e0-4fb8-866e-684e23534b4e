package org.jeecg.modules.taskPublish.vo;

import org.jeecg.modules.taskPublish.entity.TaskPublish;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 任务发布VO类 - 扩展任务进度信息
 * @Author: jeecg-boot
 * @Date: 2025-01-04
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value="TaskPublishVO对象", description="任务发布VO类 - 包含进度统计信息")
public class TaskPublishVO extends TaskPublish {
    
    /**已完成数量（非数据库字段，动态计算）*/
    @ApiModelProperty(value = "已完成数量")
    private Integer completedCount;
    
    /**完成进度百分比（非数据库字段，动态计算）*/
    @ApiModelProperty(value = "完成进度百分比")
    private Integer completedPercent;
    
    /**是否即将截止（非数据库字段，动态计算）*/
    @ApiModelProperty(value = "是否即将截止")
    private Boolean isNearDeadline;

    /**显示用的截止时间（非数据库字段，动态计算）*/
    @ApiModelProperty(value = "显示用的截止时间，格式化后的字符串")
    private String displayDeadline;

    /**实际截止时间（非数据库字段，动态计算）*/
    @ApiModelProperty(value = "实际截止时间，根据任务类型计算后的Date对象")
    private java.util.Date actualDeadline;

    /**
     * 计算完成进度百分比
     */
    public void calculateCompletedPercent() {
        if (this.getTotalCount() != null && this.getTotalCount() > 0 && this.completedCount != null) {
            this.completedPercent = Math.round((float) this.completedCount / this.getTotalCount() * 100);
        } else {
            this.completedPercent = 0;
        }
    }

    /**
     * 计算是否即将截止（24小时内）
     * 基于实际截止时间计算
     */
    public void calculateNearDeadline() {
        if (this.actualDeadline != null) {
            long diff = this.actualDeadline.getTime() - System.currentTimeMillis();
            this.isNearDeadline = diff > 0 && diff <= 24 * 60 * 60 * 1000; // 24小时内
        } else if (this.getDeadline() != null) {
            // 向后兼容：如果没有actualDeadline，使用原始deadline
            long diff = this.getDeadline().getTime() - System.currentTimeMillis();
            this.isNearDeadline = diff > 0 && diff <= 24 * 60 * 60 * 1000; // 24小时内
        } else {
            this.isNearDeadline = false;
        }
    }
}
