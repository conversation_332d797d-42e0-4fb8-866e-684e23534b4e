package org.jeecg.modules.taskAcceptanceRecord.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 任务接受记录表
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@TableName("task_acceptance_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="task_acceptance_record对象", description="任务接受记录表")
public class TaskAcceptanceRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**任务ID（关联task_publish.id）*/
    @Excel(name = "任务ID", width = 15)
    @ApiModelProperty(value = "任务ID")
    private String taskId;

    /**接受者ID（关联member_list.id）*/
    @Excel(name = "接受者ID", width = 15)
    @ApiModelProperty(value = "接受者ID")
    private String acceptorId;

    /**接受数量*/
    @Excel(name = "接受数量", width = 15)
    @ApiModelProperty(value = "接受数量")
    private Integer acceptCount;

    /**单价（冗余字段，防止任务修改影响）*/
    @Excel(name = "单价", width = 15)
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**总奖励金额*/
    @Excel(name = "总奖励金额", width = 15)
    @ApiModelProperty(value = "总奖励金额")
    private BigDecimal totalReward;

    /**状态（字典：acceptance_status）1-进行中 2-待审核 3-已完成 4-未通过 5-已过期 6-已取消*/
    @Excel(name = "状态", width = 15, dicCode = "acceptance_status")
    @Dict(dicCode = "acceptance_status")
    @ApiModelProperty(value = "状态")
    private String status;

    /**接受时间*/
    @Excel(name = "接受时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "接受时间")
    private Date acceptTime;

    /**实际截止时间*/
    @Excel(name = "实际截止时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "实际截止时间（根据接受时间和任务类型计算）")
    private Date actualDeadline;

    /**完成说明*/
    @Excel(name = "完成说明", width = 15)
    @ApiModelProperty(value = "完成说明")
    private String submitContent;

    /**证明材料图片URLs，逗号分隔*/
    @Excel(name = "证明材料", width = 15)
    @ApiModelProperty(value = "证明材料图片URLs")
    private String submitImages;

    /**提交审核时间*/
    @Excel(name = "提交审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "提交审核时间")
    private Date submitTime;

    /**审核时间*/
    @Excel(name = "审核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    /**审核人ID（普通任务：关联member_list.id；官方任务：关联sys_user.id）*/
    @Excel(name = "审核人ID", width = 15)
    @ApiModelProperty(value = "审核人ID")
    private String auditorId;

    /**审核理由（不通过原因）*/
    @Excel(name = "审核理由", width = 15)
    @ApiModelProperty(value = "审核理由")
    private String auditReason;

    /**审核意见（通过时的意见）*/
    @Excel(name = "审核意见", width = 15)
    @ApiModelProperty(value = "审核意见")
    private String auditOpinion;

    /**是否自动审核通过（字典：yn）0-否 1-是*/
    @Excel(name = "是否自动审核", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @ApiModelProperty(value = "是否自动审核通过")
    private Integer autoAuditFlag;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志 0-正常 1-删除*/
    @TableLogic
    @ApiModelProperty(value = "删除标志")
    private Integer delFlag;
}
