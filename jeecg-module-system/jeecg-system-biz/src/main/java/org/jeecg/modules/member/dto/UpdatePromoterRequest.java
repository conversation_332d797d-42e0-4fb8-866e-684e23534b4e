package org.jeecg.modules.member.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 修改推荐人请求参数
 * @Author: heartful-mall
 * @Date: 2025-01-07
 * @Version: V1.0
 */
@Data
@ApiModel(value="UpdatePromoterRequest", description="修改推荐人请求参数")
public class UpdatePromoterRequest {
    
    @ApiModelProperty(value = "会员ID", required = true)
    private String memberId;
    
    @ApiModelProperty(value = "新推荐人ID", required = true)
    private String newPromoterId;
    
    @ApiModelProperty(value = "修改原因")
    private String reason;
}
