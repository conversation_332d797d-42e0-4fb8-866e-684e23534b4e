<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.taskAcceptanceRecord.mapper.TaskAcceptanceRecordMapper">

    <!-- 分页查询包含会员信息的任务接受记录 -->
    <select id="queryPageListWithMemberInfo" resultType="org.jeecg.modules.taskAcceptanceRecord.vo.TaskAcceptanceRecordVO">
        SELECT
            tar.*,
            ml.nick_name as acceptorNickName,
            ml.phone as acceptorPhone,
            ml.head_portrait as acceptorHeadPortrait
        FROM
            task_acceptance_record tar
        LEFT JOIN
            member_list ml ON tar.acceptor_id = ml.id AND ml.del_flag = '0'
        WHERE
            tar.del_flag = '0'
        ${ew.customSqlSegment}
        ORDER BY
            tar.create_time DESC
    </select>

</mapper>
